{"version": 3, "file": "workbox-offline-ga.dev.js", "sources": ["../_version.js", "../utils/constants.js", "../initialize.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:google-analytics:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const QUEUE_NAME = 'workbox-google-analytics';\nexport const MAX_RETENTION_TIME = 60 * 48; // Two days in minutes\nexport const GOOGLE_ANALYTICS_HOST = 'www.google-analytics.com';\nexport const GTM_HOST = 'www.googletagmanager.com';\nexport const ANALYTICS_JS_PATH = '/analytics.js';\nexport const GTAG_JS_PATH = '/gtag/js';\nexport const GTM_JS_PATH = '/gtm.js';\nexport const COLLECT_DEFAULT_PATH = '/collect';\n// This RegExp matches all known Measurement Protocol single-hit collect\n// endpoints. Most of the time the default path (/collect) is used, but\n// occasionally an experimental endpoint is used when testing new features,\n// (e.g. /r/collect or /j/collect)\nexport const COLLECT_PATHS_REGEX = /^\\/(\\w+\\/)?collect/;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { BackgroundSyncPlugin } from 'workbox-background-sync/BackgroundSyncPlugin.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { Router } from 'workbox-routing/Router.js';\nimport { NetworkFirst } from 'workbox-strategies/NetworkFirst.js';\nimport { NetworkOnly } from 'workbox-strategies/NetworkOnly.js';\nimport { QUEUE_NAME, MAX_RETENTION_TIME, GOOGLE_ANALYTICS_HOST, GTM_HOST, ANALYTICS_JS_PATH, GTAG_JS_PATH, GTM_JS_PATH, COLLECT_PATHS_REGEX, } from './utils/constants.js';\nimport './_version.js';\n/**\n * Creates the requestWillDequeue callback to be used with the background\n * sync plugin. The callback takes the failed request and adds the\n * `qt` param based on the current time, as well as applies any other\n * user-defined hit modifications.\n *\n * @param {Object} config See {@link workbox-google-analytics.initialize}.\n * @return {Function} The requestWillDequeue callback function.\n *\n * @private\n */\nconst createOnSyncCallback = (config) => {\n    return async ({ queue }) => {\n        let entry;\n        while ((entry = await queue.shiftRequest())) {\n            const { request, timestamp } = entry;\n            const url = new URL(request.url);\n            try {\n                // Measurement protocol requests can set their payload parameters in\n                // either the URL query string (for GET requests) or the POST body.\n                const params = request.method === 'POST'\n                    ? new URLSearchParams(await request.clone().text())\n                    : url.searchParams;\n                // Calculate the qt param, accounting for the fact that an existing\n                // qt param may be present and should be updated rather than replaced.\n                const originalHitTime = timestamp - (Number(params.get('qt')) || 0);\n                const queueTime = Date.now() - originalHitTime;\n                // Set the qt param prior to applying hitFilter or parameterOverrides.\n                params.set('qt', String(queueTime));\n                // Apply `parameterOverrides`, if set.\n                if (config.parameterOverrides) {\n                    for (const param of Object.keys(config.parameterOverrides)) {\n                        const value = config.parameterOverrides[param];\n                        params.set(param, value);\n                    }\n                }\n                // Apply `hitFilter`, if set.\n                if (typeof config.hitFilter === 'function') {\n                    config.hitFilter.call(null, params);\n                }\n                // Retry the fetch. Ignore URL search params from the URL as they're\n                // now in the post body.\n                await fetch(new Request(url.origin + url.pathname, {\n                    body: params.toString(),\n                    method: 'POST',\n                    mode: 'cors',\n                    credentials: 'omit',\n                    headers: { 'Content-Type': 'text/plain' },\n                }));\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(url.href)}' ` + `has been replayed`);\n                }\n            }\n            catch (err) {\n                await queue.unshiftRequest(entry);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(url.href)}' ` +\n                        `failed to replay, putting it back in the queue.`);\n                }\n                throw err;\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`All Google Analytics request successfully replayed; ` +\n                `the queue is now empty!`);\n        }\n    };\n};\n/**\n * Creates GET and POST routes to catch failed Measurement Protocol hits.\n *\n * @param {BackgroundSyncPlugin} bgSyncPlugin\n * @return {Array<Route>} The created routes.\n *\n * @private\n */\nconst createCollectRoutes = (bgSyncPlugin) => {\n    const match = ({ url }) => url.hostname === GOOGLE_ANALYTICS_HOST &&\n        COLLECT_PATHS_REGEX.test(url.pathname);\n    const handler = new NetworkOnly({\n        plugins: [bgSyncPlugin],\n    });\n    return [new Route(match, handler, 'GET'), new Route(match, handler, 'POST')];\n};\n/**\n * Creates a route with a network first strategy for the analytics.js script.\n *\n * @param {string} cacheName\n * @return {Route} The created route.\n *\n * @private\n */\nconst createAnalyticsJsRoute = (cacheName) => {\n    const match = ({ url }) => url.hostname === GOOGLE_ANALYTICS_HOST &&\n        url.pathname === ANALYTICS_JS_PATH;\n    const handler = new NetworkFirst({ cacheName });\n    return new Route(match, handler, 'GET');\n};\n/**\n * Creates a route with a network first strategy for the gtag.js script.\n *\n * @param {string} cacheName\n * @return {Route} The created route.\n *\n * @private\n */\nconst createGtagJsRoute = (cacheName) => {\n    const match = ({ url }) => url.hostname === GTM_HOST && url.pathname === GTAG_JS_PATH;\n    const handler = new NetworkFirst({ cacheName });\n    return new Route(match, handler, 'GET');\n};\n/**\n * Creates a route with a network first strategy for the gtm.js script.\n *\n * @param {string} cacheName\n * @return {Route} The created route.\n *\n * @private\n */\nconst createGtmJsRoute = (cacheName) => {\n    const match = ({ url }) => url.hostname === GTM_HOST && url.pathname === GTM_JS_PATH;\n    const handler = new NetworkFirst({ cacheName });\n    return new Route(match, handler, 'GET');\n};\n/**\n * @param {Object=} [options]\n * @param {Object} [options.cacheName] The cache name to store and retrieve\n *     analytics.js. Defaults to the cache names provided by `workbox-core`.\n * @param {Object} [options.parameterOverrides]\n *     [Measurement Protocol parameters](https://developers.google.com/analytics/devguides/collection/protocol/v1/parameters),\n *     expressed as key/value pairs, to be added to replayed Google Analytics\n *     requests. This can be used to, e.g., set a custom dimension indicating\n *     that the request was replayed.\n * @param {Function} [options.hitFilter] A function that allows you to modify\n *     the hit parameters prior to replaying\n *     the hit. The function is invoked with the original hit's URLSearchParams\n *     object as its only argument.\n *\n * @memberof workbox-google-analytics\n */\nconst initialize = (options = {}) => {\n    const cacheName = cacheNames.getGoogleAnalyticsName(options.cacheName);\n    const bgSyncPlugin = new BackgroundSyncPlugin(QUEUE_NAME, {\n        maxRetentionTime: MAX_RETENTION_TIME,\n        onSync: createOnSyncCallback(options),\n    });\n    const routes = [\n        createGtmJsRoute(cacheName),\n        createAnalyticsJsRoute(cacheName),\n        createGtagJsRoute(cacheName),\n        ...createCollectRoutes(bgSyncPlugin),\n    ];\n    const router = new Router();\n    for (const route of routes) {\n        router.registerRoute(route);\n    }\n    router.addFetchListener();\n};\nexport { initialize };\n"], "names": ["self", "_", "e", "QUEUE_NAME", "MAX_RETENTION_TIME", "GOOGLE_ANALYTICS_HOST", "GTM_HOST", "ANALYTICS_JS_PATH", "GTAG_JS_PATH", "GTM_JS_PATH", "COLLECT_PATHS_REGEX", "createOnSyncCallback", "config", "queue", "entry", "shiftRequest", "request", "timestamp", "url", "URL", "params", "method", "URLSearchParams", "clone", "text", "searchParams", "originalHitTime", "Number", "get", "queueTime", "Date", "now", "set", "String", "parameterOverrides", "param", "Object", "keys", "value", "hitFilter", "call", "fetch", "Request", "origin", "pathname", "body", "toString", "mode", "credentials", "headers", "process", "logger", "log", "getFriendlyURL", "href", "err", "unshiftRequest", "createCollectRoutes", "bgSyncPlugin", "match", "hostname", "test", "handler", "NetworkOnly", "plugins", "Route", "createAnalyticsJsRoute", "cacheName", "NetworkFirst", "createGtagJsRoute", "createGtmJsRoute", "initialize", "options", "cacheNames", "getGoogleAnalyticsName", "BackgroundSyncPlugin", "maxRetentionTime", "onSync", "routes", "router", "Router", "route", "registerRoute", "addFetchListener"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,gCAAgC,CAAC,IAAIC,CAAC,EAAE,CAAA;IACjD,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAMC,UAAU,GAAG,0BAA0B,CAAA;IAC7C,MAAMC,kBAAkB,GAAG,EAAE,GAAG,EAAE,CAAC;IACnC,MAAMC,qBAAqB,GAAG,0BAA0B,CAAA;IACxD,MAAMC,QAAQ,GAAG,0BAA0B,CAAA;IAC3C,MAAMC,iBAAiB,GAAG,eAAe,CAAA;IACzC,MAAMC,YAAY,GAAG,UAAU,CAAA;IAC/B,MAAMC,WAAW,GAAG,SAAS,CAAA;IAEpC;IACA;IACA;IACA;IACO,MAAMC,mBAAmB,GAAG,oBAAoB;;ICpBvD;IACA;AACA;IACA;IACA;IACA;IACA;IAWA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,oBAAoB,GAAIC,MAAM,IAAK;IACrC,EAAA,OAAO,OAAO;IAAEC,IAAAA,KAAAA;IAAM,GAAC,KAAK;IACxB,IAAA,IAAIC,KAAK,CAAA;QACT,OAAQA,KAAK,GAAG,MAAMD,KAAK,CAACE,YAAY,EAAE,EAAG;UACzC,MAAM;YAAEC,OAAO;IAAEC,QAAAA,SAAAA;IAAU,OAAC,GAAGH,KAAK,CAAA;UACpC,MAAMI,GAAG,GAAG,IAAIC,GAAG,CAACH,OAAO,CAACE,GAAG,CAAC,CAAA;UAChC,IAAI;IACA;IACA;YACA,MAAME,MAAM,GAAGJ,OAAO,CAACK,MAAM,KAAK,MAAM,GAClC,IAAIC,eAAe,CAAC,MAAMN,OAAO,CAACO,KAAK,EAAE,CAACC,IAAI,EAAE,CAAC,GACjDN,GAAG,CAACO,YAAY,CAAA;IACtB;IACA;IACA,QAAA,MAAMC,eAAe,GAAGT,SAAS,IAAIU,MAAM,CAACP,MAAM,CAACQ,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;YACnE,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAGL,eAAe,CAAA;IAC9C;YACAN,MAAM,CAACY,GAAG,CAAC,IAAI,EAAEC,MAAM,CAACJ,SAAS,CAAC,CAAC,CAAA;IACnC;YACA,IAAIjB,MAAM,CAACsB,kBAAkB,EAAE;cAC3B,KAAK,MAAMC,KAAK,IAAIC,MAAM,CAACC,IAAI,CAACzB,MAAM,CAACsB,kBAAkB,CAAC,EAAE;IACxD,YAAA,MAAMI,KAAK,GAAG1B,MAAM,CAACsB,kBAAkB,CAACC,KAAK,CAAC,CAAA;IAC9Cf,YAAAA,MAAM,CAACY,GAAG,CAACG,KAAK,EAAEG,KAAK,CAAC,CAAA;IAC5B,WAAA;IACJ,SAAA;IACA;IACA,QAAA,IAAI,OAAO1B,MAAM,CAAC2B,SAAS,KAAK,UAAU,EAAE;cACxC3B,MAAM,CAAC2B,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEpB,MAAM,CAAC,CAAA;IACvC,SAAA;IACA;IACA;IACA,QAAA,MAAMqB,KAAK,CAAC,IAAIC,OAAO,CAACxB,GAAG,CAACyB,MAAM,GAAGzB,GAAG,CAAC0B,QAAQ,EAAE;IAC/CC,UAAAA,IAAI,EAAEzB,MAAM,CAAC0B,QAAQ,EAAE;IACvBzB,UAAAA,MAAM,EAAE,MAAM;IACd0B,UAAAA,IAAI,EAAE,MAAM;IACZC,UAAAA,WAAW,EAAE,MAAM;IACnBC,UAAAA,OAAO,EAAE;IAAE,YAAA,cAAc,EAAE,YAAA;IAAa,WAAA;IAC5C,SAAC,CAAC,CAAC,CAAA;IACH,QAAA,IAAIC,KAAoB,KAAK,YAAY,EAAE;IACvCC,UAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,aAAA,EAAeC,gCAAc,CAACnC,GAAG,CAACoC,IAAI,CAAE,CAAG,EAAA,CAAA,GAAI,mBAAkB,CAAC,CAAA;IAClF,SAAA;WACH,CACD,OAAOC,GAAG,EAAE;IACR,QAAA,MAAM1C,KAAK,CAAC2C,cAAc,CAAC1C,KAAK,CAAC,CAAA;IACjC,QAA2C;IACvCqC,UAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,aAAA,EAAeC,gCAAc,CAACnC,GAAG,CAACoC,IAAI,CAAE,CAAG,EAAA,CAAA,GAClD,iDAAgD,CAAC,CAAA;IAC1D,SAAA;IACA,QAAA,MAAMC,GAAG,CAAA;IACb,OAAA;IACJ,KAAA;IACA,IAA2C;IACvCJ,MAAAA,gBAAM,CAACC,GAAG,CAAE,CAAqD,oDAAA,CAAA,GAC5D,yBAAwB,CAAC,CAAA;IAClC,KAAA;OACH,CAAA;IACL,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMK,mBAAmB,GAAIC,YAAY,IAAK;MAC1C,MAAMC,KAAK,GAAGA,CAAC;IAAEzC,IAAAA,GAAAA;IAAI,GAAC,KAAKA,GAAG,CAAC0C,QAAQ,KAAKvD,qBAAqB,IAC7DK,mBAAmB,CAACmD,IAAI,CAAC3C,GAAG,CAAC0B,QAAQ,CAAC,CAAA;IAC1C,EAAA,MAAMkB,OAAO,GAAG,IAAIC,0BAAW,CAAC;QAC5BC,OAAO,EAAE,CAACN,YAAY,CAAA;IAC1B,GAAC,CAAC,CAAA;MACF,OAAO,CAAC,IAAIO,cAAK,CAACN,KAAK,EAAEG,OAAO,EAAE,KAAK,CAAC,EAAE,IAAIG,cAAK,CAACN,KAAK,EAAEG,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;IAChF,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMI,sBAAsB,GAAIC,SAAS,IAAK;MAC1C,MAAMR,KAAK,GAAGA,CAAC;IAAEzC,IAAAA,GAAAA;OAAK,KAAKA,GAAG,CAAC0C,QAAQ,KAAKvD,qBAAqB,IAC7Da,GAAG,CAAC0B,QAAQ,KAAKrC,iBAAiB,CAAA;IACtC,EAAA,MAAMuD,OAAO,GAAG,IAAIM,4BAAY,CAAC;IAAED,IAAAA,SAAAA;IAAU,GAAC,CAAC,CAAA;MAC/C,OAAO,IAAIF,cAAK,CAACN,KAAK,EAAEG,OAAO,EAAE,KAAK,CAAC,CAAA;IAC3C,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMO,iBAAiB,GAAIF,SAAS,IAAK;MACrC,MAAMR,KAAK,GAAGA,CAAC;IAAEzC,IAAAA,GAAAA;OAAK,KAAKA,GAAG,CAAC0C,QAAQ,KAAKtD,QAAQ,IAAIY,GAAG,CAAC0B,QAAQ,KAAKpC,YAAY,CAAA;IACrF,EAAA,MAAMsD,OAAO,GAAG,IAAIM,4BAAY,CAAC;IAAED,IAAAA,SAAAA;IAAU,GAAC,CAAC,CAAA;MAC/C,OAAO,IAAIF,cAAK,CAACN,KAAK,EAAEG,OAAO,EAAE,KAAK,CAAC,CAAA;IAC3C,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMQ,gBAAgB,GAAIH,SAAS,IAAK;MACpC,MAAMR,KAAK,GAAGA,CAAC;IAAEzC,IAAAA,GAAAA;OAAK,KAAKA,GAAG,CAAC0C,QAAQ,KAAKtD,QAAQ,IAAIY,GAAG,CAAC0B,QAAQ,KAAKnC,WAAW,CAAA;IACpF,EAAA,MAAMqD,OAAO,GAAG,IAAIM,4BAAY,CAAC;IAAED,IAAAA,SAAAA;IAAU,GAAC,CAAC,CAAA;MAC/C,OAAO,IAAIF,cAAK,CAACN,KAAK,EAAEG,OAAO,EAAE,KAAK,CAAC,CAAA;IAC3C,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA,UAAMS,UAAU,GAAGA,CAACC,OAAO,GAAG,EAAE,KAAK;MACjC,MAAML,SAAS,GAAGM,wBAAU,CAACC,sBAAsB,CAACF,OAAO,CAACL,SAAS,CAAC,CAAA;IACtE,EAAA,MAAMT,YAAY,GAAG,IAAIiB,4CAAoB,CAACxE,UAAU,EAAE;IACtDyE,IAAAA,gBAAgB,EAAExE,kBAAkB;QACpCyE,MAAM,EAAElE,oBAAoB,CAAC6D,OAAO,CAAA;IACxC,GAAC,CAAC,CAAA;MACF,MAAMM,MAAM,GAAG,CACXR,gBAAgB,CAACH,SAAS,CAAC,EAC3BD,sBAAsB,CAACC,SAAS,CAAC,EACjCE,iBAAiB,CAACF,SAAS,CAAC,EAC5B,GAAGV,mBAAmB,CAACC,YAAY,CAAC,CACvC,CAAA;IACD,EAAA,MAAMqB,MAAM,GAAG,IAAIC,gBAAM,EAAE,CAAA;IAC3B,EAAA,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IACxBC,IAAAA,MAAM,CAACG,aAAa,CAACD,KAAK,CAAC,CAAA;IAC/B,GAAA;MACAF,MAAM,CAACI,gBAAgB,EAAE,CAAA;IAC7B;;;;;;;;;;"}
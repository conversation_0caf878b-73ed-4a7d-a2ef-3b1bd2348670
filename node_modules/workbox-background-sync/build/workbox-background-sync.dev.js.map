{"version": 3, "file": "workbox-background-sync.dev.js", "sources": ["../node_modules/idb/build/wrap-idb-value.js", "../node_modules/idb/build/index.js", "../_version.js", "../lib/QueueDb.js", "../lib/QueueStore.js", "../lib/StorableRequest.js", "../Queue.js", "../BackgroundSyncPlugin.js"], "sourcesContent": ["const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction));\n        });\n    }\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking)\n            db.addEventListener('versionchange', () => blocking());\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:background-sync:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2021 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB } from 'idb';\nimport '../_version.js';\nconst DB_VERSION = 3;\nconst DB_NAME = 'workbox-background-sync';\nconst REQUEST_OBJECT_STORE_NAME = 'requests';\nconst QUEUE_NAME_INDEX = 'queueName';\n/**\n * A class to interact directly an IndexedDB created specifically to save and\n * retrieve QueueStoreEntries. This class encapsulates all the schema details\n * to store the representation of a Queue.\n *\n * @private\n */\nexport class QueueDb {\n    constructor() {\n        this._db = null;\n    }\n    /**\n     * Add QueueStoreEntry to underlying db.\n     *\n     * @param {UnidentifiedQueueStoreEntry} entry\n     */\n    async addEntry(entry) {\n        const db = await this.getDb();\n        const tx = db.transaction(REQUEST_OBJECT_STORE_NAME, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.add(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the first entry id in the ObjectStore.\n     *\n     * @return {number | undefined}\n     */\n    async getFirstEntryId() {\n        const db = await this.getDb();\n        const cursor = await db\n            .transaction(REQUEST_OBJECT_STORE_NAME)\n            .store.openCursor();\n        return cursor === null || cursor === void 0 ? void 0 : cursor.value.id;\n    }\n    /**\n     * Get all the entries filtered by index\n     *\n     * @param queueName\n     * @return {Promise<QueueStoreEntry[]>}\n     */\n    async getAllEntriesByQueueName(queueName) {\n        const db = await this.getDb();\n        const results = await db.getAllFromIndex(REQUEST_OBJECT_STORE_NAME, QUEUE_NAME_INDEX, IDBKeyRange.only(queueName));\n        return results ? results : new Array();\n    }\n    /**\n     * Returns the number of entries filtered by index\n     *\n     * @param queueName\n     * @return {Promise<number>}\n     */\n    async getEntryCountByQueueName(queueName) {\n        const db = await this.getDb();\n        return db.countFromIndex(REQUEST_OBJECT_STORE_NAME, QUEUE_NAME_INDEX, IDBKeyRange.only(queueName));\n    }\n    /**\n     * Deletes a single entry by id.\n     *\n     * @param {number} id the id of the entry to be deleted\n     */\n    async deleteEntry(id) {\n        const db = await this.getDb();\n        await db.delete(REQUEST_OBJECT_STORE_NAME, id);\n    }\n    /**\n     *\n     * @param queueName\n     * @returns {Promise<QueueStoreEntry | undefined>}\n     */\n    async getFirstEntryByQueueName(queueName) {\n        return await this.getEndEntryFromIndex(IDBKeyRange.only(queueName), 'next');\n    }\n    /**\n     *\n     * @param queueName\n     * @returns {Promise<QueueStoreEntry | undefined>}\n     */\n    async getLastEntryByQueueName(queueName) {\n        return await this.getEndEntryFromIndex(IDBKeyRange.only(queueName), 'prev');\n    }\n    /**\n     * Returns either the first or the last entries, depending on direction.\n     * Filtered by index.\n     *\n     * @param {IDBCursorDirection} direction\n     * @param {IDBKeyRange} query\n     * @return {Promise<QueueStoreEntry | undefined>}\n     * @private\n     */\n    async getEndEntryFromIndex(query, direction) {\n        const db = await this.getDb();\n        const cursor = await db\n            .transaction(REQUEST_OBJECT_STORE_NAME)\n            .store.index(QUEUE_NAME_INDEX)\n            .openCursor(query, direction);\n        return cursor === null || cursor === void 0 ? void 0 : cursor.value;\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, DB_VERSION, {\n                upgrade: this._upgradeDb,\n            });\n        }\n        return this._db;\n    }\n    /**\n     * Upgrades QueueDB\n     *\n     * @param {IDBPDatabase<QueueDBSchema>} db\n     * @param {number} oldVersion\n     * @private\n     */\n    _upgradeDb(db, oldVersion) {\n        if (oldVersion > 0 && oldVersion < DB_VERSION) {\n            if (db.objectStoreNames.contains(REQUEST_OBJECT_STORE_NAME)) {\n                db.deleteObjectStore(REQUEST_OBJECT_STORE_NAME);\n            }\n        }\n        const objStore = db.createObjectStore(REQUEST_OBJECT_STORE_NAME, {\n            autoIncrement: true,\n            keyPath: 'id',\n        });\n        objStore.createIndex(QUEUE_NAME_INDEX, QUEUE_NAME_INDEX, { unique: false });\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { QueueDb, } from './QueueDb.js';\nimport '../_version.js';\n/**\n * A class to manage storing requests from a Queue in IndexedDB,\n * indexed by their queue name for easier access.\n *\n * Most developers will not need to access this class directly;\n * it is exposed for advanced use cases.\n */\nexport class QueueStore {\n    /**\n     * Associates this instance with a Queue instance, so entries added can be\n     * identified by their queue name.\n     *\n     * @param {string} queueName\n     */\n    constructor(queueName) {\n        this._queueName = queueName;\n        this._queueDb = new QueueDb();\n    }\n    /**\n     * Append an entry last in the queue.\n     *\n     * @param {Object} entry\n     * @param {Object} entry.requestData\n     * @param {number} [entry.timestamp]\n     * @param {Object} [entry.metadata]\n     */\n    async pushEntry(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'pushEntry',\n                paramName: 'entry',\n            });\n            assert.isType(entry.requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'pushEntry',\n                paramName: 'entry.requestData',\n            });\n        }\n        // Don't specify an ID since one is automatically generated.\n        delete entry.id;\n        entry.queueName = this._queueName;\n        await this._queueDb.addEntry(entry);\n    }\n    /**\n     * Prepend an entry first in the queue.\n     *\n     * @param {Object} entry\n     * @param {Object} entry.requestData\n     * @param {number} [entry.timestamp]\n     * @param {Object} [entry.metadata]\n     */\n    async unshiftEntry(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'unshiftEntry',\n                paramName: 'entry',\n            });\n            assert.isType(entry.requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'unshiftEntry',\n                paramName: 'entry.requestData',\n            });\n        }\n        const firstId = await this._queueDb.getFirstEntryId();\n        if (firstId) {\n            // Pick an ID one less than the lowest ID in the object store.\n            entry.id = firstId - 1;\n        }\n        else {\n            // Otherwise let the auto-incrementor assign the ID.\n            delete entry.id;\n        }\n        entry.queueName = this._queueName;\n        await this._queueDb.addEntry(entry);\n    }\n    /**\n     * Removes and returns the last entry in the queue matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     */\n    async popEntry() {\n        return this._removeEntry(await this._queueDb.getLastEntryByQueueName(this._queueName));\n    }\n    /**\n     * Removes and returns the first entry in the queue matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     */\n    async shiftEntry() {\n        return this._removeEntry(await this._queueDb.getFirstEntryByQueueName(this._queueName));\n    }\n    /**\n     * Returns all entries in the store matching the `queueName`.\n     *\n     * @param {Object} options See {@link workbox-background-sync.Queue~getAll}\n     * @return {Promise<Array<Object>>}\n     */\n    async getAll() {\n        return await this._queueDb.getAllEntriesByQueueName(this._queueName);\n    }\n    /**\n     * Returns the number of entries in the store matching the `queueName`.\n     *\n     * @param {Object} options See {@link workbox-background-sync.Queue~size}\n     * @return {Promise<number>}\n     */\n    async size() {\n        return await this._queueDb.getEntryCountByQueueName(this._queueName);\n    }\n    /**\n     * Deletes the entry for the given ID.\n     *\n     * WARNING: this method does not ensure the deleted entry belongs to this\n     * queue (i.e. matches the `queueName`). But this limitation is acceptable\n     * as this class is not publicly exposed. An additional check would make\n     * this method slower than it needs to be.\n     *\n     * @param {number} id\n     */\n    async deleteEntry(id) {\n        await this._queueDb.deleteEntry(id);\n    }\n    /**\n     * Removes and returns the first or last entry in the queue (based on the\n     * `direction` argument) matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     * @private\n     */\n    async _removeEntry(entry) {\n        if (entry) {\n            await this.deleteEntry(entry.id);\n        }\n        return entry;\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\nconst serializableProperties = [\n    'method',\n    'referrer',\n    'referrerPolicy',\n    'mode',\n    'credentials',\n    'cache',\n    'redirect',\n    'integrity',\n    'keepalive',\n];\n/**\n * A class to make it easier to serialize and de-serialize requests so they\n * can be stored in IndexedDB.\n *\n * Most developers will not need to access this class directly;\n * it is exposed for advanced use cases.\n */\nclass StorableRequest {\n    /**\n     * Converts a Request object to a plain object that can be structured\n     * cloned or JSON-stringified.\n     *\n     * @param {Request} request\n     * @return {Promise<StorableRequest>}\n     */\n    static async fromRequest(request) {\n        const requestData = {\n            url: request.url,\n            headers: {},\n        };\n        // Set the body if present.\n        if (request.method !== 'GET') {\n            // Use ArrayBuffer to support non-text request bodies.\n            // NOTE: we can't use Blobs becuse Safari doesn't support storing\n            // Blobs in IndexedDB in some cases:\n            // https://github.com/dfahlander/Dexie.js/issues/618#issuecomment-398348457\n            requestData.body = await request.clone().arrayBuffer();\n        }\n        // Convert the headers from an iterable to an object.\n        for (const [key, value] of request.headers.entries()) {\n            requestData.headers[key] = value;\n        }\n        // Add all other serializable request properties\n        for (const prop of serializableProperties) {\n            if (request[prop] !== undefined) {\n                requestData[prop] = request[prop];\n            }\n        }\n        return new StorableRequest(requestData);\n    }\n    /**\n     * Accepts an object of request data that can be used to construct a\n     * `Request` but can also be stored in IndexedDB.\n     *\n     * @param {Object} requestData An object of request data that includes the\n     *     `url` plus any relevant properties of\n     *     [requestInit]{@link https://fetch.spec.whatwg.org/#requestinit}.\n     */\n    constructor(requestData) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'StorableRequest',\n                funcName: 'constructor',\n                paramName: 'requestData',\n            });\n            assert.isType(requestData.url, 'string', {\n                moduleName: 'workbox-background-sync',\n                className: 'StorableRequest',\n                funcName: 'constructor',\n                paramName: 'requestData.url',\n            });\n        }\n        // If the request's mode is `navigate`, convert it to `same-origin` since\n        // navigation requests can't be constructed via script.\n        if (requestData['mode'] === 'navigate') {\n            requestData['mode'] = 'same-origin';\n        }\n        this._requestData = requestData;\n    }\n    /**\n     * Returns a deep clone of the instances `_requestData` object.\n     *\n     * @return {Object}\n     */\n    toObject() {\n        const requestData = Object.assign({}, this._requestData);\n        requestData.headers = Object.assign({}, this._requestData.headers);\n        if (requestData.body) {\n            requestData.body = requestData.body.slice(0);\n        }\n        return requestData;\n    }\n    /**\n     * Converts this instance to a Request.\n     *\n     * @return {Request}\n     */\n    toRequest() {\n        return new Request(this._requestData.url, this._requestData);\n    }\n    /**\n     * Creates and returns a deep clone of the instance.\n     *\n     * @return {StorableRequest}\n     */\n    clone() {\n        return new StorableRequest(this.toObject());\n    }\n}\nexport { StorableRequest };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { QueueStore } from './lib/QueueStore.js';\nimport { StorableRequest } from './lib/StorableRequest.js';\nimport './_version.js';\nconst TAG_PREFIX = 'workbox-background-sync';\nconst MAX_RETENTION_TIME = 60 * 24 * 7; // 7 days in minutes\nconst queueNames = new Set();\n/**\n * Converts a QueueStore entry into the format exposed by Queue. This entails\n * converting the request data into a real request and omitting the `id` and\n * `queueName` properties.\n *\n * @param {UnidentifiedQueueStoreEntry} queueStoreEntry\n * @return {Queue}\n * @private\n */\nconst convertEntry = (queueStoreEntry) => {\n    const queueEntry = {\n        request: new StorableRequest(queueStoreEntry.requestData).toRequest(),\n        timestamp: queueStoreEntry.timestamp,\n    };\n    if (queueStoreEntry.metadata) {\n        queueEntry.metadata = queueStoreEntry.metadata;\n    }\n    return queueEntry;\n};\n/**\n * A class to manage storing failed requests in IndexedDB and retrying them\n * later. All parts of the storing and replaying process are observable via\n * callbacks.\n *\n * @memberof workbox-background-sync\n */\nclass Queue {\n    /**\n     * Creates an instance of Queue with the given options\n     *\n     * @param {string} name The unique name for this queue. This name must be\n     *     unique as it's used to register sync events and store requests\n     *     in IndexedDB specific to this instance. An error will be thrown if\n     *     a duplicate name is detected.\n     * @param {Object} [options]\n     * @param {Function} [options.onSync] A function that gets invoked whenever\n     *     the 'sync' event fires. The function is invoked with an object\n     *     containing the `queue` property (referencing this instance), and you\n     *     can use the callback to customize the replay behavior of the queue.\n     *     When not set the `replayRequests()` method is called.\n     *     Note: if the replay fails after a sync event, make sure you throw an\n     *     error, so the browser knows to retry the sync event later.\n     * @param {number} [options.maxRetentionTime=7 days] The amount of time (in\n     *     minutes) a request may be retried. After this amount of time has\n     *     passed, the request will be deleted from the queue.\n     * @param {boolean} [options.forceSyncFallback=false] If `true`, instead\n     *     of attempting to use background sync events, always attempt to replay\n     *     queued request at service worker startup. Most folks will not need\n     *     this, unless you explicitly target a runtime like Electron that\n     *     exposes the interfaces for background sync, but does not have a working\n     *     implementation.\n     */\n    constructor(name, { forceSyncFallback, onSync, maxRetentionTime } = {}) {\n        this._syncInProgress = false;\n        this._requestsAddedDuringSync = false;\n        // Ensure the store name is not already being used\n        if (queueNames.has(name)) {\n            throw new WorkboxError('duplicate-queue-name', { name });\n        }\n        else {\n            queueNames.add(name);\n        }\n        this._name = name;\n        this._onSync = onSync || this.replayRequests;\n        this._maxRetentionTime = maxRetentionTime || MAX_RETENTION_TIME;\n        this._forceSyncFallback = Boolean(forceSyncFallback);\n        this._queueStore = new QueueStore(this._name);\n        this._addSyncListener();\n    }\n    /**\n     * @return {string}\n     */\n    get name() {\n        return this._name;\n    }\n    /**\n     * Stores the passed request in IndexedDB (with its timestamp and any\n     * metadata) at the end of the queue.\n     *\n     * @param {QueueEntry} entry\n     * @param {Request} entry.request The request to store in the queue.\n     * @param {Object} [entry.metadata] Any metadata you want associated with the\n     *     stored request. When requests are replayed you'll have access to this\n     *     metadata object in case you need to modify the request beforehand.\n     * @param {number} [entry.timestamp] The timestamp (Epoch time in\n     *     milliseconds) when the request was first added to the queue. This is\n     *     used along with `maxRetentionTime` to remove outdated requests. In\n     *     general you don't need to set this value, as it's automatically set\n     *     for you (defaulting to `Date.now()`), but you can update it if you\n     *     don't want particular requests to expire.\n     */\n    async pushRequest(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'pushRequest',\n                paramName: 'entry',\n            });\n            assert.isInstance(entry.request, Request, {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'pushRequest',\n                paramName: 'entry.request',\n            });\n        }\n        await this._addRequest(entry, 'push');\n    }\n    /**\n     * Stores the passed request in IndexedDB (with its timestamp and any\n     * metadata) at the beginning of the queue.\n     *\n     * @param {QueueEntry} entry\n     * @param {Request} entry.request The request to store in the queue.\n     * @param {Object} [entry.metadata] Any metadata you want associated with the\n     *     stored request. When requests are replayed you'll have access to this\n     *     metadata object in case you need to modify the request beforehand.\n     * @param {number} [entry.timestamp] The timestamp (Epoch time in\n     *     milliseconds) when the request was first added to the queue. This is\n     *     used along with `maxRetentionTime` to remove outdated requests. In\n     *     general you don't need to set this value, as it's automatically set\n     *     for you (defaulting to `Date.now()`), but you can update it if you\n     *     don't want particular requests to expire.\n     */\n    async unshiftRequest(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'unshiftRequest',\n                paramName: 'entry',\n            });\n            assert.isInstance(entry.request, Request, {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'unshiftRequest',\n                paramName: 'entry.request',\n            });\n        }\n        await this._addRequest(entry, 'unshift');\n    }\n    /**\n     * Removes and returns the last request in the queue (along with its\n     * timestamp and any metadata). The returned object takes the form:\n     * `{request, timestamp, metadata}`.\n     *\n     * @return {Promise<QueueEntry | undefined>}\n     */\n    async popRequest() {\n        return this._removeRequest('pop');\n    }\n    /**\n     * Removes and returns the first request in the queue (along with its\n     * timestamp and any metadata). The returned object takes the form:\n     * `{request, timestamp, metadata}`.\n     *\n     * @return {Promise<QueueEntry | undefined>}\n     */\n    async shiftRequest() {\n        return this._removeRequest('shift');\n    }\n    /**\n     * Returns all the entries that have not expired (per `maxRetentionTime`).\n     * Any expired entries are removed from the queue.\n     *\n     * @return {Promise<Array<QueueEntry>>}\n     */\n    async getAll() {\n        const allEntries = await this._queueStore.getAll();\n        const now = Date.now();\n        const unexpiredEntries = [];\n        for (const entry of allEntries) {\n            // Ignore requests older than maxRetentionTime. Call this function\n            // recursively until an unexpired request is found.\n            const maxRetentionTimeInMs = this._maxRetentionTime * 60 * 1000;\n            if (now - entry.timestamp > maxRetentionTimeInMs) {\n                await this._queueStore.deleteEntry(entry.id);\n            }\n            else {\n                unexpiredEntries.push(convertEntry(entry));\n            }\n        }\n        return unexpiredEntries;\n    }\n    /**\n     * Returns the number of entries present in the queue.\n     * Note that expired entries (per `maxRetentionTime`) are also included in this count.\n     *\n     * @return {Promise<number>}\n     */\n    async size() {\n        return await this._queueStore.size();\n    }\n    /**\n     * Adds the entry to the QueueStore and registers for a sync event.\n     *\n     * @param {Object} entry\n     * @param {Request} entry.request\n     * @param {Object} [entry.metadata]\n     * @param {number} [entry.timestamp=Date.now()]\n     * @param {string} operation ('push' or 'unshift')\n     * @private\n     */\n    async _addRequest({ request, metadata, timestamp = Date.now() }, operation) {\n        const storableRequest = await StorableRequest.fromRequest(request.clone());\n        const entry = {\n            requestData: storableRequest.toObject(),\n            timestamp,\n        };\n        // Only include metadata if it's present.\n        if (metadata) {\n            entry.metadata = metadata;\n        }\n        switch (operation) {\n            case 'push':\n                await this._queueStore.pushEntry(entry);\n                break;\n            case 'unshift':\n                await this._queueStore.unshiftEntry(entry);\n                break;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Request for '${getFriendlyURL(request.url)}' has ` +\n                `been added to background sync queue '${this._name}'.`);\n        }\n        // Don't register for a sync if we're in the middle of a sync. Instead,\n        // we wait until the sync is complete and call register if\n        // `this._requestsAddedDuringSync` is true.\n        if (this._syncInProgress) {\n            this._requestsAddedDuringSync = true;\n        }\n        else {\n            await this.registerSync();\n        }\n    }\n    /**\n     * Removes and returns the first or last (depending on `operation`) entry\n     * from the QueueStore that's not older than the `maxRetentionTime`.\n     *\n     * @param {string} operation ('pop' or 'shift')\n     * @return {Object|undefined}\n     * @private\n     */\n    async _removeRequest(operation) {\n        const now = Date.now();\n        let entry;\n        switch (operation) {\n            case 'pop':\n                entry = await this._queueStore.popEntry();\n                break;\n            case 'shift':\n                entry = await this._queueStore.shiftEntry();\n                break;\n        }\n        if (entry) {\n            // Ignore requests older than maxRetentionTime. Call this function\n            // recursively until an unexpired request is found.\n            const maxRetentionTimeInMs = this._maxRetentionTime * 60 * 1000;\n            if (now - entry.timestamp > maxRetentionTimeInMs) {\n                return this._removeRequest(operation);\n            }\n            return convertEntry(entry);\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Loops through each request in the queue and attempts to re-fetch it.\n     * If any request fails to re-fetch, it's put back in the same position in\n     * the queue (which registers a retry for the next sync event).\n     */\n    async replayRequests() {\n        let entry;\n        while ((entry = await this.shiftRequest())) {\n            try {\n                await fetch(entry.request.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(entry.request.url)}' ` +\n                        `has been replayed in queue '${this._name}'`);\n                }\n            }\n            catch (error) {\n                await this.unshiftRequest(entry);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(entry.request.url)}' ` +\n                        `failed to replay, putting it back in queue '${this._name}'`);\n                }\n                throw new WorkboxError('queue-replay-failed', { name: this._name });\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`All requests in queue '${this.name}' have successfully ` +\n                `replayed; the queue is now empty!`);\n        }\n    }\n    /**\n     * Registers a sync event with a tag unique to this instance.\n     */\n    async registerSync() {\n        // See https://github.com/GoogleChrome/workbox/issues/2393\n        if ('sync' in self.registration && !this._forceSyncFallback) {\n            try {\n                await self.registration.sync.register(`${TAG_PREFIX}:${this._name}`);\n            }\n            catch (err) {\n                // This means the registration failed for some reason, possibly due to\n                // the user disabling it.\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn(`Unable to register sync event for '${this._name}'.`, err);\n                }\n            }\n        }\n    }\n    /**\n     * In sync-supporting browsers, this adds a listener for the sync event.\n     * In non-sync-supporting browsers, or if _forceSyncFallback is true, this\n     * will retry the queue on service worker startup.\n     *\n     * @private\n     */\n    _addSyncListener() {\n        // See https://github.com/GoogleChrome/workbox/issues/2393\n        if ('sync' in self.registration && !this._forceSyncFallback) {\n            self.addEventListener('sync', (event) => {\n                if (event.tag === `${TAG_PREFIX}:${this._name}`) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logger.log(`Background sync for tag '${event.tag}' ` + `has been received`);\n                    }\n                    const syncComplete = async () => {\n                        this._syncInProgress = true;\n                        let syncError;\n                        try {\n                            await this._onSync({ queue: this });\n                        }\n                        catch (error) {\n                            if (error instanceof Error) {\n                                syncError = error;\n                                // Rethrow the error. Note: the logic in the finally clause\n                                // will run before this gets rethrown.\n                                throw syncError;\n                            }\n                        }\n                        finally {\n                            // New items may have been added to the queue during the sync,\n                            // so we need to register for a new sync if that's happened...\n                            // Unless there was an error during the sync, in which\n                            // case the browser will automatically retry later, as long\n                            // as `event.lastChance` is not true.\n                            if (this._requestsAddedDuringSync &&\n                                !(syncError && !event.lastChance)) {\n                                await this.registerSync();\n                            }\n                            this._syncInProgress = false;\n                            this._requestsAddedDuringSync = false;\n                        }\n                    };\n                    event.waitUntil(syncComplete());\n                }\n            });\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Background sync replaying without background sync event`);\n            }\n            // If the browser doesn't support background sync, or the developer has\n            // opted-in to not using it, retry every time the service worker starts up\n            // as a fallback.\n            void this._onSync({ queue: this });\n        }\n    }\n    /**\n     * Returns the set of queue names. This is primarily used to reset the list\n     * of queue names in tests.\n     *\n     * @return {Set<string>}\n     *\n     * @private\n     */\n    static get _queueNames() {\n        return queueNames;\n    }\n}\nexport { Queue };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Queue } from './Queue.js';\nimport './_version.js';\n/**\n * A class implementing the `fetchDidFail` lifecycle callback. This makes it\n * easier to add failed requests to a background sync Queue.\n *\n * @memberof workbox-background-sync\n */\nclass BackgroundSyncPlugin {\n    /**\n     * @param {string} name See the {@link workbox-background-sync.Queue}\n     *     documentation for parameter details.\n     * @param {Object} [options] See the\n     *     {@link workbox-background-sync.Queue} documentation for\n     *     parameter details.\n     */\n    constructor(name, options) {\n        /**\n         * @param {Object} options\n         * @param {Request} options.request\n         * @private\n         */\n        this.fetchDidFail = async ({ request }) => {\n            await this._queue.pushRequest({ request });\n        };\n        this._queue = new Queue(name, options);\n    }\n}\nexport { BackgroundSyncPlugin };\n"], "names": ["instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "getIdbProxyableTypes", "IDBDatabase", "IDBObjectStore", "IDBIndex", "IDBCursor", "IDBTransaction", "getCursorAdvanceMethods", "prototype", "advance", "continue", "continuePrimaryKey", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "promisifyRequest", "request", "promise", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "success", "error", "wrap", "result", "addEventListener", "then", "value", "set", "catch", "cacheDonePromiseForTransaction", "tx", "has", "done", "complete", "DOMException", "idbProxyTraps", "get", "target", "prop", "receiver", "objectStoreNames", "undefined", "objectStore", "replaceTraps", "callback", "wrapFunction", "func", "transaction", "storeNames", "args", "call", "unwrap", "sort", "includes", "apply", "transformCachableValue", "Proxy", "IDBRequest", "newValue", "openDB", "name", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "replace", "useIndex", "isWrite", "method", "storeName", "store", "index", "shift", "all", "oldTraps", "_extends", "self", "_", "e", "DB_VERSION", "DB_NAME", "REQUEST_OBJECT_STORE_NAME", "QUEUE_NAME_INDEX", "QueueDb", "constructor", "_db", "addEntry", "entry", "getDb", "durability", "add", "getFirstEntryId", "cursor", "openCursor", "id", "getAllEntriesByQueueName", "queueName", "results", "getAllFromIndex", "IDBKeyRange", "only", "Array", "getEntryCountByQueueName", "countFromIndex", "deleteEntry", "delete", "getFirstEntryByQueueName", "getEndEntryFromIndex", "getLastEntryByQueueName", "query", "direction", "_upgradeDb", "contains", "deleteObjectStore", "objStore", "createObjectStore", "autoIncrement", "keyP<PERSON>", "createIndex", "unique", "QueueStore", "_queueName", "_queueDb", "pushEntry", "assert", "isType", "moduleName", "className", "funcName", "paramName", "requestData", "unshiftEntry", "firstId", "popEntry", "_removeEntry", "shiftEntry", "getAll", "size", "serializableProperties", "StorableRequest", "fromRequest", "url", "headers", "body", "clone", "arrayBuffer", "key", "entries", "_requestData", "toObject", "Object", "assign", "slice", "toRequest", "Request", "TAG_PREFIX", "MAX_RETENTION_TIME", "queueNames", "Set", "convertEntry", "queueStoreEntry", "queueEntry", "timestamp", "metadata", "Queue", "forceSyncFallback", "onSync", "maxRetentionTime", "_syncInProgress", "_requestsAddedDuringSync", "WorkboxError", "_name", "_onSync", "replayRequests", "_maxRetentionTime", "_forceSyncFallback", "Boolean", "_queueStore", "_addSyncListener", "pushRequest", "isInstance", "_addRequest", "unshiftRequest", "popRequest", "_removeRequest", "shiftRequest", "allEntries", "now", "Date", "unexpiredEntries", "maxRetentionTimeInMs", "push", "operation", "storableRequest", "logger", "log", "getFriendlyURL", "registerSync", "fetch", "process", "registration", "sync", "register", "err", "warn", "tag", "syncComplete", "syncError", "queue", "Error", "lastC<PERSON>ce", "waitUntil", "_queueNames", "BackgroundSyncPlugin", "options", "fetchDidFail", "_queue"], "mappings": ";;;;;;;;;;;;;;;;;;;EAAA,MAAMA,aAAa,GAAGA,CAACC,MAAM,EAAEC,YAAY,KAAKA,YAAY,CAACC,IAAI,CAAEC,CAAC,IAAKH,MAAM,YAAYG,CAAC,CAAC,CAAA;EAE7F,IAAIC,iBAAiB,CAAA;EACrB,IAAIC,oBAAoB,CAAA;EACxB;EACA,SAASC,oBAAoBA,GAAG;EAC5B,EAAA,OAAQF,iBAAiB,KACpBA,iBAAiB,GAAG,CACjBG,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,cAAc,CACjB,CAAC,CAAA;EACV,CAAA;EACA;EACA,SAASC,uBAAuBA,GAAG;IAC/B,OAAQP,oBAAoB,KACvBA,oBAAoB,GAAG,CACpBK,SAAS,CAACG,SAAS,CAACC,OAAO,EAC3BJ,SAAS,CAACG,SAAS,CAACE,QAAQ,EAC5BL,SAAS,CAACG,SAAS,CAACG,kBAAkB,CACzC,CAAC,CAAA;EACV,CAAA;EACA,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,EAAE,CAAA;EACtC,MAAMC,kBAAkB,GAAG,IAAID,OAAO,EAAE,CAAA;EACxC,MAAME,wBAAwB,GAAG,IAAIF,OAAO,EAAE,CAAA;EAC9C,MAAMG,cAAc,GAAG,IAAIH,OAAO,EAAE,CAAA;EACpC,MAAMI,qBAAqB,GAAG,IAAIJ,OAAO,EAAE,CAAA;EAC3C,SAASK,gBAAgBA,CAACC,OAAO,EAAE;IAC/B,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC7C,MAAMC,QAAQ,GAAGA,MAAM;EACnBL,MAAAA,OAAO,CAACM,mBAAmB,CAAC,SAAS,EAAEC,OAAO,CAAC,CAAA;EAC/CP,MAAAA,OAAO,CAACM,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC,CAAA;OAC9C,CAAA;MACD,MAAMD,OAAO,GAAGA,MAAM;EAClBJ,MAAAA,OAAO,CAACM,IAAI,CAACT,OAAO,CAACU,MAAM,CAAC,CAAC,CAAA;EAC7BL,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;MACD,MAAMG,KAAK,GAAGA,MAAM;EAChBJ,MAAAA,MAAM,CAACJ,OAAO,CAACQ,KAAK,CAAC,CAAA;EACrBH,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;EACDL,IAAAA,OAAO,CAACW,gBAAgB,CAAC,SAAS,EAAEJ,OAAO,CAAC,CAAA;EAC5CP,IAAAA,OAAO,CAACW,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAA;EAC5C,GAAC,CAAC,CAAA;EACFP,EAAAA,OAAO,CACFW,IAAI,CAAEC,KAAK,IAAK;EACjB;EACA;MACA,IAAIA,KAAK,YAAY3B,SAAS,EAAE;EAC5BO,MAAAA,gBAAgB,CAACqB,GAAG,CAACD,KAAK,EAAEb,OAAO,CAAC,CAAA;EACxC,KAAA;EACA;EACJ,GAAC,CAAC,CACGe,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;EACrB;EACA;EACAjB,EAAAA,qBAAqB,CAACgB,GAAG,CAACb,OAAO,EAAED,OAAO,CAAC,CAAA;EAC3C,EAAA,OAAOC,OAAO,CAAA;EAClB,CAAA;EACA,SAASe,8BAA8BA,CAACC,EAAE,EAAE;EACxC;EACA,EAAA,IAAItB,kBAAkB,CAACuB,GAAG,CAACD,EAAE,CAAC,EAC1B,OAAA;IACJ,MAAME,IAAI,GAAG,IAAIjB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC1C,MAAMC,QAAQ,GAAGA,MAAM;EACnBY,MAAAA,EAAE,CAACX,mBAAmB,CAAC,UAAU,EAAEc,QAAQ,CAAC,CAAA;EAC5CH,MAAAA,EAAE,CAACX,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC,CAAA;EACtCS,MAAAA,EAAE,CAACX,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC,CAAA;OACzC,CAAA;MACD,MAAMY,QAAQ,GAAGA,MAAM;EACnBjB,MAAAA,OAAO,EAAE,CAAA;EACTE,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;MACD,MAAMG,KAAK,GAAGA,MAAM;EAChBJ,MAAAA,MAAM,CAACa,EAAE,CAACT,KAAK,IAAI,IAAIa,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;EAChEhB,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;EACDY,IAAAA,EAAE,CAACN,gBAAgB,CAAC,UAAU,EAAES,QAAQ,CAAC,CAAA;EACzCH,IAAAA,EAAE,CAACN,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAA;EACnCS,IAAAA,EAAE,CAACN,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAA;EACvC,GAAC,CAAC,CAAA;EACF;EACAb,EAAAA,kBAAkB,CAACmB,GAAG,CAACG,EAAE,EAAEE,IAAI,CAAC,CAAA;EACpC,CAAA;EACA,IAAIG,aAAa,GAAG;EAChBC,EAAAA,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;MACxB,IAAIF,MAAM,YAAYrC,cAAc,EAAE;EAClC;QACA,IAAIsC,IAAI,KAAK,MAAM,EACf,OAAO9B,kBAAkB,CAAC4B,GAAG,CAACC,MAAM,CAAC,CAAA;EACzC;QACA,IAAIC,IAAI,KAAK,kBAAkB,EAAE;UAC7B,OAAOD,MAAM,CAACG,gBAAgB,IAAI/B,wBAAwB,CAAC2B,GAAG,CAACC,MAAM,CAAC,CAAA;EAC1E,OAAA;EACA;QACA,IAAIC,IAAI,KAAK,OAAO,EAAE;EAClB,QAAA,OAAOC,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,GAC7BC,SAAS,GACTF,QAAQ,CAACG,WAAW,CAACH,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAA;EAC5D,OAAA;EACJ,KAAA;EACA;EACA,IAAA,OAAOlB,IAAI,CAACe,MAAM,CAACC,IAAI,CAAC,CAAC,CAAA;KAC5B;EACDX,EAAAA,GAAGA,CAACU,MAAM,EAAEC,IAAI,EAAEZ,KAAK,EAAE;EACrBW,IAAAA,MAAM,CAACC,IAAI,CAAC,GAAGZ,KAAK,CAAA;EACpB,IAAA,OAAO,IAAI,CAAA;KACd;EACDK,EAAAA,GAAGA,CAACM,MAAM,EAAEC,IAAI,EAAE;EACd,IAAA,IAAID,MAAM,YAAYrC,cAAc,KAC/BsC,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,CAAC,EAAE;EACvC,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;MACA,OAAOA,IAAI,IAAID,MAAM,CAAA;EACzB,GAAA;EACJ,CAAC,CAAA;EACD,SAASM,YAAYA,CAACC,QAAQ,EAAE;EAC5BT,EAAAA,aAAa,GAAGS,QAAQ,CAACT,aAAa,CAAC,CAAA;EAC3C,CAAA;EACA,SAASU,YAAYA,CAACC,IAAI,EAAE;EACxB;EACA;EACA;EACA,EAAA,IAAIA,IAAI,KAAKlD,WAAW,CAACM,SAAS,CAAC6C,WAAW,IAC1C,EAAE,kBAAkB,IAAI/C,cAAc,CAACE,SAAS,CAAC,EAAE;EACnD,IAAA,OAAO,UAAU8C,UAAU,EAAE,GAAGC,IAAI,EAAE;EAClC,MAAA,MAAMnB,EAAE,GAAGgB,IAAI,CAACI,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,EAAEH,UAAU,EAAE,GAAGC,IAAI,CAAC,CAAA;EACvDxC,MAAAA,wBAAwB,CAACkB,GAAG,CAACG,EAAE,EAAEkB,UAAU,CAACI,IAAI,GAAGJ,UAAU,CAACI,IAAI,EAAE,GAAG,CAACJ,UAAU,CAAC,CAAC,CAAA;QACpF,OAAO1B,IAAI,CAACQ,EAAE,CAAC,CAAA;OAClB,CAAA;EACL,GAAA;EACA;EACA;EACA;EACA;EACA;IACA,IAAI7B,uBAAuB,EAAE,CAACoD,QAAQ,CAACP,IAAI,CAAC,EAAE;MAC1C,OAAO,UAAU,GAAGG,IAAI,EAAE;EACtB;EACA;QACAH,IAAI,CAACQ,KAAK,CAACH,MAAM,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC,CAAA;QAC9B,OAAO3B,IAAI,CAAChB,gBAAgB,CAAC8B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;OAC1C,CAAA;EACL,GAAA;IACA,OAAO,UAAU,GAAGa,IAAI,EAAE;EACtB;EACA;EACA,IAAA,OAAO3B,IAAI,CAACwB,IAAI,CAACQ,KAAK,CAACH,MAAM,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAA;KAC9C,CAAA;EACL,CAAA;EACA,SAASM,sBAAsBA,CAAC7B,KAAK,EAAE;IACnC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAC3B,OAAOmB,YAAY,CAACnB,KAAK,CAAC,CAAA;EAC9B;EACA;EACA,EAAA,IAAIA,KAAK,YAAY1B,cAAc,EAC/B6B,8BAA8B,CAACH,KAAK,CAAC,CAAA;EACzC,EAAA,IAAItC,aAAa,CAACsC,KAAK,EAAE/B,oBAAoB,EAAE,CAAC,EAC5C,OAAO,IAAI6D,KAAK,CAAC9B,KAAK,EAAES,aAAa,CAAC,CAAA;EAC1C;EACA,EAAA,OAAOT,KAAK,CAAA;EAChB,CAAA;EACA,SAASJ,IAAIA,CAACI,KAAK,EAAE;EACjB;EACA;IACA,IAAIA,KAAK,YAAY+B,UAAU,EAC3B,OAAO7C,gBAAgB,CAACc,KAAK,CAAC,CAAA;EAClC;EACA;EACA,EAAA,IAAIhB,cAAc,CAACqB,GAAG,CAACL,KAAK,CAAC,EACzB,OAAOhB,cAAc,CAAC0B,GAAG,CAACV,KAAK,CAAC,CAAA;EACpC,EAAA,MAAMgC,QAAQ,GAAGH,sBAAsB,CAAC7B,KAAK,CAAC,CAAA;EAC9C;EACA;IACA,IAAIgC,QAAQ,KAAKhC,KAAK,EAAE;EACpBhB,IAAAA,cAAc,CAACiB,GAAG,CAACD,KAAK,EAAEgC,QAAQ,CAAC,CAAA;EACnC/C,IAAAA,qBAAqB,CAACgB,GAAG,CAAC+B,QAAQ,EAAEhC,KAAK,CAAC,CAAA;EAC9C,GAAA;EACA,EAAA,OAAOgC,QAAQ,CAAA;EACnB,CAAA;EACA,MAAMP,MAAM,GAAIzB,KAAK,IAAKf,qBAAqB,CAACyB,GAAG,CAACV,KAAK,CAAC;;ECnL1D;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASiC,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAAEC,OAAO;IAAEC,OAAO;IAAEC,QAAQ;EAAEC,EAAAA,UAAAA;EAAW,CAAC,GAAG,EAAE,EAAE;IAC5E,MAAMpD,OAAO,GAAGqD,SAAS,CAACC,IAAI,CAACP,IAAI,EAAEC,OAAO,CAAC,CAAA;EAC7C,EAAA,MAAMO,WAAW,GAAG9C,IAAI,CAACT,OAAO,CAAC,CAAA;EACjC,EAAA,IAAIkD,OAAO,EAAE;EACTlD,IAAAA,OAAO,CAACW,gBAAgB,CAAC,eAAe,EAAG6C,KAAK,IAAK;QACjDN,OAAO,CAACzC,IAAI,CAACT,OAAO,CAACU,MAAM,CAAC,EAAE8C,KAAK,CAACC,UAAU,EAAED,KAAK,CAACE,UAAU,EAAEjD,IAAI,CAACT,OAAO,CAACkC,WAAW,CAAC,CAAC,CAAA;EAChG,KAAC,CAAC,CAAA;EACN,GAAA;EACA,EAAA,IAAIe,OAAO,EACPjD,OAAO,CAACW,gBAAgB,CAAC,SAAS,EAAE,MAAMsC,OAAO,EAAE,CAAC,CAAA;EACxDM,EAAAA,WAAW,CACN3C,IAAI,CAAE+C,EAAE,IAAK;EACd,IAAA,IAAIP,UAAU,EACVO,EAAE,CAAChD,gBAAgB,CAAC,OAAO,EAAE,MAAMyC,UAAU,EAAE,CAAC,CAAA;EACpD,IAAA,IAAID,QAAQ,EACRQ,EAAE,CAAChD,gBAAgB,CAAC,eAAe,EAAE,MAAMwC,QAAQ,EAAE,CAAC,CAAA;EAC9D,GAAC,CAAC,CACGpC,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;EACrB,EAAA,OAAOwC,WAAW,CAAA;EACtB,CAAA;EAaA,MAAMK,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAA;EACtE,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;EACtD,MAAMC,aAAa,GAAG,IAAIC,GAAG,EAAE,CAAA;EAC/B,SAASC,SAASA,CAACxC,MAAM,EAAEC,IAAI,EAAE;EAC7B,EAAA,IAAI,EAAED,MAAM,YAAYzC,WAAW,IAC/B,EAAE0C,IAAI,IAAID,MAAM,CAAC,IACjB,OAAOC,IAAI,KAAK,QAAQ,CAAC,EAAE;EAC3B,IAAA,OAAA;EACJ,GAAA;EACA,EAAA,IAAIqC,aAAa,CAACvC,GAAG,CAACE,IAAI,CAAC,EACvB,OAAOqC,aAAa,CAACvC,GAAG,CAACE,IAAI,CAAC,CAAA;IAClC,MAAMwC,cAAc,GAAGxC,IAAI,CAACyC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;EACrD,EAAA,MAAMC,QAAQ,GAAG1C,IAAI,KAAKwC,cAAc,CAAA;EACxC,EAAA,MAAMG,OAAO,GAAGP,YAAY,CAACrB,QAAQ,CAACyB,cAAc,CAAC,CAAA;EACrD,EAAA;EACA;IACA,EAAEA,cAAc,IAAI,CAACE,QAAQ,GAAGlF,QAAQ,GAAGD,cAAc,EAAEK,SAAS,CAAC,IACjE,EAAE+E,OAAO,IAAIR,WAAW,CAACpB,QAAQ,CAACyB,cAAc,CAAC,CAAC,EAAE;EACpD,IAAA,OAAA;EACJ,GAAA;IACA,MAAMI,MAAM,GAAG,gBAAgBC,SAAS,EAAE,GAAGlC,IAAI,EAAE;EAC/C;EACA,IAAA,MAAMnB,EAAE,GAAG,IAAI,CAACiB,WAAW,CAACoC,SAAS,EAAEF,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC,CAAA;EAC1E,IAAA,IAAI5C,MAAM,GAAGP,EAAE,CAACsD,KAAK,CAAA;EACrB,IAAA,IAAIJ,QAAQ,EACR3C,MAAM,GAAGA,MAAM,CAACgD,KAAK,CAACpC,IAAI,CAACqC,KAAK,EAAE,CAAC,CAAA;EACvC;EACA;EACA;EACA;EACA;MACA,OAAO,CAAC,MAAMvE,OAAO,CAACwE,GAAG,CAAC,CACtBlD,MAAM,CAACyC,cAAc,CAAC,CAAC,GAAG7B,IAAI,CAAC,EAC/BgC,OAAO,IAAInD,EAAE,CAACE,IAAI,CACrB,CAAC,EAAE,CAAC,CAAC,CAAA;KACT,CAAA;EACD2C,EAAAA,aAAa,CAAChD,GAAG,CAACW,IAAI,EAAE4C,MAAM,CAAC,CAAA;EAC/B,EAAA,OAAOA,MAAM,CAAA;EACjB,CAAA;EACAvC,YAAY,CAAE6C,QAAQ,IAAAC,QAAA,KACfD,QAAQ,EAAA;IACXpD,GAAG,EAAEA,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,KAAKsC,SAAS,CAACxC,MAAM,EAAEC,IAAI,CAAC,IAAIkD,QAAQ,CAACpD,GAAG,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;IAChGR,GAAG,EAAEA,CAACM,MAAM,EAAEC,IAAI,KAAK,CAAC,CAACuC,SAAS,CAACxC,MAAM,EAAEC,IAAI,CAAC,IAAIkD,QAAQ,CAACzD,GAAG,CAACM,MAAM,EAAEC,IAAI,CAAA;EAAC,CAAA,CAChF,CAAC;;ECpFH;EACA,IAAI;EACAoD,EAAAA,IAAI,CAAC,+BAA+B,CAAC,IAAIC,CAAC,EAAE,CAAA;EAChD,CAAC,CACD,OAAOC,CAAC,EAAE;;ECLV;EACA;AACA;EACA;EACA;EACA;EACA;EAGA,MAAMC,UAAU,GAAG,CAAC,CAAA;EACpB,MAAMC,OAAO,GAAG,yBAAyB,CAAA;EACzC,MAAMC,yBAAyB,GAAG,UAAU,CAAA;EAC5C,MAAMC,gBAAgB,GAAG,WAAW,CAAA;EACpC;EACA;EACA;EACA;EACA;EACA;EACA;EACO,MAAMC,OAAO,CAAC;EACjBC,EAAAA,WAAWA,GAAG;MACV,IAAI,CAACC,GAAG,GAAG,IAAI,CAAA;EACnB,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMC,QAAQA,CAACC,KAAK,EAAE;EAClB,IAAA,MAAM7B,EAAE,GAAG,MAAM,IAAI,CAAC8B,KAAK,EAAE,CAAA;MAC7B,MAAMxE,EAAE,GAAG0C,EAAE,CAACzB,WAAW,CAACgD,yBAAyB,EAAE,WAAW,EAAE;EAC9DQ,MAAAA,UAAU,EAAE,SAAA;EAChB,KAAC,CAAC,CAAA;EACF,IAAA,MAAMzE,EAAE,CAACsD,KAAK,CAACoB,GAAG,CAACH,KAAK,CAAC,CAAA;MACzB,MAAMvE,EAAE,CAACE,IAAI,CAAA;EACjB,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMyE,eAAeA,GAAG;EACpB,IAAA,MAAMjC,EAAE,GAAG,MAAM,IAAI,CAAC8B,KAAK,EAAE,CAAA;EAC7B,IAAA,MAAMI,MAAM,GAAG,MAAMlC,EAAE,CAClBzB,WAAW,CAACgD,yBAAyB,CAAC,CACtCX,KAAK,CAACuB,UAAU,EAAE,CAAA;EACvB,IAAA,OAAOD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAChF,KAAK,CAACkF,EAAE,CAAA;EAC1E,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;IACI,MAAMC,wBAAwBA,CAACC,SAAS,EAAE;EACtC,IAAA,MAAMtC,EAAE,GAAG,MAAM,IAAI,CAAC8B,KAAK,EAAE,CAAA;EAC7B,IAAA,MAAMS,OAAO,GAAG,MAAMvC,EAAE,CAACwC,eAAe,CAACjB,yBAAyB,EAAEC,gBAAgB,EAAEiB,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAA;EAClH,IAAA,OAAOC,OAAO,GAAGA,OAAO,GAAG,IAAII,KAAK,EAAE,CAAA;EAC1C,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;IACI,MAAMC,wBAAwBA,CAACN,SAAS,EAAE;EACtC,IAAA,MAAMtC,EAAE,GAAG,MAAM,IAAI,CAAC8B,KAAK,EAAE,CAAA;EAC7B,IAAA,OAAO9B,EAAE,CAAC6C,cAAc,CAACtB,yBAAyB,EAAEC,gBAAgB,EAAEiB,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAA;EACtG,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMQ,WAAWA,CAACV,EAAE,EAAE;EAClB,IAAA,MAAMpC,EAAE,GAAG,MAAM,IAAI,CAAC8B,KAAK,EAAE,CAAA;EAC7B,IAAA,MAAM9B,EAAE,CAAC+C,MAAM,CAACxB,yBAAyB,EAAEa,EAAE,CAAC,CAAA;EAClD,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMY,wBAAwBA,CAACV,SAAS,EAAE;EACtC,IAAA,OAAO,MAAM,IAAI,CAACW,oBAAoB,CAACR,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,EAAE,MAAM,CAAC,CAAA;EAC/E,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMY,uBAAuBA,CAACZ,SAAS,EAAE;EACrC,IAAA,OAAO,MAAM,IAAI,CAACW,oBAAoB,CAACR,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,EAAE,MAAM,CAAC,CAAA;EAC/E,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI,EAAA,MAAMW,oBAAoBA,CAACE,KAAK,EAAEC,SAAS,EAAE;EACzC,IAAA,MAAMpD,EAAE,GAAG,MAAM,IAAI,CAAC8B,KAAK,EAAE,CAAA;MAC7B,MAAMI,MAAM,GAAG,MAAMlC,EAAE,CAClBzB,WAAW,CAACgD,yBAAyB,CAAC,CACtCX,KAAK,CAACC,KAAK,CAACW,gBAAgB,CAAC,CAC7BW,UAAU,CAACgB,KAAK,EAAEC,SAAS,CAAC,CAAA;EACjC,IAAA,OAAOlB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAChF,KAAK,CAAA;EACvE,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAM4E,KAAKA,GAAG;EACV,IAAA,IAAI,CAAC,IAAI,CAACH,GAAG,EAAE;QACX,IAAI,CAACA,GAAG,GAAG,MAAMxC,MAAM,CAACmC,OAAO,EAAED,UAAU,EAAE;UACzC9B,OAAO,EAAE,IAAI,CAAC8D,UAAAA;EAClB,OAAC,CAAC,CAAA;EACN,KAAA;MACA,OAAO,IAAI,CAAC1B,GAAG,CAAA;EACnB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACI0B,EAAAA,UAAUA,CAACrD,EAAE,EAAEF,UAAU,EAAE;EACvB,IAAA,IAAIA,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAGuB,UAAU,EAAE;QAC3C,IAAIrB,EAAE,CAAChC,gBAAgB,CAACsF,QAAQ,CAAC/B,yBAAyB,CAAC,EAAE;EACzDvB,QAAAA,EAAE,CAACuD,iBAAiB,CAAChC,yBAAyB,CAAC,CAAA;EACnD,OAAA;EACJ,KAAA;EACA,IAAA,MAAMiC,QAAQ,GAAGxD,EAAE,CAACyD,iBAAiB,CAAClC,yBAAyB,EAAE;EAC7DmC,MAAAA,aAAa,EAAE,IAAI;EACnBC,MAAAA,OAAO,EAAE,IAAA;EACb,KAAC,CAAC,CAAA;EACFH,IAAAA,QAAQ,CAACI,WAAW,CAACpC,gBAAgB,EAAEA,gBAAgB,EAAE;EAAEqC,MAAAA,MAAM,EAAE,KAAA;EAAM,KAAC,CAAC,CAAA;EAC/E,GAAA;EACJ;;EChJA;EACA;AACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,MAAMC,UAAU,CAAC;EACpB;EACJ;EACA;EACA;EACA;EACA;IACIpC,WAAWA,CAACY,SAAS,EAAE;MACnB,IAAI,CAACyB,UAAU,GAAGzB,SAAS,CAAA;EAC3B,IAAA,IAAI,CAAC0B,QAAQ,GAAG,IAAIvC,OAAO,EAAE,CAAA;EACjC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAMwC,SAASA,CAACpC,KAAK,EAAE;EACnB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAM,CAACtC,KAAK,EAAE,QAAQ,EAAE;EAC3BuC,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,YAAY;EACvBC,QAAAA,QAAQ,EAAE,WAAW;EACrBC,QAAAA,SAAS,EAAE,OAAA;EACf,OAAC,CAAC,CAAA;QACFL,gBAAM,CAACC,MAAM,CAACtC,KAAK,CAAC2C,WAAW,EAAE,QAAQ,EAAE;EACvCJ,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,YAAY;EACvBC,QAAAA,QAAQ,EAAE,WAAW;EACrBC,QAAAA,SAAS,EAAE,mBAAA;EACf,OAAC,CAAC,CAAA;EACN,KAAA;EACA;MACA,OAAO1C,KAAK,CAACO,EAAE,CAAA;EACfP,IAAAA,KAAK,CAACS,SAAS,GAAG,IAAI,CAACyB,UAAU,CAAA;EACjC,IAAA,MAAM,IAAI,CAACC,QAAQ,CAACpC,QAAQ,CAACC,KAAK,CAAC,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAM4C,YAAYA,CAAC5C,KAAK,EAAE;EACtB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAM,CAACtC,KAAK,EAAE,QAAQ,EAAE;EAC3BuC,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,YAAY;EACvBC,QAAAA,QAAQ,EAAE,cAAc;EACxBC,QAAAA,SAAS,EAAE,OAAA;EACf,OAAC,CAAC,CAAA;QACFL,gBAAM,CAACC,MAAM,CAACtC,KAAK,CAAC2C,WAAW,EAAE,QAAQ,EAAE;EACvCJ,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,YAAY;EACvBC,QAAAA,QAAQ,EAAE,cAAc;EACxBC,QAAAA,SAAS,EAAE,mBAAA;EACf,OAAC,CAAC,CAAA;EACN,KAAA;MACA,MAAMG,OAAO,GAAG,MAAM,IAAI,CAACV,QAAQ,CAAC/B,eAAe,EAAE,CAAA;EACrD,IAAA,IAAIyC,OAAO,EAAE;EACT;EACA7C,MAAAA,KAAK,CAACO,EAAE,GAAGsC,OAAO,GAAG,CAAC,CAAA;EAC1B,KAAC,MACI;EACD;QACA,OAAO7C,KAAK,CAACO,EAAE,CAAA;EACnB,KAAA;EACAP,IAAAA,KAAK,CAACS,SAAS,GAAG,IAAI,CAACyB,UAAU,CAAA;EACjC,IAAA,MAAM,IAAI,CAACC,QAAQ,CAACpC,QAAQ,CAACC,KAAK,CAAC,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAM8C,QAAQA,GAAG;EACb,IAAA,OAAO,IAAI,CAACC,YAAY,CAAC,MAAM,IAAI,CAACZ,QAAQ,CAACd,uBAAuB,CAAC,IAAI,CAACa,UAAU,CAAC,CAAC,CAAA;EAC1F,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMc,UAAUA,GAAG;EACf,IAAA,OAAO,IAAI,CAACD,YAAY,CAAC,MAAM,IAAI,CAACZ,QAAQ,CAAChB,wBAAwB,CAAC,IAAI,CAACe,UAAU,CAAC,CAAC,CAAA;EAC3F,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;IACI,MAAMe,MAAMA,GAAG;MACX,OAAO,MAAM,IAAI,CAACd,QAAQ,CAAC3B,wBAAwB,CAAC,IAAI,CAAC0B,UAAU,CAAC,CAAA;EACxE,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;IACI,MAAMgB,IAAIA,GAAG;MACT,OAAO,MAAM,IAAI,CAACf,QAAQ,CAACpB,wBAAwB,CAAC,IAAI,CAACmB,UAAU,CAAC,CAAA;EACxE,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAMjB,WAAWA,CAACV,EAAE,EAAE;EAClB,IAAA,MAAM,IAAI,CAAC4B,QAAQ,CAAClB,WAAW,CAACV,EAAE,CAAC,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IACI,MAAMwC,YAAYA,CAAC/C,KAAK,EAAE;EACtB,IAAA,IAAIA,KAAK,EAAE;EACP,MAAA,MAAM,IAAI,CAACiB,WAAW,CAACjB,KAAK,CAACO,EAAE,CAAC,CAAA;EACpC,KAAA;EACA,IAAA,OAAOP,KAAK,CAAA;EAChB,GAAA;EACJ;;ECvJA;EACA;AACA;EACA;EACA;EACA;EACA;EAGA,MAAMmD,sBAAsB,GAAG,CAC3B,QAAQ,EACR,UAAU,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACb,OAAO,EACP,UAAU,EACV,WAAW,EACX,WAAW,CACd,CAAA;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,eAAe,CAAC;EAClB;EACJ;EACA;EACA;EACA;EACA;EACA;IACI,aAAaC,WAAWA,CAAC7I,OAAO,EAAE;EAC9B,IAAA,MAAMmI,WAAW,GAAG;QAChBW,GAAG,EAAE9I,OAAO,CAAC8I,GAAG;EAChBC,MAAAA,OAAO,EAAE,EAAC;OACb,CAAA;EACD;EACA,IAAA,IAAI/I,OAAO,CAACqE,MAAM,KAAK,KAAK,EAAE;EAC1B;EACA;EACA;EACA;EACA8D,MAAAA,WAAW,CAACa,IAAI,GAAG,MAAMhJ,OAAO,CAACiJ,KAAK,EAAE,CAACC,WAAW,EAAE,CAAA;EAC1D,KAAA;EACA;EACA,IAAA,KAAK,MAAM,CAACC,GAAG,EAAEtI,KAAK,CAAC,IAAIb,OAAO,CAAC+I,OAAO,CAACK,OAAO,EAAE,EAAE;EAClDjB,MAAAA,WAAW,CAACY,OAAO,CAACI,GAAG,CAAC,GAAGtI,KAAK,CAAA;EACpC,KAAA;EACA;EACA,IAAA,KAAK,MAAMY,IAAI,IAAIkH,sBAAsB,EAAE;EACvC,MAAA,IAAI3I,OAAO,CAACyB,IAAI,CAAC,KAAKG,SAAS,EAAE;EAC7BuG,QAAAA,WAAW,CAAC1G,IAAI,CAAC,GAAGzB,OAAO,CAACyB,IAAI,CAAC,CAAA;EACrC,OAAA;EACJ,KAAA;EACA,IAAA,OAAO,IAAImH,eAAe,CAACT,WAAW,CAAC,CAAA;EAC3C,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACI9C,WAAWA,CAAC8C,WAAW,EAAE;EACrB,IAA2C;EACvCN,MAAAA,gBAAM,CAACC,MAAM,CAACK,WAAW,EAAE,QAAQ,EAAE;EACjCJ,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,iBAAiB;EAC5BC,QAAAA,QAAQ,EAAE,aAAa;EACvBC,QAAAA,SAAS,EAAE,aAAA;EACf,OAAC,CAAC,CAAA;QACFL,gBAAM,CAACC,MAAM,CAACK,WAAW,CAACW,GAAG,EAAE,QAAQ,EAAE;EACrCf,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,iBAAiB;EAC5BC,QAAAA,QAAQ,EAAE,aAAa;EACvBC,QAAAA,SAAS,EAAE,iBAAA;EACf,OAAC,CAAC,CAAA;EACN,KAAA;EACA;EACA;EACA,IAAA,IAAIC,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE;EACpCA,MAAAA,WAAW,CAAC,MAAM,CAAC,GAAG,aAAa,CAAA;EACvC,KAAA;MACA,IAAI,CAACkB,YAAY,GAAGlB,WAAW,CAAA;EACnC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACImB,EAAAA,QAAQA,GAAG;EACP,IAAA,MAAMnB,WAAW,GAAGoB,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACH,YAAY,CAAC,CAAA;EACxDlB,IAAAA,WAAW,CAACY,OAAO,GAAGQ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACH,YAAY,CAACN,OAAO,CAAC,CAAA;MAClE,IAAIZ,WAAW,CAACa,IAAI,EAAE;QAClBb,WAAW,CAACa,IAAI,GAAGb,WAAW,CAACa,IAAI,CAACS,KAAK,CAAC,CAAC,CAAC,CAAA;EAChD,KAAA;EACA,IAAA,OAAOtB,WAAW,CAAA;EACtB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACIuB,EAAAA,SAASA,GAAG;EACR,IAAA,OAAO,IAAIC,OAAO,CAAC,IAAI,CAACN,YAAY,CAACP,GAAG,EAAE,IAAI,CAACO,YAAY,CAAC,CAAA;EAChE,GAAA;EACA;EACJ;EACA;EACA;EACA;EACIJ,EAAAA,KAAKA,GAAG;MACJ,OAAO,IAAIL,eAAe,CAAC,IAAI,CAACU,QAAQ,EAAE,CAAC,CAAA;EAC/C,GAAA;EACJ;;ECvHA;EACA;AACA;EACA;EACA;EACA;EACA;EAQA,MAAMM,UAAU,GAAG,yBAAyB,CAAA;EAC5C,MAAMC,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EACvC,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAE,CAAA;EAC5B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,YAAY,GAAIC,eAAe,IAAK;EACtC,EAAA,MAAMC,UAAU,GAAG;MACflK,OAAO,EAAE,IAAI4I,eAAe,CAACqB,eAAe,CAAC9B,WAAW,CAAC,CAACuB,SAAS,EAAE;MACrES,SAAS,EAAEF,eAAe,CAACE,SAAAA;KAC9B,CAAA;IACD,IAAIF,eAAe,CAACG,QAAQ,EAAE;EAC1BF,IAAAA,UAAU,CAACE,QAAQ,GAAGH,eAAe,CAACG,QAAQ,CAAA;EAClD,GAAA;EACA,EAAA,OAAOF,UAAU,CAAA;EACrB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMG,KAAK,CAAC;EACR;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACIhF,WAAWA,CAACtC,IAAI,EAAE;MAAEuH,iBAAiB;MAAEC,MAAM;EAAEC,IAAAA,gBAAAA;KAAkB,GAAG,EAAE,EAAE;MACpE,IAAI,CAACC,eAAe,GAAG,KAAK,CAAA;MAC5B,IAAI,CAACC,wBAAwB,GAAG,KAAK,CAAA;EACrC;EACA,IAAA,IAAIZ,UAAU,CAAC5I,GAAG,CAAC6B,IAAI,CAAC,EAAE;EACtB,MAAA,MAAM,IAAI4H,4BAAY,CAAC,sBAAsB,EAAE;EAAE5H,QAAAA,IAAAA;EAAK,OAAC,CAAC,CAAA;EAC5D,KAAC,MACI;EACD+G,MAAAA,UAAU,CAACnE,GAAG,CAAC5C,IAAI,CAAC,CAAA;EACxB,KAAA;MACA,IAAI,CAAC6H,KAAK,GAAG7H,IAAI,CAAA;EACjB,IAAA,IAAI,CAAC8H,OAAO,GAAGN,MAAM,IAAI,IAAI,CAACO,cAAc,CAAA;EAC5C,IAAA,IAAI,CAACC,iBAAiB,GAAGP,gBAAgB,IAAIX,kBAAkB,CAAA;EAC/D,IAAA,IAAI,CAACmB,kBAAkB,GAAGC,OAAO,CAACX,iBAAiB,CAAC,CAAA;MACpD,IAAI,CAACY,WAAW,GAAG,IAAIzD,UAAU,CAAC,IAAI,CAACmD,KAAK,CAAC,CAAA;MAC7C,IAAI,CAACO,gBAAgB,EAAE,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;IACI,IAAIpI,IAAIA,GAAG;MACP,OAAO,IAAI,CAAC6H,KAAK,CAAA;EACrB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAMQ,WAAWA,CAAC5F,KAAK,EAAE;EACrB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAM,CAACtC,KAAK,EAAE,QAAQ,EAAE;EAC3BuC,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,OAAO;EAClBC,QAAAA,QAAQ,EAAE,aAAa;EACvBC,QAAAA,SAAS,EAAE,OAAA;EACf,OAAC,CAAC,CAAA;QACFL,gBAAM,CAACwD,UAAU,CAAC7F,KAAK,CAACxF,OAAO,EAAE2J,OAAO,EAAE;EACtC5B,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,OAAO;EAClBC,QAAAA,QAAQ,EAAE,aAAa;EACvBC,QAAAA,SAAS,EAAE,eAAA;EACf,OAAC,CAAC,CAAA;EACN,KAAA;EACA,IAAA,MAAM,IAAI,CAACoD,WAAW,CAAC9F,KAAK,EAAE,MAAM,CAAC,CAAA;EACzC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAM+F,cAAcA,CAAC/F,KAAK,EAAE;EACxB,IAA2C;EACvCqC,MAAAA,gBAAM,CAACC,MAAM,CAACtC,KAAK,EAAE,QAAQ,EAAE;EAC3BuC,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,OAAO;EAClBC,QAAAA,QAAQ,EAAE,gBAAgB;EAC1BC,QAAAA,SAAS,EAAE,OAAA;EACf,OAAC,CAAC,CAAA;QACFL,gBAAM,CAACwD,UAAU,CAAC7F,KAAK,CAACxF,OAAO,EAAE2J,OAAO,EAAE;EACtC5B,QAAAA,UAAU,EAAE,yBAAyB;EACrCC,QAAAA,SAAS,EAAE,OAAO;EAClBC,QAAAA,QAAQ,EAAE,gBAAgB;EAC1BC,QAAAA,SAAS,EAAE,eAAA;EACf,OAAC,CAAC,CAAA;EACN,KAAA;EACA,IAAA,MAAM,IAAI,CAACoD,WAAW,CAAC9F,KAAK,EAAE,SAAS,CAAC,CAAA;EAC5C,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IACI,MAAMgG,UAAUA,GAAG;EACf,IAAA,OAAO,IAAI,CAACC,cAAc,CAAC,KAAK,CAAC,CAAA;EACrC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IACI,MAAMC,YAAYA,GAAG;EACjB,IAAA,OAAO,IAAI,CAACD,cAAc,CAAC,OAAO,CAAC,CAAA;EACvC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;IACI,MAAMhD,MAAMA,GAAG;MACX,MAAMkD,UAAU,GAAG,MAAM,IAAI,CAACT,WAAW,CAACzC,MAAM,EAAE,CAAA;EAClD,IAAA,MAAMmD,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE,CAAA;MACtB,MAAME,gBAAgB,GAAG,EAAE,CAAA;EAC3B,IAAA,KAAK,MAAMtG,KAAK,IAAImG,UAAU,EAAE;EAC5B;EACA;QACA,MAAMI,oBAAoB,GAAG,IAAI,CAAChB,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAA;EAC/D,MAAA,IAAIa,GAAG,GAAGpG,KAAK,CAAC2E,SAAS,GAAG4B,oBAAoB,EAAE;UAC9C,MAAM,IAAI,CAACb,WAAW,CAACzE,WAAW,CAACjB,KAAK,CAACO,EAAE,CAAC,CAAA;EAChD,OAAC,MACI;EACD+F,QAAAA,gBAAgB,CAACE,IAAI,CAAChC,YAAY,CAACxE,KAAK,CAAC,CAAC,CAAA;EAC9C,OAAA;EACJ,KAAA;EACA,IAAA,OAAOsG,gBAAgB,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;IACI,MAAMpD,IAAIA,GAAG;EACT,IAAA,OAAO,MAAM,IAAI,CAACwC,WAAW,CAACxC,IAAI,EAAE,CAAA;EACxC,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI,EAAA,MAAM4C,WAAWA,CAAC;MAAEtL,OAAO;MAAEoK,QAAQ;EAAED,IAAAA,SAAS,GAAG0B,IAAI,CAACD,GAAG,EAAC;KAAG,EAAEK,SAAS,EAAE;EACxE,IAAA,MAAMC,eAAe,GAAG,MAAMtD,eAAe,CAACC,WAAW,CAAC7I,OAAO,CAACiJ,KAAK,EAAE,CAAC,CAAA;EAC1E,IAAA,MAAMzD,KAAK,GAAG;EACV2C,MAAAA,WAAW,EAAE+D,eAAe,CAAC5C,QAAQ,EAAE;EACvCa,MAAAA,SAAAA;OACH,CAAA;EACD;EACA,IAAA,IAAIC,QAAQ,EAAE;QACV5E,KAAK,CAAC4E,QAAQ,GAAGA,QAAQ,CAAA;EAC7B,KAAA;EACA,IAAA,QAAQ6B,SAAS;EACb,MAAA,KAAK,MAAM;EACP,QAAA,MAAM,IAAI,CAACf,WAAW,CAACtD,SAAS,CAACpC,KAAK,CAAC,CAAA;EACvC,QAAA,MAAA;EACJ,MAAA,KAAK,SAAS;EACV,QAAA,MAAM,IAAI,CAAC0F,WAAW,CAAC9C,YAAY,CAAC5C,KAAK,CAAC,CAAA;EAC1C,QAAA,MAAA;EACR,KAAA;EACA,IAA2C;EACvC2G,MAAAA,gBAAM,CAACC,GAAG,CAAE,CAAeC,aAAAA,EAAAA,gCAAc,CAACrM,OAAO,CAAC8I,GAAG,CAAE,QAAO,GACzD,CAAA,qCAAA,EAAuC,IAAI,CAAC8B,KAAM,IAAG,CAAC,CAAA;EAC/D,KAAA;EACA;EACA;EACA;MACA,IAAI,IAAI,CAACH,eAAe,EAAE;QACtB,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAAA;EACxC,KAAC,MACI;EACD,MAAA,MAAM,IAAI,CAAC4B,YAAY,EAAE,CAAA;EAC7B,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAMb,cAAcA,CAACQ,SAAS,EAAE;EAC5B,IAAA,MAAML,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE,CAAA;EACtB,IAAA,IAAIpG,KAAK,CAAA;EACT,IAAA,QAAQyG,SAAS;EACb,MAAA,KAAK,KAAK;UACNzG,KAAK,GAAG,MAAM,IAAI,CAAC0F,WAAW,CAAC5C,QAAQ,EAAE,CAAA;EACzC,QAAA,MAAA;EACJ,MAAA,KAAK,OAAO;UACR9C,KAAK,GAAG,MAAM,IAAI,CAAC0F,WAAW,CAAC1C,UAAU,EAAE,CAAA;EAC3C,QAAA,MAAA;EACR,KAAA;EACA,IAAA,IAAIhD,KAAK,EAAE;EACP;EACA;QACA,MAAMuG,oBAAoB,GAAG,IAAI,CAAChB,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAA;EAC/D,MAAA,IAAIa,GAAG,GAAGpG,KAAK,CAAC2E,SAAS,GAAG4B,oBAAoB,EAAE;EAC9C,QAAA,OAAO,IAAI,CAACN,cAAc,CAACQ,SAAS,CAAC,CAAA;EACzC,OAAA;QACA,OAAOjC,YAAY,CAACxE,KAAK,CAAC,CAAA;EAC9B,KAAC,MACI;EACD,MAAA,OAAO5D,SAAS,CAAA;EACpB,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMkJ,cAAcA,GAAG;EACnB,IAAA,IAAItF,KAAK,CAAA;MACT,OAAQA,KAAK,GAAG,MAAM,IAAI,CAACkG,YAAY,EAAE,EAAG;QACxC,IAAI;UACA,MAAMa,KAAK,CAAC/G,KAAK,CAACxF,OAAO,CAACiJ,KAAK,EAAE,CAAC,CAAA;EAClC,QAAA,IAAIuD,KAAoB,KAAK,YAAY,EAAE;EACvCL,UAAAA,gBAAM,CAACC,GAAG,CAAE,gBAAeC,gCAAc,CAAC7G,KAAK,CAACxF,OAAO,CAAC8I,GAAG,CAAE,IAAG,GAC3D,CAAA,4BAAA,EAA8B,IAAI,CAAC8B,KAAM,GAAE,CAAC,CAAA;EACrD,SAAA;SACH,CACD,OAAOpK,KAAK,EAAE;EACV,QAAA,MAAM,IAAI,CAAC+K,cAAc,CAAC/F,KAAK,CAAC,CAAA;EAChC,QAA2C;EACvC2G,UAAAA,gBAAM,CAACC,GAAG,CAAE,gBAAeC,gCAAc,CAAC7G,KAAK,CAACxF,OAAO,CAAC8I,GAAG,CAAE,IAAG,GAC3D,CAAA,4CAAA,EAA8C,IAAI,CAAC8B,KAAM,GAAE,CAAC,CAAA;EACrE,SAAA;EACA,QAAA,MAAM,IAAID,4BAAY,CAAC,qBAAqB,EAAE;YAAE5H,IAAI,EAAE,IAAI,CAAC6H,KAAAA;EAAM,SAAC,CAAC,CAAA;EACvE,OAAA;EACJ,KAAA;EACA,IAA2C;QACvCuB,gBAAM,CAACC,GAAG,CAAE,CAAyB,uBAAA,EAAA,IAAI,CAACrJ,IAAK,CAAA,oBAAA,CAAqB,GAC/D,CAAA,iCAAA,CAAkC,CAAC,CAAA;EAC5C,KAAA;EACJ,GAAA;EACA;EACJ;EACA;IACI,MAAMuJ,YAAYA,GAAG;EACjB;MACA,IAAI,MAAM,IAAIzH,IAAI,CAAC4H,YAAY,IAAI,CAAC,IAAI,CAACzB,kBAAkB,EAAE;QACzD,IAAI;EACA,QAAA,MAAMnG,IAAI,CAAC4H,YAAY,CAACC,IAAI,CAACC,QAAQ,CAAE,CAAA,EAAE/C,UAAW,CAAG,CAAA,EAAA,IAAI,CAACgB,KAAM,EAAC,CAAC,CAAA;SACvE,CACD,OAAOgC,GAAG,EAAE;EACR;EACA;EACA,QAA2C;YACvCT,gBAAM,CAACU,IAAI,CAAE,CAAqC,mCAAA,EAAA,IAAI,CAACjC,KAAM,CAAA,EAAA,CAAG,EAAEgC,GAAG,CAAC,CAAA;EAC1E,SAAA;EACJ,OAAA;EACJ,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACIzB,EAAAA,gBAAgBA,GAAG;EACf;MACA,IAAI,MAAM,IAAItG,IAAI,CAAC4H,YAAY,IAAI,CAAC,IAAI,CAACzB,kBAAkB,EAAE;EACzDnG,MAAAA,IAAI,CAAClE,gBAAgB,CAAC,MAAM,EAAG6C,KAAK,IAAK;UACrC,IAAIA,KAAK,CAACsJ,GAAG,KAAM,CAAA,EAAElD,UAAW,CAAA,CAAA,EAAG,IAAI,CAACgB,KAAM,CAAA,CAAC,EAAE;EAC7C,UAA2C;cACvCuB,gBAAM,CAACC,GAAG,CAAE,CAA2B5I,yBAAAA,EAAAA,KAAK,CAACsJ,GAAI,CAAA,EAAA,CAAG,GAAI,CAAA,iBAAA,CAAkB,CAAC,CAAA;EAC/E,WAAA;EACA,UAAA,MAAMC,YAAY,GAAG,YAAY;cAC7B,IAAI,CAACtC,eAAe,GAAG,IAAI,CAAA;EAC3B,YAAA,IAAIuC,SAAS,CAAA;cACb,IAAI;gBACA,MAAM,IAAI,CAACnC,OAAO,CAAC;EAAEoC,gBAAAA,KAAK,EAAE,IAAA;EAAK,eAAC,CAAC,CAAA;eACtC,CACD,OAAOzM,KAAK,EAAE;gBACV,IAAIA,KAAK,YAAY0M,KAAK,EAAE;EACxBF,gBAAAA,SAAS,GAAGxM,KAAK,CAAA;EACjB;EACA;EACA,gBAAA,MAAMwM,SAAS,CAAA;EACnB,eAAA;EACJ,aAAC,SACO;EACJ;EACA;EACA;EACA;EACA;EACA,cAAA,IAAI,IAAI,CAACtC,wBAAwB,IAC7B,EAAEsC,SAAS,IAAI,CAACxJ,KAAK,CAAC2J,UAAU,CAAC,EAAE;EACnC,gBAAA,MAAM,IAAI,CAACb,YAAY,EAAE,CAAA;EAC7B,eAAA;gBACA,IAAI,CAAC7B,eAAe,GAAG,KAAK,CAAA;gBAC5B,IAAI,CAACC,wBAAwB,GAAG,KAAK,CAAA;EACzC,aAAA;aACH,CAAA;EACDlH,UAAAA,KAAK,CAAC4J,SAAS,CAACL,YAAY,EAAE,CAAC,CAAA;EACnC,SAAA;EACJ,OAAC,CAAC,CAAA;EACN,KAAC,MACI;EACD,MAA2C;EACvCZ,QAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,uDAAA,CAAwD,CAAC,CAAA;EACzE,OAAA;EACA;EACA;EACA;QACA,KAAK,IAAI,CAACvB,OAAO,CAAC;EAAEoC,QAAAA,KAAK,EAAE,IAAA;EAAK,OAAC,CAAC,CAAA;EACtC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACI,WAAWI,WAAWA,GAAG;EACrB,IAAA,OAAOvD,UAAU,CAAA;EACrB,GAAA;EACJ;;EC/YA;EACA;AACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMwD,oBAAoB,CAAC;EACvB;EACJ;EACA;EACA;EACA;EACA;EACA;EACIjI,EAAAA,WAAWA,CAACtC,IAAI,EAAEwK,OAAO,EAAE;EACvB;EACR;EACA;EACA;EACA;MACQ,IAAI,CAACC,YAAY,GAAG,OAAO;EAAExN,MAAAA,OAAAA;EAAQ,KAAC,KAAK;EACvC,MAAA,MAAM,IAAI,CAACyN,MAAM,CAACrC,WAAW,CAAC;EAAEpL,QAAAA,OAAAA;EAAQ,OAAC,CAAC,CAAA;OAC7C,CAAA;MACD,IAAI,CAACyN,MAAM,GAAG,IAAIpD,KAAK,CAACtH,IAAI,EAAEwK,OAAO,CAAC,CAAA;EAC1C,GAAA;EACJ;;;;;;;;;;;;;"}
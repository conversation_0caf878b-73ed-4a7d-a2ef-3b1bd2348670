{"name": "workbox-range-requests", "version": "7.3.0", "license": "MIT", "author": "Google's Web DevRel Team and Google's Aurora Team", "description": "This library creates a new Response, given a source Response and a Range header value.", "repository": {"type": "git", "url": "git+https://github.com/googlechrome/workbox.git"}, "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "caching", "cache", "range", "media", "workbox-plugin"], "workbox": {"browserNamespace": "workbox.rangeRequests", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-core": "7.3.0"}, "gitHead": "c77dceb54d4af1749db95316710d6430e82b0c48"}
{"version": 3, "file": "workbox-range-requests.dev.js", "sources": ["../_version.js", "../utils/calculateEffectiveBoundaries.js", "../utils/parseRangeHeader.js", "../createPartialResponse.js", "../RangeRequestsPlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:range-requests:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {Blob} blob A source blob.\n * @param {number} [start] The offset to use as the start of the\n * slice.\n * @param {number} [end] The offset to use as the end of the slice.\n * @return {Object} An object with `start` and `end` properties, reflecting\n * the effective boundaries to use given the size of the blob.\n *\n * @private\n */\nfunction calculateEffectiveBoundaries(blob, start, end) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isInstance(blob, Blob, {\n            moduleName: 'workbox-range-requests',\n            funcName: 'calculateEffectiveBoundaries',\n            paramName: 'blob',\n        });\n    }\n    const blobSize = blob.size;\n    if ((end && end > blobSize) || (start && start < 0)) {\n        throw new WorkboxError('range-not-satisfiable', {\n            size: blobSize,\n            end,\n            start,\n        });\n    }\n    let effectiveStart;\n    let effectiveEnd;\n    if (start !== undefined && end !== undefined) {\n        effectiveStart = start;\n        // Range values are inclusive, so add 1 to the value.\n        effectiveEnd = end + 1;\n    }\n    else if (start !== undefined && end === undefined) {\n        effectiveStart = start;\n        effectiveEnd = blobSize;\n    }\n    else if (end !== undefined && start === undefined) {\n        effectiveStart = blobSize - end;\n        effectiveEnd = blobSize;\n    }\n    return {\n        start: effectiveStart,\n        end: effectiveEnd,\n    };\n}\nexport { calculateEffectiveBoundaries };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {string} rangeHeader A Range: header value.\n * @return {Object} An object with `start` and `end` properties, reflecting\n * the parsed value of the Range: header. If either the `start` or `end` are\n * omitted, then `null` will be returned.\n *\n * @private\n */\nfunction parseRangeHeader(rangeHeader) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(rangeHeader, 'string', {\n            moduleName: 'workbox-range-requests',\n            funcName: 'parseRangeHeader',\n            paramName: 'rangeHeader',\n        });\n    }\n    const normalizedRangeHeader = rangeHeader.trim().toLowerCase();\n    if (!normalizedRangeHeader.startsWith('bytes=')) {\n        throw new WorkboxError('unit-must-be-bytes', { normalizedRangeHeader });\n    }\n    // Specifying multiple ranges separate by commas is valid syntax, but this\n    // library only attempts to handle a single, contiguous sequence of bytes.\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Range#Syntax\n    if (normalizedRangeHeader.includes(',')) {\n        throw new WorkboxError('single-range-only', { normalizedRangeHeader });\n    }\n    const rangeParts = /(\\d*)-(\\d*)/.exec(normalizedRangeHeader);\n    // We need either at least one of the start or end values.\n    if (!rangeParts || !(rangeParts[1] || rangeParts[2])) {\n        throw new WorkboxError('invalid-range-values', { normalizedRangeHeader });\n    }\n    return {\n        start: rangeParts[1] === '' ? undefined : Number(rangeParts[1]),\n        end: rangeParts[2] === '' ? undefined : Number(rangeParts[2]),\n    };\n}\nexport { parseRangeHeader };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { calculateEffectiveBoundaries } from './utils/calculateEffectiveBoundaries.js';\nimport { parseRangeHeader } from './utils/parseRangeHeader.js';\nimport './_version.js';\n/**\n * Given a `Request` and `Response` objects as input, this will return a\n * promise for a new `Response`.\n *\n * If the original `Response` already contains partial content (i.e. it has\n * a status of 206), then this assumes it already fulfills the `Range:`\n * requirements, and will return it as-is.\n *\n * @param {Request} request A request, which should contain a Range:\n * header.\n * @param {Response} originalResponse A response.\n * @return {Promise<Response>} Either a `206 Partial Content` response, with\n * the response body set to the slice of content specified by the request's\n * `Range:` header, or a `416 Range Not Satisfiable` response if the\n * conditions of the `Range:` header can't be met.\n *\n * @memberof workbox-range-requests\n */\nasync function createPartialResponse(request, originalResponse) {\n    try {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-range-requests',\n                funcName: 'createPartialResponse',\n                paramName: 'request',\n            });\n            assert.isInstance(originalResponse, Response, {\n                moduleName: 'workbox-range-requests',\n                funcName: 'createPartialResponse',\n                paramName: 'originalResponse',\n            });\n        }\n        if (originalResponse.status === 206) {\n            // If we already have a 206, then just pass it through as-is;\n            // see https://github.com/GoogleChrome/workbox/issues/1720\n            return originalResponse;\n        }\n        const rangeHeader = request.headers.get('range');\n        if (!rangeHeader) {\n            throw new WorkboxError('no-range-header');\n        }\n        const boundaries = parseRangeHeader(rangeHeader);\n        const originalBlob = await originalResponse.blob();\n        const effectiveBoundaries = calculateEffectiveBoundaries(originalBlob, boundaries.start, boundaries.end);\n        const slicedBlob = originalBlob.slice(effectiveBoundaries.start, effectiveBoundaries.end);\n        const slicedBlobSize = slicedBlob.size;\n        const slicedResponse = new Response(slicedBlob, {\n            // Status code 206 is for a Partial Content response.\n            // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/206\n            status: 206,\n            statusText: 'Partial Content',\n            headers: originalResponse.headers,\n        });\n        slicedResponse.headers.set('Content-Length', String(slicedBlobSize));\n        slicedResponse.headers.set('Content-Range', `bytes ${effectiveBoundaries.start}-${effectiveBoundaries.end - 1}/` +\n            `${originalBlob.size}`);\n        return slicedResponse;\n    }\n    catch (error) {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.warn(`Unable to construct a partial response; returning a ` +\n                `416 Range Not Satisfiable response instead.`);\n            logger.groupCollapsed(`View details here.`);\n            logger.log(error);\n            logger.log(request);\n            logger.log(originalResponse);\n            logger.groupEnd();\n        }\n        return new Response('', {\n            status: 416,\n            statusText: 'Range Not Satisfiable',\n        });\n    }\n}\nexport { createPartialResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { createPartialResponse } from './createPartialResponse.js';\nimport './_version.js';\n/**\n * The range request plugin makes it easy for a request with a 'Range' header to\n * be fulfilled by a cached response.\n *\n * It does this by intercepting the `cachedResponseWillBeUsed` plugin callback\n * and returning the appropriate subset of the cached response body.\n *\n * @memberof workbox-range-requests\n */\nclass RangeRequestsPlugin {\n    constructor() {\n        /**\n         * @param {Object} options\n         * @param {Request} options.request The original request, which may or may not\n         * contain a Range: header.\n         * @param {Response} options.cachedResponse The complete cached response.\n         * @return {Promise<Response>} If request contains a 'Range' header, then a\n         * new response with status 206 whose body is a subset of `cachedResponse` is\n         * returned. Otherwise, `cachedResponse` is returned as-is.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ request, cachedResponse, }) => {\n            // Only return a sliced response if there's something valid in the cache,\n            // and there's a Range: header in the request.\n            if (cachedResponse && request.headers.has('range')) {\n                return await createPartialResponse(request, cachedResponse);\n            }\n            // If there was no Range: header, or if cachedResponse wasn't valid, just\n            // pass it through as-is.\n            return cachedResponse;\n        };\n    }\n}\nexport { RangeRequestsPlugin };\n"], "names": ["self", "_", "e", "calculateEffectiveBoundaries", "blob", "start", "end", "assert", "isInstance", "Blob", "moduleName", "funcName", "paramName", "blobSize", "size", "WorkboxError", "effectiveStart", "effectiveEnd", "undefined", "parseRange<PERSON><PERSON>er", "rangeHeader", "isType", "normalizedRangeHeader", "trim", "toLowerCase", "startsWith", "includes", "rangeParts", "exec", "Number", "createPartialResponse", "request", "originalResponse", "process", "Request", "Response", "status", "headers", "get", "boundaries", "originalBlob", "effectiveBoundaries", "slicedBlob", "slice", "slicedBlobSize", "slicedResponse", "statusText", "set", "String", "error", "logger", "warn", "groupCollapsed", "log", "groupEnd", "RangeRequestsPlugin", "constructor", "cachedResponseWillBeUsed", "cachedResponse", "has"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,8BAA8B,CAAC,IAAIC,CAAC,EAAE,CAAA;IAC/C,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,4BAA4BA,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;IACpD,EAA2C;IACvCC,IAAAA,gBAAM,CAACC,UAAU,CAACJ,IAAI,EAAEK,IAAI,EAAE;IAC1BC,MAAAA,UAAU,EAAE,wBAAwB;IACpCC,MAAAA,QAAQ,EAAE,8BAA8B;IACxCC,MAAAA,SAAS,EAAE,MAAA;IACf,KAAC,CAAC,CAAA;IACN,GAAA;IACA,EAAA,MAAMC,QAAQ,GAAGT,IAAI,CAACU,IAAI,CAAA;MAC1B,IAAKR,GAAG,IAAIA,GAAG,GAAGO,QAAQ,IAAMR,KAAK,IAAIA,KAAK,GAAG,CAAE,EAAE;IACjD,IAAA,MAAM,IAAIU,4BAAY,CAAC,uBAAuB,EAAE;IAC5CD,MAAAA,IAAI,EAAED,QAAQ;UACdP,GAAG;IACHD,MAAAA,KAAAA;IACJ,KAAC,CAAC,CAAA;IACN,GAAA;IACA,EAAA,IAAIW,cAAc,CAAA;IAClB,EAAA,IAAIC,YAAY,CAAA;IAChB,EAAA,IAAIZ,KAAK,KAAKa,SAAS,IAAIZ,GAAG,KAAKY,SAAS,EAAE;IAC1CF,IAAAA,cAAc,GAAGX,KAAK,CAAA;IACtB;QACAY,YAAY,GAAGX,GAAG,GAAG,CAAC,CAAA;OACzB,MACI,IAAID,KAAK,KAAKa,SAAS,IAAIZ,GAAG,KAAKY,SAAS,EAAE;IAC/CF,IAAAA,cAAc,GAAGX,KAAK,CAAA;IACtBY,IAAAA,YAAY,GAAGJ,QAAQ,CAAA;OAC1B,MACI,IAAIP,GAAG,KAAKY,SAAS,IAAIb,KAAK,KAAKa,SAAS,EAAE;QAC/CF,cAAc,GAAGH,QAAQ,GAAGP,GAAG,CAAA;IAC/BW,IAAAA,YAAY,GAAGJ,QAAQ,CAAA;IAC3B,GAAA;MACA,OAAO;IACHR,IAAAA,KAAK,EAAEW,cAAc;IACrBV,IAAAA,GAAG,EAAEW,YAAAA;OACR,CAAA;IACL;;ICvDA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASE,gBAAgBA,CAACC,WAAW,EAAE;IACnC,EAA2C;IACvCb,IAAAA,gBAAM,CAACc,MAAM,CAACD,WAAW,EAAE,QAAQ,EAAE;IACjCV,MAAAA,UAAU,EAAE,wBAAwB;IACpCC,MAAAA,QAAQ,EAAE,kBAAkB;IAC5BC,MAAAA,SAAS,EAAE,aAAA;IACf,KAAC,CAAC,CAAA;IACN,GAAA;MACA,MAAMU,qBAAqB,GAAGF,WAAW,CAACG,IAAI,EAAE,CAACC,WAAW,EAAE,CAAA;IAC9D,EAAA,IAAI,CAACF,qBAAqB,CAACG,UAAU,CAAC,QAAQ,CAAC,EAAE;IAC7C,IAAA,MAAM,IAAIV,4BAAY,CAAC,oBAAoB,EAAE;IAAEO,MAAAA,qBAAAA;IAAsB,KAAC,CAAC,CAAA;IAC3E,GAAA;IACA;IACA;IACA;IACA,EAAA,IAAIA,qBAAqB,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrC,IAAA,MAAM,IAAIX,4BAAY,CAAC,mBAAmB,EAAE;IAAEO,MAAAA,qBAAAA;IAAsB,KAAC,CAAC,CAAA;IAC1E,GAAA;IACA,EAAA,MAAMK,UAAU,GAAG,aAAa,CAACC,IAAI,CAACN,qBAAqB,CAAC,CAAA;IAC5D;IACA,EAAA,IAAI,CAACK,UAAU,IAAI,EAAEA,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IAClD,IAAA,MAAM,IAAIZ,4BAAY,CAAC,sBAAsB,EAAE;IAAEO,MAAAA,qBAAAA;IAAsB,KAAC,CAAC,CAAA;IAC7E,GAAA;MACA,OAAO;IACHjB,IAAAA,KAAK,EAAEsB,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,GAAGT,SAAS,GAAGW,MAAM,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/DrB,IAAAA,GAAG,EAAEqB,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,GAAGT,SAAS,GAAGW,MAAM,CAACF,UAAU,CAAC,CAAC,CAAC,CAAA;OAC/D,CAAA;IACL;;IC7CA;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,eAAeG,qBAAqBA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;MAC5D,IAAI;IACA,IAAA,IAAIC,KAAoB,KAAK,YAAY,EAAE;IACvC1B,MAAAA,gBAAM,CAACC,UAAU,CAACuB,OAAO,EAAEG,OAAO,EAAE;IAChCxB,QAAAA,UAAU,EAAE,wBAAwB;IACpCC,QAAAA,QAAQ,EAAE,uBAAuB;IACjCC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACFL,MAAAA,gBAAM,CAACC,UAAU,CAACwB,gBAAgB,EAAEG,QAAQ,EAAE;IAC1CzB,QAAAA,UAAU,EAAE,wBAAwB;IACpCC,QAAAA,QAAQ,EAAE,uBAAuB;IACjCC,QAAAA,SAAS,EAAE,kBAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,IAAIoB,gBAAgB,CAACI,MAAM,KAAK,GAAG,EAAE;IACjC;IACA;IACA,MAAA,OAAOJ,gBAAgB,CAAA;IAC3B,KAAA;QACA,MAAMZ,WAAW,GAAGW,OAAO,CAACM,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,CAAA;QAChD,IAAI,CAAClB,WAAW,EAAE;IACd,MAAA,MAAM,IAAIL,4BAAY,CAAC,iBAAiB,CAAC,CAAA;IAC7C,KAAA;IACA,IAAA,MAAMwB,UAAU,GAAGpB,gBAAgB,CAACC,WAAW,CAAC,CAAA;IAChD,IAAA,MAAMoB,YAAY,GAAG,MAAMR,gBAAgB,CAAC5B,IAAI,EAAE,CAAA;IAClD,IAAA,MAAMqC,mBAAmB,GAAGtC,4BAA4B,CAACqC,YAAY,EAAED,UAAU,CAAClC,KAAK,EAAEkC,UAAU,CAACjC,GAAG,CAAC,CAAA;IACxG,IAAA,MAAMoC,UAAU,GAAGF,YAAY,CAACG,KAAK,CAACF,mBAAmB,CAACpC,KAAK,EAAEoC,mBAAmB,CAACnC,GAAG,CAAC,CAAA;IACzF,IAAA,MAAMsC,cAAc,GAAGF,UAAU,CAAC5B,IAAI,CAAA;IACtC,IAAA,MAAM+B,cAAc,GAAG,IAAIV,QAAQ,CAACO,UAAU,EAAE;IAC5C;IACA;IACAN,MAAAA,MAAM,EAAE,GAAG;IACXU,MAAAA,UAAU,EAAE,iBAAiB;UAC7BT,OAAO,EAAEL,gBAAgB,CAACK,OAAAA;IAC9B,KAAC,CAAC,CAAA;QACFQ,cAAc,CAACR,OAAO,CAACU,GAAG,CAAC,gBAAgB,EAAEC,MAAM,CAACJ,cAAc,CAAC,CAAC,CAAA;QACpEC,cAAc,CAACR,OAAO,CAACU,GAAG,CAAC,eAAe,EAAG,CAAA,MAAA,EAAQN,mBAAmB,CAACpC,KAAM,CAAA,CAAA,EAAGoC,mBAAmB,CAACnC,GAAG,GAAG,CAAE,CAAE,CAAA,CAAA,GAC3G,GAAEkC,YAAY,CAAC1B,IAAK,CAAA,CAAC,CAAC,CAAA;IAC3B,IAAA,OAAO+B,cAAc,CAAA;OACxB,CACD,OAAOI,KAAK,EAAE;IACV,IAA2C;IACvCC,MAAAA,gBAAM,CAACC,IAAI,CAAE,CAAqD,oDAAA,CAAA,GAC7D,6CAA4C,CAAC,CAAA;IAClDD,MAAAA,gBAAM,CAACE,cAAc,CAAE,CAAA,kBAAA,CAAmB,CAAC,CAAA;IAC3CF,MAAAA,gBAAM,CAACG,GAAG,CAACJ,KAAK,CAAC,CAAA;IACjBC,MAAAA,gBAAM,CAACG,GAAG,CAACtB,OAAO,CAAC,CAAA;IACnBmB,MAAAA,gBAAM,CAACG,GAAG,CAACrB,gBAAgB,CAAC,CAAA;UAC5BkB,gBAAM,CAACI,QAAQ,EAAE,CAAA;IACrB,KAAA;IACA,IAAA,OAAO,IAAInB,QAAQ,CAAC,EAAE,EAAE;IACpBC,MAAAA,MAAM,EAAE,GAAG;IACXU,MAAAA,UAAU,EAAE,uBAAA;IAChB,KAAC,CAAC,CAAA;IACN,GAAA;IACJ;;ICtFA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMS,mBAAmB,CAAC;IACtBC,EAAAA,WAAWA,GAAG;IACV;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;QACQ,IAAI,CAACC,wBAAwB,GAAG,OAAO;UAAE1B,OAAO;IAAE2B,MAAAA,cAAAA;IAAgB,KAAC,KAAK;IACpE;IACA;UACA,IAAIA,cAAc,IAAI3B,OAAO,CAACM,OAAO,CAACsB,GAAG,CAAC,OAAO,CAAC,EAAE;IAChD,QAAA,OAAO,MAAM7B,qBAAqB,CAACC,OAAO,EAAE2B,cAAc,CAAC,CAAA;IAC/D,OAAA;IACA;IACA;IACA,MAAA,OAAOA,cAAc,CAAA;SACxB,CAAA;IACL,GAAA;IACJ;;;;;;;;;;;"}
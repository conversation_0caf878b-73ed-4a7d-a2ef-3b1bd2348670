/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.file-input{cursor:pointer;border:var(--border)solid #0000;cursor:pointer;appearance:none;background-color:var(--color-base-100);vertical-align:middle;-webkit-user-select:none;user-select:none;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));align-items:center;padding-inline-end:.75rem;font-size:.875rem;line-height:2;display:inline-flex;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;height:calc(100% + var(--border)*2);margin-inline-end:1rem;margin-block:calc(var(--border)*-1);color:var(--btn-fg);border-width:var(--border);border-style:solid;border-color:var(--btn-border);background-color:var(--btn-bg);background-size:calc(var(--noise)*100%);background-image:var(--btn-noise);text-shadow:0 .5px oklch(1 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px color-mix(in oklab,color-mix(in oklab,white 30%,var(--btn-bg))calc(var(--depth)*20%),#0000)inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-border:color-mix(in oklab,var(--btn-bg),#000 5%);--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)30%,#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)30%,#0000);--btn-noise:var(--fx-noise);border-start-start-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-start-radius:calc(var(--join-es,var(--radius-field) - var(--border)));margin-inline-start:calc(var(--border)*-1);padding-inline:1rem;font-size:.875rem;font-weight:600}&:focus{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)10%,#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);box-shadow:none;color:color-mix(in oklch,var(--color-base-content)20%,#0000);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}&::file-selector-button{cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);--btn-border:#0000;--btn-noise:none;--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}}.file-input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;border-start-end-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-end-radius:calc(var(--join-es,var(--radius-field) - var(--border)));height:100%;margin-block:0;margin-inline:0 1rem;padding-inline:1rem}&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.file-input-neutral{--btn-color:var(--color-neutral);&::file-selector-button{color:var(--color-neutral-content)}&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.file-input-primary{--btn-color:var(--color-primary);&::file-selector-button{color:var(--color-primary-content)}&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.file-input-secondary{--btn-color:var(--color-secondary);&::file-selector-button{color:var(--color-secondary-content)}&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.file-input-accent{--btn-color:var(--color-accent);&::file-selector-button{color:var(--color-accent-content)}&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.file-input-info{--btn-color:var(--color-info);&::file-selector-button{color:var(--color-info-content)}&,&:focus,&:focus-within{--input-color:var(--color-info)}}.file-input-success{--btn-color:var(--color-success);&::file-selector-button{color:var(--color-success-content)}&,&:focus,&:focus-within{--input-color:var(--color-success)}}.file-input-warning{--btn-color:var(--color-warning);&::file-selector-button{color:var(--color-warning-content)}&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.file-input-error{--btn-color:var(--color-error);&::file-selector-button{color:var(--color-error-content)}&,&:focus,&:focus-within{--input-color:var(--color-error)}}.file-input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;line-height:1rem;&::file-selector-button{font-size:.6875rem}}.file-input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;line-height:1.5rem;&::file-selector-button{font-size:.75rem}}.file-input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;line-height:2;&::file-selector-button{font-size:.875rem}}.file-input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;line-height:2.5rem;&::file-selector-button{font-size:1.125rem}}.file-input-xl{--size:calc(var(--size-field,.25rem)*14);padding-inline-end:1.5rem;font-size:1.125rem;line-height:3rem;&::file-selector-button{font-size:1.375rem}}@media (width>=640px){.sm\:file-input{cursor:pointer;border:var(--border)solid #0000;cursor:pointer;appearance:none;background-color:var(--color-base-100);vertical-align:middle;-webkit-user-select:none;user-select:none;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));align-items:center;padding-inline-end:.75rem;font-size:.875rem;line-height:2;display:inline-flex;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;height:calc(100% + var(--border)*2);margin-inline-end:1rem;margin-block:calc(var(--border)*-1);color:var(--btn-fg);border-width:var(--border);border-style:solid;border-color:var(--btn-border);background-color:var(--btn-bg);background-size:calc(var(--noise)*100%);background-image:var(--btn-noise);text-shadow:0 .5px oklch(1 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px color-mix(in oklab,color-mix(in oklab,white 30%,var(--btn-bg))calc(var(--depth)*20%),#0000)inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-border:color-mix(in oklab,var(--btn-bg),#000 5%);--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)30%,#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)30%,#0000);--btn-noise:var(--fx-noise);border-start-start-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-start-radius:calc(var(--join-es,var(--radius-field) - var(--border)));margin-inline-start:calc(var(--border)*-1);padding-inline:1rem;font-size:.875rem;font-weight:600}&:focus{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)10%,#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);box-shadow:none;color:color-mix(in oklch,var(--color-base-content)20%,#0000);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}&::file-selector-button{cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);--btn-border:#0000;--btn-noise:none;--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}}.sm\:file-input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;border-start-end-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-end-radius:calc(var(--join-es,var(--radius-field) - var(--border)));height:100%;margin-block:0;margin-inline:0 1rem;padding-inline:1rem}&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.sm\:file-input-neutral{--btn-color:var(--color-neutral);&::file-selector-button{color:var(--color-neutral-content)}&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.sm\:file-input-primary{--btn-color:var(--color-primary);&::file-selector-button{color:var(--color-primary-content)}&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.sm\:file-input-secondary{--btn-color:var(--color-secondary);&::file-selector-button{color:var(--color-secondary-content)}&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.sm\:file-input-accent{--btn-color:var(--color-accent);&::file-selector-button{color:var(--color-accent-content)}&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.sm\:file-input-info{--btn-color:var(--color-info);&::file-selector-button{color:var(--color-info-content)}&,&:focus,&:focus-within{--input-color:var(--color-info)}}.sm\:file-input-success{--btn-color:var(--color-success);&::file-selector-button{color:var(--color-success-content)}&,&:focus,&:focus-within{--input-color:var(--color-success)}}.sm\:file-input-warning{--btn-color:var(--color-warning);&::file-selector-button{color:var(--color-warning-content)}&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.sm\:file-input-error{--btn-color:var(--color-error);&::file-selector-button{color:var(--color-error-content)}&,&:focus,&:focus-within{--input-color:var(--color-error)}}.sm\:file-input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;line-height:1rem;&::file-selector-button{font-size:.6875rem}}.sm\:file-input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;line-height:1.5rem;&::file-selector-button{font-size:.75rem}}.sm\:file-input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;line-height:2;&::file-selector-button{font-size:.875rem}}.sm\:file-input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;line-height:2.5rem;&::file-selector-button{font-size:1.125rem}}.sm\:file-input-xl{--size:calc(var(--size-field,.25rem)*14);padding-inline-end:1.5rem;font-size:1.125rem;line-height:3rem;&::file-selector-button{font-size:1.375rem}}}@media (width>=768px){.md\:file-input{cursor:pointer;border:var(--border)solid #0000;cursor:pointer;appearance:none;background-color:var(--color-base-100);vertical-align:middle;-webkit-user-select:none;user-select:none;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));align-items:center;padding-inline-end:.75rem;font-size:.875rem;line-height:2;display:inline-flex;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;height:calc(100% + var(--border)*2);margin-inline-end:1rem;margin-block:calc(var(--border)*-1);color:var(--btn-fg);border-width:var(--border);border-style:solid;border-color:var(--btn-border);background-color:var(--btn-bg);background-size:calc(var(--noise)*100%);background-image:var(--btn-noise);text-shadow:0 .5px oklch(1 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px color-mix(in oklab,color-mix(in oklab,white 30%,var(--btn-bg))calc(var(--depth)*20%),#0000)inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-border:color-mix(in oklab,var(--btn-bg),#000 5%);--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)30%,#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)30%,#0000);--btn-noise:var(--fx-noise);border-start-start-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-start-radius:calc(var(--join-es,var(--radius-field) - var(--border)));margin-inline-start:calc(var(--border)*-1);padding-inline:1rem;font-size:.875rem;font-weight:600}&:focus{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)10%,#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);box-shadow:none;color:color-mix(in oklch,var(--color-base-content)20%,#0000);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}&::file-selector-button{cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);--btn-border:#0000;--btn-noise:none;--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}}.md\:file-input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;border-start-end-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-end-radius:calc(var(--join-es,var(--radius-field) - var(--border)));height:100%;margin-block:0;margin-inline:0 1rem;padding-inline:1rem}&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.md\:file-input-neutral{--btn-color:var(--color-neutral);&::file-selector-button{color:var(--color-neutral-content)}&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.md\:file-input-primary{--btn-color:var(--color-primary);&::file-selector-button{color:var(--color-primary-content)}&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.md\:file-input-secondary{--btn-color:var(--color-secondary);&::file-selector-button{color:var(--color-secondary-content)}&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.md\:file-input-accent{--btn-color:var(--color-accent);&::file-selector-button{color:var(--color-accent-content)}&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.md\:file-input-info{--btn-color:var(--color-info);&::file-selector-button{color:var(--color-info-content)}&,&:focus,&:focus-within{--input-color:var(--color-info)}}.md\:file-input-success{--btn-color:var(--color-success);&::file-selector-button{color:var(--color-success-content)}&,&:focus,&:focus-within{--input-color:var(--color-success)}}.md\:file-input-warning{--btn-color:var(--color-warning);&::file-selector-button{color:var(--color-warning-content)}&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.md\:file-input-error{--btn-color:var(--color-error);&::file-selector-button{color:var(--color-error-content)}&,&:focus,&:focus-within{--input-color:var(--color-error)}}.md\:file-input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;line-height:1rem;&::file-selector-button{font-size:.6875rem}}.md\:file-input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;line-height:1.5rem;&::file-selector-button{font-size:.75rem}}.md\:file-input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;line-height:2;&::file-selector-button{font-size:.875rem}}.md\:file-input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;line-height:2.5rem;&::file-selector-button{font-size:1.125rem}}.md\:file-input-xl{--size:calc(var(--size-field,.25rem)*14);padding-inline-end:1.5rem;font-size:1.125rem;line-height:3rem;&::file-selector-button{font-size:1.375rem}}}@media (width>=1024px){.lg\:file-input{cursor:pointer;border:var(--border)solid #0000;cursor:pointer;appearance:none;background-color:var(--color-base-100);vertical-align:middle;-webkit-user-select:none;user-select:none;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));align-items:center;padding-inline-end:.75rem;font-size:.875rem;line-height:2;display:inline-flex;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;height:calc(100% + var(--border)*2);margin-inline-end:1rem;margin-block:calc(var(--border)*-1);color:var(--btn-fg);border-width:var(--border);border-style:solid;border-color:var(--btn-border);background-color:var(--btn-bg);background-size:calc(var(--noise)*100%);background-image:var(--btn-noise);text-shadow:0 .5px oklch(1 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px color-mix(in oklab,color-mix(in oklab,white 30%,var(--btn-bg))calc(var(--depth)*20%),#0000)inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-border:color-mix(in oklab,var(--btn-bg),#000 5%);--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)30%,#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)30%,#0000);--btn-noise:var(--fx-noise);border-start-start-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-start-radius:calc(var(--join-es,var(--radius-field) - var(--border)));margin-inline-start:calc(var(--border)*-1);padding-inline:1rem;font-size:.875rem;font-weight:600}&:focus{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)10%,#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);box-shadow:none;color:color-mix(in oklch,var(--color-base-content)20%,#0000);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}&::file-selector-button{cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);--btn-border:#0000;--btn-noise:none;--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}}.lg\:file-input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;border-start-end-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-end-radius:calc(var(--join-es,var(--radius-field) - var(--border)));height:100%;margin-block:0;margin-inline:0 1rem;padding-inline:1rem}&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.lg\:file-input-neutral{--btn-color:var(--color-neutral);&::file-selector-button{color:var(--color-neutral-content)}&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.lg\:file-input-primary{--btn-color:var(--color-primary);&::file-selector-button{color:var(--color-primary-content)}&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.lg\:file-input-secondary{--btn-color:var(--color-secondary);&::file-selector-button{color:var(--color-secondary-content)}&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.lg\:file-input-accent{--btn-color:var(--color-accent);&::file-selector-button{color:var(--color-accent-content)}&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.lg\:file-input-info{--btn-color:var(--color-info);&::file-selector-button{color:var(--color-info-content)}&,&:focus,&:focus-within{--input-color:var(--color-info)}}.lg\:file-input-success{--btn-color:var(--color-success);&::file-selector-button{color:var(--color-success-content)}&,&:focus,&:focus-within{--input-color:var(--color-success)}}.lg\:file-input-warning{--btn-color:var(--color-warning);&::file-selector-button{color:var(--color-warning-content)}&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.lg\:file-input-error{--btn-color:var(--color-error);&::file-selector-button{color:var(--color-error-content)}&,&:focus,&:focus-within{--input-color:var(--color-error)}}.lg\:file-input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;line-height:1rem;&::file-selector-button{font-size:.6875rem}}.lg\:file-input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;line-height:1.5rem;&::file-selector-button{font-size:.75rem}}.lg\:file-input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;line-height:2;&::file-selector-button{font-size:.875rem}}.lg\:file-input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;line-height:2.5rem;&::file-selector-button{font-size:1.125rem}}.lg\:file-input-xl{--size:calc(var(--size-field,.25rem)*14);padding-inline-end:1.5rem;font-size:1.125rem;line-height:3rem;&::file-selector-button{font-size:1.375rem}}}@media (width>=1280px){.xl\:file-input{cursor:pointer;border:var(--border)solid #0000;cursor:pointer;appearance:none;background-color:var(--color-base-100);vertical-align:middle;-webkit-user-select:none;user-select:none;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));align-items:center;padding-inline-end:.75rem;font-size:.875rem;line-height:2;display:inline-flex;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;height:calc(100% + var(--border)*2);margin-inline-end:1rem;margin-block:calc(var(--border)*-1);color:var(--btn-fg);border-width:var(--border);border-style:solid;border-color:var(--btn-border);background-color:var(--btn-bg);background-size:calc(var(--noise)*100%);background-image:var(--btn-noise);text-shadow:0 .5px oklch(1 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px color-mix(in oklab,color-mix(in oklab,white 30%,var(--btn-bg))calc(var(--depth)*20%),#0000)inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-border:color-mix(in oklab,var(--btn-bg),#000 5%);--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)30%,#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)30%,#0000);--btn-noise:var(--fx-noise);border-start-start-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-start-radius:calc(var(--join-es,var(--radius-field) - var(--border)));margin-inline-start:calc(var(--border)*-1);padding-inline:1rem;font-size:.875rem;font-weight:600}&:focus{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)10%,#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);box-shadow:none;color:color-mix(in oklch,var(--color-base-content)20%,#0000);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}&::file-selector-button{cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);--btn-border:#0000;--btn-noise:none;--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}}.xl\:file-input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;border-start-end-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-end-radius:calc(var(--join-es,var(--radius-field) - var(--border)));height:100%;margin-block:0;margin-inline:0 1rem;padding-inline:1rem}&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.xl\:file-input-neutral{--btn-color:var(--color-neutral);&::file-selector-button{color:var(--color-neutral-content)}&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.xl\:file-input-primary{--btn-color:var(--color-primary);&::file-selector-button{color:var(--color-primary-content)}&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.xl\:file-input-secondary{--btn-color:var(--color-secondary);&::file-selector-button{color:var(--color-secondary-content)}&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.xl\:file-input-accent{--btn-color:var(--color-accent);&::file-selector-button{color:var(--color-accent-content)}&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.xl\:file-input-info{--btn-color:var(--color-info);&::file-selector-button{color:var(--color-info-content)}&,&:focus,&:focus-within{--input-color:var(--color-info)}}.xl\:file-input-success{--btn-color:var(--color-success);&::file-selector-button{color:var(--color-success-content)}&,&:focus,&:focus-within{--input-color:var(--color-success)}}.xl\:file-input-warning{--btn-color:var(--color-warning);&::file-selector-button{color:var(--color-warning-content)}&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.xl\:file-input-error{--btn-color:var(--color-error);&::file-selector-button{color:var(--color-error-content)}&,&:focus,&:focus-within{--input-color:var(--color-error)}}.xl\:file-input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;line-height:1rem;&::file-selector-button{font-size:.6875rem}}.xl\:file-input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;line-height:1.5rem;&::file-selector-button{font-size:.75rem}}.xl\:file-input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;line-height:2;&::file-selector-button{font-size:.875rem}}.xl\:file-input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;line-height:2.5rem;&::file-selector-button{font-size:1.125rem}}.xl\:file-input-xl{--size:calc(var(--size-field,.25rem)*14);padding-inline-end:1.5rem;font-size:1.125rem;line-height:3rem;&::file-selector-button{font-size:1.375rem}}}@media (width>=1536px){.\32 xl\:file-input{cursor:pointer;border:var(--border)solid #0000;cursor:pointer;appearance:none;background-color:var(--color-base-100);vertical-align:middle;-webkit-user-select:none;user-select:none;width:clamp(3rem,20rem,100%);height:var(--size);border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--size:calc(var(--size-field,.25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));align-items:center;padding-inline-end:.75rem;font-size:.875rem;line-height:2;display:inline-flex;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;height:calc(100% + var(--border)*2);margin-inline-end:1rem;margin-block:calc(var(--border)*-1);color:var(--btn-fg);border-width:var(--border);border-style:solid;border-color:var(--btn-border);background-color:var(--btn-bg);background-size:calc(var(--noise)*100%);background-image:var(--btn-noise);text-shadow:0 .5px oklch(1 0 0/calc(var(--depth)*.15));box-shadow:0 .5px 0 .5px color-mix(in oklab,color-mix(in oklab,white 30%,var(--btn-bg))calc(var(--depth)*20%),#0000)inset,var(--btn-shadow);--size:calc(var(--size-field,.25rem)*10);--btn-bg:var(--btn-color,var(--color-base-200));--btn-fg:var(--color-base-content);--btn-border:color-mix(in oklab,var(--btn-bg),#000 5%);--btn-shadow:0 3px 2px -2px color-mix(in oklab,var(--btn-bg)30%,#0000),0 4px 3px -2px color-mix(in oklab,var(--btn-bg)30%,#0000);--btn-noise:var(--fx-noise);border-start-start-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-start-radius:calc(var(--join-es,var(--radius-field) - var(--border)));margin-inline-start:calc(var(--border)*-1);padding-inline:1rem;font-size:.875rem;font-weight:600}&:focus{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)10%,#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>input[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);box-shadow:none;color:color-mix(in oklch,var(--color-base-content)20%,#0000);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}&::file-selector-button{cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);--btn-border:#0000;--btn-noise:none;--btn-fg:color-mix(in oklch,var(--color-base-content)20%,#0000)}}}.\32 xl\:file-input-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&::file-selector-button{cursor:pointer;-webkit-user-select:none;user-select:none;border-start-end-radius:calc(var(--join-ss,var(--radius-field) - var(--border)));border-end-end-radius:calc(var(--join-es,var(--radius-field) - var(--border)));height:100%;margin-block:0;margin-inline:0 1rem;padding-inline:1rem}&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.\32 xl\:file-input-neutral{--btn-color:var(--color-neutral);&::file-selector-button{color:var(--color-neutral-content)}&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.\32 xl\:file-input-primary{--btn-color:var(--color-primary);&::file-selector-button{color:var(--color-primary-content)}&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.\32 xl\:file-input-secondary{--btn-color:var(--color-secondary);&::file-selector-button{color:var(--color-secondary-content)}&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.\32 xl\:file-input-accent{--btn-color:var(--color-accent);&::file-selector-button{color:var(--color-accent-content)}&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.\32 xl\:file-input-info{--btn-color:var(--color-info);&::file-selector-button{color:var(--color-info-content)}&,&:focus,&:focus-within{--input-color:var(--color-info)}}.\32 xl\:file-input-success{--btn-color:var(--color-success);&::file-selector-button{color:var(--color-success-content)}&,&:focus,&:focus-within{--input-color:var(--color-success)}}.\32 xl\:file-input-warning{--btn-color:var(--color-warning);&::file-selector-button{color:var(--color-warning-content)}&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.\32 xl\:file-input-error{--btn-color:var(--color-error);&::file-selector-button{color:var(--color-error-content)}&,&:focus,&:focus-within{--input-color:var(--color-error)}}.\32 xl\:file-input-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem;line-height:1rem;&::file-selector-button{font-size:.6875rem}}.\32 xl\:file-input-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem;line-height:1.5rem;&::file-selector-button{font-size:.75rem}}.\32 xl\:file-input-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem;line-height:2;&::file-selector-button{font-size:.875rem}}.\32 xl\:file-input-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem;line-height:2.5rem;&::file-selector-button{font-size:1.125rem}}.\32 xl\:file-input-xl{--size:calc(var(--size-field,.25rem)*14);padding-inline-end:1.5rem;font-size:1.125rem;line-height:3rem;&::file-selector-button{font-size:1.375rem}}}}
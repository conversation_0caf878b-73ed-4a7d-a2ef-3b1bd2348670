/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.tooltip{--tt-bg:var(--color-neutral);--tt-off:calc(100% + .5rem);--tt-tail:calc(100% + 1px + .25rem);display:inline-block;position:relative;&>:where(.tooltip-content),&:where([data-tip]):before{border-radius:var(--radius-field);text-align:center;white-space:normal;max-width:20rem;color:var(--color-neutral-content);opacity:0;background-color:var(--tt-bg);pointer-events:none;z-index:2;--tw-content:attr(data-tip);content:var(--tw-content);width:max-content;padding-block:.25rem;padding-inline:.5rem;font-size:.875rem;line-height:1.25;transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;position:absolute}&:after{opacity:0;background-color:var(--tt-bg);content:"";pointer-events:none;--mask-tooltip:url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");width:.625rem;height:.25rem;mask-position:-1px 0;mask-repeat:no-repeat;mask-image:var(--mask-tooltip);transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;display:block;position:absolute}}.tooltip,.tooltip-top{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-off)50%}}.tooltip-bottom{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem));inset:var(--tt-off)auto auto 50%}}.tooltip-left{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,.25rem) - .25rem))translateY(-50%);inset:50% var(--tt-off)auto auto}}.tooltip-right{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,-.25rem) + .25rem))translateY(-50%);inset:50% auto auto var(--tt-off)}}.tooltip{&.tooltip-open,&[data-tip]:not([data-tip=""]):hover,&:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover,&:has(:focus-visible){&>.tooltip-content,&[data-tip]:before,&:after{opacity:1;--tt-pos:0rem;transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1)}}}.tooltip,.tooltip-top{&:after{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-tail)50%}}.tooltip-bottom{&:after{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem))rotate(180deg);inset:var(--tt-tail)auto auto 50%}}.tooltip-left{&:after{transform:translateX(var(--tt-pos,.25rem))translateY(-50%)rotate(-90deg);inset:50% calc(var(--tt-tail) + 1px)auto auto}}.tooltip-right{&:after{transform:translateX(var(--tt-pos,-.25rem))translateY(-50%)rotate(90deg);inset:50% auto auto calc(var(--tt-tail) + 1px)}}.tooltip-primary{--tt-bg:var(--color-primary);&>.tooltip-content,&[data-tip]:before{color:var(--color-primary-content)}}.tooltip-secondary{--tt-bg:var(--color-secondary);&>.tooltip-content,&[data-tip]:before{color:var(--color-secondary-content)}}.tooltip-accent{--tt-bg:var(--color-accent);&>.tooltip-content,&[data-tip]:before{color:var(--color-accent-content)}}.tooltip-info{--tt-bg:var(--color-info);&>.tooltip-content,&[data-tip]:before{color:var(--color-info-content)}}.tooltip-success{--tt-bg:var(--color-success);&>.tooltip-content,&[data-tip]:before{color:var(--color-success-content)}}.tooltip-warning{--tt-bg:var(--color-warning);&>.tooltip-content,&[data-tip]:before{color:var(--color-warning-content)}}.tooltip-error{--tt-bg:var(--color-error);&>.tooltip-content,&[data-tip]:before{color:var(--color-error-content)}}@media (width>=640px){.sm\:tooltip{--tt-bg:var(--color-neutral);--tt-off:calc(100% + .5rem);--tt-tail:calc(100% + 1px + .25rem);display:inline-block;position:relative;&>:where(.tooltip-content),&:where([data-tip]):before{border-radius:var(--radius-field);text-align:center;white-space:normal;max-width:20rem;color:var(--color-neutral-content);opacity:0;background-color:var(--tt-bg);pointer-events:none;z-index:2;--tw-content:attr(data-tip);content:var(--tw-content);width:max-content;padding-block:.25rem;padding-inline:.5rem;font-size:.875rem;line-height:1.25;transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;position:absolute}&:after{opacity:0;background-color:var(--tt-bg);content:"";pointer-events:none;--mask-tooltip:url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");width:.625rem;height:.25rem;mask-position:-1px 0;mask-repeat:no-repeat;mask-image:var(--mask-tooltip);transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;display:block;position:absolute}}.sm\:tooltip,.sm\:tooltip-top{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-off)50%}}.sm\:tooltip-bottom{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem));inset:var(--tt-off)auto auto 50%}}.sm\:tooltip-left{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,.25rem) - .25rem))translateY(-50%);inset:50% var(--tt-off)auto auto}}.sm\:tooltip-right{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,-.25rem) + .25rem))translateY(-50%);inset:50% auto auto var(--tt-off)}}.sm\:tooltip{&.tooltip-open,&[data-tip]:not([data-tip=""]):hover,&:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover,&:has(:focus-visible){&>.tooltip-content,&[data-tip]:before,&:after{opacity:1;--tt-pos:0rem;transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1)}}}.sm\:tooltip,.sm\:tooltip-top{&:after{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-tail)50%}}.sm\:tooltip-bottom{&:after{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem))rotate(180deg);inset:var(--tt-tail)auto auto 50%}}.sm\:tooltip-left{&:after{transform:translateX(var(--tt-pos,.25rem))translateY(-50%)rotate(-90deg);inset:50% calc(var(--tt-tail) + 1px)auto auto}}.sm\:tooltip-right{&:after{transform:translateX(var(--tt-pos,-.25rem))translateY(-50%)rotate(90deg);inset:50% auto auto calc(var(--tt-tail) + 1px)}}.sm\:tooltip-primary{--tt-bg:var(--color-primary);&>.tooltip-content,&[data-tip]:before{color:var(--color-primary-content)}}.sm\:tooltip-secondary{--tt-bg:var(--color-secondary);&>.tooltip-content,&[data-tip]:before{color:var(--color-secondary-content)}}.sm\:tooltip-accent{--tt-bg:var(--color-accent);&>.tooltip-content,&[data-tip]:before{color:var(--color-accent-content)}}.sm\:tooltip-info{--tt-bg:var(--color-info);&>.tooltip-content,&[data-tip]:before{color:var(--color-info-content)}}.sm\:tooltip-success{--tt-bg:var(--color-success);&>.tooltip-content,&[data-tip]:before{color:var(--color-success-content)}}.sm\:tooltip-warning{--tt-bg:var(--color-warning);&>.tooltip-content,&[data-tip]:before{color:var(--color-warning-content)}}.sm\:tooltip-error{--tt-bg:var(--color-error);&>.tooltip-content,&[data-tip]:before{color:var(--color-error-content)}}}@media (width>=768px){.md\:tooltip{--tt-bg:var(--color-neutral);--tt-off:calc(100% + .5rem);--tt-tail:calc(100% + 1px + .25rem);display:inline-block;position:relative;&>:where(.tooltip-content),&:where([data-tip]):before{border-radius:var(--radius-field);text-align:center;white-space:normal;max-width:20rem;color:var(--color-neutral-content);opacity:0;background-color:var(--tt-bg);pointer-events:none;z-index:2;--tw-content:attr(data-tip);content:var(--tw-content);width:max-content;padding-block:.25rem;padding-inline:.5rem;font-size:.875rem;line-height:1.25;transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;position:absolute}&:after{opacity:0;background-color:var(--tt-bg);content:"";pointer-events:none;--mask-tooltip:url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");width:.625rem;height:.25rem;mask-position:-1px 0;mask-repeat:no-repeat;mask-image:var(--mask-tooltip);transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;display:block;position:absolute}}.md\:tooltip,.md\:tooltip-top{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-off)50%}}.md\:tooltip-bottom{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem));inset:var(--tt-off)auto auto 50%}}.md\:tooltip-left{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,.25rem) - .25rem))translateY(-50%);inset:50% var(--tt-off)auto auto}}.md\:tooltip-right{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,-.25rem) + .25rem))translateY(-50%);inset:50% auto auto var(--tt-off)}}.md\:tooltip{&.tooltip-open,&[data-tip]:not([data-tip=""]):hover,&:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover,&:has(:focus-visible){&>.tooltip-content,&[data-tip]:before,&:after{opacity:1;--tt-pos:0rem;transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1)}}}.md\:tooltip,.md\:tooltip-top{&:after{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-tail)50%}}.md\:tooltip-bottom{&:after{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem))rotate(180deg);inset:var(--tt-tail)auto auto 50%}}.md\:tooltip-left{&:after{transform:translateX(var(--tt-pos,.25rem))translateY(-50%)rotate(-90deg);inset:50% calc(var(--tt-tail) + 1px)auto auto}}.md\:tooltip-right{&:after{transform:translateX(var(--tt-pos,-.25rem))translateY(-50%)rotate(90deg);inset:50% auto auto calc(var(--tt-tail) + 1px)}}.md\:tooltip-primary{--tt-bg:var(--color-primary);&>.tooltip-content,&[data-tip]:before{color:var(--color-primary-content)}}.md\:tooltip-secondary{--tt-bg:var(--color-secondary);&>.tooltip-content,&[data-tip]:before{color:var(--color-secondary-content)}}.md\:tooltip-accent{--tt-bg:var(--color-accent);&>.tooltip-content,&[data-tip]:before{color:var(--color-accent-content)}}.md\:tooltip-info{--tt-bg:var(--color-info);&>.tooltip-content,&[data-tip]:before{color:var(--color-info-content)}}.md\:tooltip-success{--tt-bg:var(--color-success);&>.tooltip-content,&[data-tip]:before{color:var(--color-success-content)}}.md\:tooltip-warning{--tt-bg:var(--color-warning);&>.tooltip-content,&[data-tip]:before{color:var(--color-warning-content)}}.md\:tooltip-error{--tt-bg:var(--color-error);&>.tooltip-content,&[data-tip]:before{color:var(--color-error-content)}}}@media (width>=1024px){.lg\:tooltip{--tt-bg:var(--color-neutral);--tt-off:calc(100% + .5rem);--tt-tail:calc(100% + 1px + .25rem);display:inline-block;position:relative;&>:where(.tooltip-content),&:where([data-tip]):before{border-radius:var(--radius-field);text-align:center;white-space:normal;max-width:20rem;color:var(--color-neutral-content);opacity:0;background-color:var(--tt-bg);pointer-events:none;z-index:2;--tw-content:attr(data-tip);content:var(--tw-content);width:max-content;padding-block:.25rem;padding-inline:.5rem;font-size:.875rem;line-height:1.25;transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;position:absolute}&:after{opacity:0;background-color:var(--tt-bg);content:"";pointer-events:none;--mask-tooltip:url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");width:.625rem;height:.25rem;mask-position:-1px 0;mask-repeat:no-repeat;mask-image:var(--mask-tooltip);transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;display:block;position:absolute}}.lg\:tooltip,.lg\:tooltip-top{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-off)50%}}.lg\:tooltip-bottom{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem));inset:var(--tt-off)auto auto 50%}}.lg\:tooltip-left{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,.25rem) - .25rem))translateY(-50%);inset:50% var(--tt-off)auto auto}}.lg\:tooltip-right{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,-.25rem) + .25rem))translateY(-50%);inset:50% auto auto var(--tt-off)}}.lg\:tooltip{&.tooltip-open,&[data-tip]:not([data-tip=""]):hover,&:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover,&:has(:focus-visible){&>.tooltip-content,&[data-tip]:before,&:after{opacity:1;--tt-pos:0rem;transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1)}}}.lg\:tooltip,.lg\:tooltip-top{&:after{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-tail)50%}}.lg\:tooltip-bottom{&:after{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem))rotate(180deg);inset:var(--tt-tail)auto auto 50%}}.lg\:tooltip-left{&:after{transform:translateX(var(--tt-pos,.25rem))translateY(-50%)rotate(-90deg);inset:50% calc(var(--tt-tail) + 1px)auto auto}}.lg\:tooltip-right{&:after{transform:translateX(var(--tt-pos,-.25rem))translateY(-50%)rotate(90deg);inset:50% auto auto calc(var(--tt-tail) + 1px)}}.lg\:tooltip-primary{--tt-bg:var(--color-primary);&>.tooltip-content,&[data-tip]:before{color:var(--color-primary-content)}}.lg\:tooltip-secondary{--tt-bg:var(--color-secondary);&>.tooltip-content,&[data-tip]:before{color:var(--color-secondary-content)}}.lg\:tooltip-accent{--tt-bg:var(--color-accent);&>.tooltip-content,&[data-tip]:before{color:var(--color-accent-content)}}.lg\:tooltip-info{--tt-bg:var(--color-info);&>.tooltip-content,&[data-tip]:before{color:var(--color-info-content)}}.lg\:tooltip-success{--tt-bg:var(--color-success);&>.tooltip-content,&[data-tip]:before{color:var(--color-success-content)}}.lg\:tooltip-warning{--tt-bg:var(--color-warning);&>.tooltip-content,&[data-tip]:before{color:var(--color-warning-content)}}.lg\:tooltip-error{--tt-bg:var(--color-error);&>.tooltip-content,&[data-tip]:before{color:var(--color-error-content)}}}@media (width>=1280px){.xl\:tooltip{--tt-bg:var(--color-neutral);--tt-off:calc(100% + .5rem);--tt-tail:calc(100% + 1px + .25rem);display:inline-block;position:relative;&>:where(.tooltip-content),&:where([data-tip]):before{border-radius:var(--radius-field);text-align:center;white-space:normal;max-width:20rem;color:var(--color-neutral-content);opacity:0;background-color:var(--tt-bg);pointer-events:none;z-index:2;--tw-content:attr(data-tip);content:var(--tw-content);width:max-content;padding-block:.25rem;padding-inline:.5rem;font-size:.875rem;line-height:1.25;transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;position:absolute}&:after{opacity:0;background-color:var(--tt-bg);content:"";pointer-events:none;--mask-tooltip:url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");width:.625rem;height:.25rem;mask-position:-1px 0;mask-repeat:no-repeat;mask-image:var(--mask-tooltip);transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;display:block;position:absolute}}.xl\:tooltip,.xl\:tooltip-top{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-off)50%}}.xl\:tooltip-bottom{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem));inset:var(--tt-off)auto auto 50%}}.xl\:tooltip-left{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,.25rem) - .25rem))translateY(-50%);inset:50% var(--tt-off)auto auto}}.xl\:tooltip-right{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,-.25rem) + .25rem))translateY(-50%);inset:50% auto auto var(--tt-off)}}.xl\:tooltip{&.tooltip-open,&[data-tip]:not([data-tip=""]):hover,&:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover,&:has(:focus-visible){&>.tooltip-content,&[data-tip]:before,&:after{opacity:1;--tt-pos:0rem;transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1)}}}.xl\:tooltip,.xl\:tooltip-top{&:after{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-tail)50%}}.xl\:tooltip-bottom{&:after{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem))rotate(180deg);inset:var(--tt-tail)auto auto 50%}}.xl\:tooltip-left{&:after{transform:translateX(var(--tt-pos,.25rem))translateY(-50%)rotate(-90deg);inset:50% calc(var(--tt-tail) + 1px)auto auto}}.xl\:tooltip-right{&:after{transform:translateX(var(--tt-pos,-.25rem))translateY(-50%)rotate(90deg);inset:50% auto auto calc(var(--tt-tail) + 1px)}}.xl\:tooltip-primary{--tt-bg:var(--color-primary);&>.tooltip-content,&[data-tip]:before{color:var(--color-primary-content)}}.xl\:tooltip-secondary{--tt-bg:var(--color-secondary);&>.tooltip-content,&[data-tip]:before{color:var(--color-secondary-content)}}.xl\:tooltip-accent{--tt-bg:var(--color-accent);&>.tooltip-content,&[data-tip]:before{color:var(--color-accent-content)}}.xl\:tooltip-info{--tt-bg:var(--color-info);&>.tooltip-content,&[data-tip]:before{color:var(--color-info-content)}}.xl\:tooltip-success{--tt-bg:var(--color-success);&>.tooltip-content,&[data-tip]:before{color:var(--color-success-content)}}.xl\:tooltip-warning{--tt-bg:var(--color-warning);&>.tooltip-content,&[data-tip]:before{color:var(--color-warning-content)}}.xl\:tooltip-error{--tt-bg:var(--color-error);&>.tooltip-content,&[data-tip]:before{color:var(--color-error-content)}}}@media (width>=1536px){.\32 xl\:tooltip{--tt-bg:var(--color-neutral);--tt-off:calc(100% + .5rem);--tt-tail:calc(100% + 1px + .25rem);display:inline-block;position:relative;&>:where(.tooltip-content),&:where([data-tip]):before{border-radius:var(--radius-field);text-align:center;white-space:normal;max-width:20rem;color:var(--color-neutral-content);opacity:0;background-color:var(--tt-bg);pointer-events:none;z-index:2;--tw-content:attr(data-tip);content:var(--tw-content);width:max-content;padding-block:.25rem;padding-inline:.5rem;font-size:.875rem;line-height:1.25;transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;position:absolute}&:after{opacity:0;background-color:var(--tt-bg);content:"";pointer-events:none;--mask-tooltip:url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");width:.625rem;height:.25rem;mask-position:-1px 0;mask-repeat:no-repeat;mask-image:var(--mask-tooltip);transition:opacity .2s cubic-bezier(.4,0,.2,1) 75ms,transform .2s cubic-bezier(.4,0,.2,1) 75ms;display:block;position:absolute}}.\32 xl\:tooltip,.\32 xl\:tooltip-top{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-off)50%}}.\32 xl\:tooltip-bottom{&>.tooltip-content,&[data-tip]:before{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem));inset:var(--tt-off)auto auto 50%}}.\32 xl\:tooltip-left{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,.25rem) - .25rem))translateY(-50%);inset:50% var(--tt-off)auto auto}}.\32 xl\:tooltip-right{&>.tooltip-content,&[data-tip]:before{transform:translateX(calc(var(--tt-pos,-.25rem) + .25rem))translateY(-50%);inset:50% auto auto var(--tt-off)}}.\32 xl\:tooltip{&.tooltip-open,&[data-tip]:not([data-tip=""]):hover,&:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover,&:has(:focus-visible){&>.tooltip-content,&[data-tip]:before,&:after{opacity:1;--tt-pos:0rem;transition:opacity .2s cubic-bezier(.4,0,.2,1),transform .2s cubic-bezier(.4,0,.2,1)}}}.\32 xl\:tooltip,.\32 xl\:tooltip-top{&:after{transform:translateX(-50%)translateY(var(--tt-pos,.25rem));inset:auto auto var(--tt-tail)50%}}.\32 xl\:tooltip-bottom{&:after{transform:translateX(-50%)translateY(var(--tt-pos,-.25rem))rotate(180deg);inset:var(--tt-tail)auto auto 50%}}.\32 xl\:tooltip-left{&:after{transform:translateX(var(--tt-pos,.25rem))translateY(-50%)rotate(-90deg);inset:50% calc(var(--tt-tail) + 1px)auto auto}}.\32 xl\:tooltip-right{&:after{transform:translateX(var(--tt-pos,-.25rem))translateY(-50%)rotate(90deg);inset:50% auto auto calc(var(--tt-tail) + 1px)}}.\32 xl\:tooltip-primary{--tt-bg:var(--color-primary);&>.tooltip-content,&[data-tip]:before{color:var(--color-primary-content)}}.\32 xl\:tooltip-secondary{--tt-bg:var(--color-secondary);&>.tooltip-content,&[data-tip]:before{color:var(--color-secondary-content)}}.\32 xl\:tooltip-accent{--tt-bg:var(--color-accent);&>.tooltip-content,&[data-tip]:before{color:var(--color-accent-content)}}.\32 xl\:tooltip-info{--tt-bg:var(--color-info);&>.tooltip-content,&[data-tip]:before{color:var(--color-info-content)}}.\32 xl\:tooltip-success{--tt-bg:var(--color-success);&>.tooltip-content,&[data-tip]:before{color:var(--color-success-content)}}.\32 xl\:tooltip-warning{--tt-bg:var(--color-warning);&>.tooltip-content,&[data-tip]:before{color:var(--color-warning-content)}}.\32 xl\:tooltip-error{--tt-bg:var(--color-error);&>.tooltip-content,&[data-tip]:before{color:var(--color-error-content)}}}}
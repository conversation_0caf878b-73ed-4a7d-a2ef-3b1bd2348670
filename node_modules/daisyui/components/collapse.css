/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.collapse:not(td,tr,colgroup){visibility:visible}.collapse{border-radius:var(--radius-box,1rem);isolation:isolate;grid-template-rows:max-content 0fr;width:100%;transition:grid-template-rows .2s;display:grid;position:relative;overflow:hidden;&>input:is([type=checkbox],[type=radio]){appearance:none;opacity:0;grid-row-start:1;grid-column-start:1}&:is([open],:focus:not(.collapse-close)),&:not(.collapse-close):has(>input:is([type=checkbox],[type=radio]):checked){grid-template-rows:max-content 1fr}&:is([open],:focus:not(.collapse-close))>.collapse-content,&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){visibility:visible;min-height:fit-content}&:focus-visible,&:has(>input:is([type=checkbox],[type=radio]):focus-visible){outline-color:var(--color-base-content);outline-offset:2px;outline-width:2px;outline-style:solid}&:not(.collapse-close){&>input[type=checkbox],&>input[type=radio]:not(:checked),&>.collapse-title{cursor:pointer}}&:focus:not(.collapse-close,.collapse[open])>.collapse-title{cursor:unset}&:is([open],:focus:not(.collapse-close))>:where(.collapse-content),&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}&[open]{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-open{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-arrow:focus:not(.collapse-close){&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&.collapse-arrow:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&[open]{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-open{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-plus:focus:not(.collapse-close){&>.collapse-title:after{content:"−"}}&.collapse-plus:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{content:"−"}}&>input:is([type=checkbox],[type=radio]){z-index:1;padding:1rem;width:100%;min-height:1lh;padding-inline-end:3rem;transition:background-color .2s ease-out}}.collapse-title,.collapse-content{grid-row-start:1;grid-column-start:1}.collapse-content{visibility:hidden;min-height:0;cursor:unset;grid-row-start:2;grid-column-start:1;padding-left:1rem;padding-right:1rem;transition:visibility .2s,padding .2s ease-out,background-color .2s ease-out}.collapse:is(details){width:100%;& summary{display:block;position:relative;&::-webkit-details-marker{display:none}}}.collapse:is(details) summary{outline:none}.collapse-arrow{&>.collapse-title:after{top:1.9rem;content:"";transform-origin:75% 75%;pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem;transform:translateY(-100%)rotate(45deg);box-shadow:2px 2px}}.collapse-plus{&>.collapse-title:after{top:.9rem;content:"+";pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.3s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem}}.collapse-title{width:100%;min-height:1lh;padding:1rem;padding-inline-end:3rem;transition:background-color .2s ease-out;position:relative}.collapse-open{grid-template-rows:max-content 1fr;&>.collapse-content{visibility:visible;min-height:fit-content;padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}}@media (width>=640px){.sm\:collapse:not(td,tr,colgroup){visibility:visible}.sm\:collapse{border-radius:var(--radius-box,1rem);isolation:isolate;grid-template-rows:max-content 0fr;width:100%;transition:grid-template-rows .2s;display:grid;position:relative;overflow:hidden;&>input:is([type=checkbox],[type=radio]){appearance:none;opacity:0;grid-row-start:1;grid-column-start:1}&:is([open],:focus:not(.collapse-close)),&:not(.collapse-close):has(>input:is([type=checkbox],[type=radio]):checked){grid-template-rows:max-content 1fr}&:is([open],:focus:not(.collapse-close))>.collapse-content,&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){visibility:visible;min-height:fit-content}&:focus-visible,&:has(>input:is([type=checkbox],[type=radio]):focus-visible){outline-color:var(--color-base-content);outline-offset:2px;outline-width:2px;outline-style:solid}&:not(.collapse-close){&>input[type=checkbox],&>input[type=radio]:not(:checked),&>.collapse-title{cursor:pointer}}&:focus:not(.collapse-close,.collapse[open])>.collapse-title{cursor:unset}&:is([open],:focus:not(.collapse-close))>:where(.collapse-content),&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}&[open]{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-open{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-arrow:focus:not(.collapse-close){&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&.collapse-arrow:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&[open]{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-open{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-plus:focus:not(.collapse-close){&>.collapse-title:after{content:"−"}}&.collapse-plus:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{content:"−"}}&>input:is([type=checkbox],[type=radio]){z-index:1;padding:1rem;width:100%;min-height:1lh;padding-inline-end:3rem;transition:background-color .2s ease-out}}.sm\:collapse-title,.sm\:collapse-content{grid-row-start:1;grid-column-start:1}.sm\:collapse-content{visibility:hidden;min-height:0;cursor:unset;grid-row-start:2;grid-column-start:1;padding-left:1rem;padding-right:1rem;transition:visibility .2s,padding .2s ease-out,background-color .2s ease-out}.sm\:collapse:is(details){width:100%;& summary{display:block;position:relative;&::-webkit-details-marker{display:none}}}.sm\:collapse:is(details) summary{outline:none}.sm\:collapse-arrow{&>.collapse-title:after{top:1.9rem;content:"";transform-origin:75% 75%;pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem;transform:translateY(-100%)rotate(45deg);box-shadow:2px 2px}}.sm\:collapse-plus{&>.collapse-title:after{top:.9rem;content:"+";pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.3s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem}}.sm\:collapse-title{width:100%;min-height:1lh;padding:1rem;padding-inline-end:3rem;transition:background-color .2s ease-out;position:relative}.sm\:collapse-open{grid-template-rows:max-content 1fr;&>.collapse-content{visibility:visible;min-height:fit-content;padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}}}@media (width>=768px){.md\:collapse:not(td,tr,colgroup){visibility:visible}.md\:collapse{border-radius:var(--radius-box,1rem);isolation:isolate;grid-template-rows:max-content 0fr;width:100%;transition:grid-template-rows .2s;display:grid;position:relative;overflow:hidden;&>input:is([type=checkbox],[type=radio]){appearance:none;opacity:0;grid-row-start:1;grid-column-start:1}&:is([open],:focus:not(.collapse-close)),&:not(.collapse-close):has(>input:is([type=checkbox],[type=radio]):checked){grid-template-rows:max-content 1fr}&:is([open],:focus:not(.collapse-close))>.collapse-content,&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){visibility:visible;min-height:fit-content}&:focus-visible,&:has(>input:is([type=checkbox],[type=radio]):focus-visible){outline-color:var(--color-base-content);outline-offset:2px;outline-width:2px;outline-style:solid}&:not(.collapse-close){&>input[type=checkbox],&>input[type=radio]:not(:checked),&>.collapse-title{cursor:pointer}}&:focus:not(.collapse-close,.collapse[open])>.collapse-title{cursor:unset}&:is([open],:focus:not(.collapse-close))>:where(.collapse-content),&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}&[open]{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-open{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-arrow:focus:not(.collapse-close){&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&.collapse-arrow:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&[open]{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-open{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-plus:focus:not(.collapse-close){&>.collapse-title:after{content:"−"}}&.collapse-plus:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{content:"−"}}&>input:is([type=checkbox],[type=radio]){z-index:1;padding:1rem;width:100%;min-height:1lh;padding-inline-end:3rem;transition:background-color .2s ease-out}}.md\:collapse-title,.md\:collapse-content{grid-row-start:1;grid-column-start:1}.md\:collapse-content{visibility:hidden;min-height:0;cursor:unset;grid-row-start:2;grid-column-start:1;padding-left:1rem;padding-right:1rem;transition:visibility .2s,padding .2s ease-out,background-color .2s ease-out}.md\:collapse:is(details){width:100%;& summary{display:block;position:relative;&::-webkit-details-marker{display:none}}}.md\:collapse:is(details) summary{outline:none}.md\:collapse-arrow{&>.collapse-title:after{top:1.9rem;content:"";transform-origin:75% 75%;pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem;transform:translateY(-100%)rotate(45deg);box-shadow:2px 2px}}.md\:collapse-plus{&>.collapse-title:after{top:.9rem;content:"+";pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.3s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem}}.md\:collapse-title{width:100%;min-height:1lh;padding:1rem;padding-inline-end:3rem;transition:background-color .2s ease-out;position:relative}.md\:collapse-open{grid-template-rows:max-content 1fr;&>.collapse-content{visibility:visible;min-height:fit-content;padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}}}@media (width>=1024px){.lg\:collapse:not(td,tr,colgroup){visibility:visible}.lg\:collapse{border-radius:var(--radius-box,1rem);isolation:isolate;grid-template-rows:max-content 0fr;width:100%;transition:grid-template-rows .2s;display:grid;position:relative;overflow:hidden;&>input:is([type=checkbox],[type=radio]){appearance:none;opacity:0;grid-row-start:1;grid-column-start:1}&:is([open],:focus:not(.collapse-close)),&:not(.collapse-close):has(>input:is([type=checkbox],[type=radio]):checked){grid-template-rows:max-content 1fr}&:is([open],:focus:not(.collapse-close))>.collapse-content,&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){visibility:visible;min-height:fit-content}&:focus-visible,&:has(>input:is([type=checkbox],[type=radio]):focus-visible){outline-color:var(--color-base-content);outline-offset:2px;outline-width:2px;outline-style:solid}&:not(.collapse-close){&>input[type=checkbox],&>input[type=radio]:not(:checked),&>.collapse-title{cursor:pointer}}&:focus:not(.collapse-close,.collapse[open])>.collapse-title{cursor:unset}&:is([open],:focus:not(.collapse-close))>:where(.collapse-content),&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}&[open]{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-open{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-arrow:focus:not(.collapse-close){&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&.collapse-arrow:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&[open]{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-open{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-plus:focus:not(.collapse-close){&>.collapse-title:after{content:"−"}}&.collapse-plus:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{content:"−"}}&>input:is([type=checkbox],[type=radio]){z-index:1;padding:1rem;width:100%;min-height:1lh;padding-inline-end:3rem;transition:background-color .2s ease-out}}.lg\:collapse-title,.lg\:collapse-content{grid-row-start:1;grid-column-start:1}.lg\:collapse-content{visibility:hidden;min-height:0;cursor:unset;grid-row-start:2;grid-column-start:1;padding-left:1rem;padding-right:1rem;transition:visibility .2s,padding .2s ease-out,background-color .2s ease-out}.lg\:collapse:is(details){width:100%;& summary{display:block;position:relative;&::-webkit-details-marker{display:none}}}.lg\:collapse:is(details) summary{outline:none}.lg\:collapse-arrow{&>.collapse-title:after{top:1.9rem;content:"";transform-origin:75% 75%;pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem;transform:translateY(-100%)rotate(45deg);box-shadow:2px 2px}}.lg\:collapse-plus{&>.collapse-title:after{top:.9rem;content:"+";pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.3s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem}}.lg\:collapse-title{width:100%;min-height:1lh;padding:1rem;padding-inline-end:3rem;transition:background-color .2s ease-out;position:relative}.lg\:collapse-open{grid-template-rows:max-content 1fr;&>.collapse-content{visibility:visible;min-height:fit-content;padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}}}@media (width>=1280px){.xl\:collapse:not(td,tr,colgroup){visibility:visible}.xl\:collapse{border-radius:var(--radius-box,1rem);isolation:isolate;grid-template-rows:max-content 0fr;width:100%;transition:grid-template-rows .2s;display:grid;position:relative;overflow:hidden;&>input:is([type=checkbox],[type=radio]){appearance:none;opacity:0;grid-row-start:1;grid-column-start:1}&:is([open],:focus:not(.collapse-close)),&:not(.collapse-close):has(>input:is([type=checkbox],[type=radio]):checked){grid-template-rows:max-content 1fr}&:is([open],:focus:not(.collapse-close))>.collapse-content,&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){visibility:visible;min-height:fit-content}&:focus-visible,&:has(>input:is([type=checkbox],[type=radio]):focus-visible){outline-color:var(--color-base-content);outline-offset:2px;outline-width:2px;outline-style:solid}&:not(.collapse-close){&>input[type=checkbox],&>input[type=radio]:not(:checked),&>.collapse-title{cursor:pointer}}&:focus:not(.collapse-close,.collapse[open])>.collapse-title{cursor:unset}&:is([open],:focus:not(.collapse-close))>:where(.collapse-content),&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}&[open]{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-open{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-arrow:focus:not(.collapse-close){&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&.collapse-arrow:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&[open]{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-open{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-plus:focus:not(.collapse-close){&>.collapse-title:after{content:"−"}}&.collapse-plus:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{content:"−"}}&>input:is([type=checkbox],[type=radio]){z-index:1;padding:1rem;width:100%;min-height:1lh;padding-inline-end:3rem;transition:background-color .2s ease-out}}.xl\:collapse-title,.xl\:collapse-content{grid-row-start:1;grid-column-start:1}.xl\:collapse-content{visibility:hidden;min-height:0;cursor:unset;grid-row-start:2;grid-column-start:1;padding-left:1rem;padding-right:1rem;transition:visibility .2s,padding .2s ease-out,background-color .2s ease-out}.xl\:collapse:is(details){width:100%;& summary{display:block;position:relative;&::-webkit-details-marker{display:none}}}.xl\:collapse:is(details) summary{outline:none}.xl\:collapse-arrow{&>.collapse-title:after{top:1.9rem;content:"";transform-origin:75% 75%;pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem;transform:translateY(-100%)rotate(45deg);box-shadow:2px 2px}}.xl\:collapse-plus{&>.collapse-title:after{top:.9rem;content:"+";pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.3s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem}}.xl\:collapse-title{width:100%;min-height:1lh;padding:1rem;padding-inline-end:3rem;transition:background-color .2s ease-out;position:relative}.xl\:collapse-open{grid-template-rows:max-content 1fr;&>.collapse-content{visibility:visible;min-height:fit-content;padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}}}@media (width>=1536px){.\32 xl\:collapse:not(td,tr,colgroup){visibility:visible}.\32 xl\:collapse{border-radius:var(--radius-box,1rem);isolation:isolate;grid-template-rows:max-content 0fr;width:100%;transition:grid-template-rows .2s;display:grid;position:relative;overflow:hidden;&>input:is([type=checkbox],[type=radio]){appearance:none;opacity:0;grid-row-start:1;grid-column-start:1}&:is([open],:focus:not(.collapse-close)),&:not(.collapse-close):has(>input:is([type=checkbox],[type=radio]):checked){grid-template-rows:max-content 1fr}&:is([open],:focus:not(.collapse-close))>.collapse-content,&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){visibility:visible;min-height:fit-content}&:focus-visible,&:has(>input:is([type=checkbox],[type=radio]):focus-visible){outline-color:var(--color-base-content);outline-offset:2px;outline-width:2px;outline-style:solid}&:not(.collapse-close){&>input[type=checkbox],&>input[type=radio]:not(:checked),&>.collapse-title{cursor:pointer}}&:focus:not(.collapse-close,.collapse[open])>.collapse-title{cursor:unset}&:is([open],:focus:not(.collapse-close))>:where(.collapse-content),&:not(.collapse-close)>:where(input:is([type=checkbox],[type=radio]):checked~.collapse-content){padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}&[open]{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-open{&.collapse-arrow{&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}}&.collapse-arrow:focus:not(.collapse-close){&>.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&.collapse-arrow:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{transform:translateY(-50%)rotate(225deg)}}&[open]{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-open{&.collapse-plus{&>.collapse-title:after{content:"−"}}}&.collapse-plus:focus:not(.collapse-close){&>.collapse-title:after{content:"−"}}&.collapse-plus:not(.collapse-close){&>input:is([type=checkbox],[type=radio]):checked~.collapse-title:after{content:"−"}}&>input:is([type=checkbox],[type=radio]){z-index:1;padding:1rem;width:100%;min-height:1lh;padding-inline-end:3rem;transition:background-color .2s ease-out}}.\32 xl\:collapse-title,.\32 xl\:collapse-content{grid-row-start:1;grid-column-start:1}.\32 xl\:collapse-content{visibility:hidden;min-height:0;cursor:unset;grid-row-start:2;grid-column-start:1;padding-left:1rem;padding-right:1rem;transition:visibility .2s,padding .2s ease-out,background-color .2s ease-out}.\32 xl\:collapse:is(details){width:100%;& summary{display:block;position:relative;&::-webkit-details-marker{display:none}}}.\32 xl\:collapse:is(details) summary{outline:none}.\32 xl\:collapse-arrow{&>.collapse-title:after{top:1.9rem;content:"";transform-origin:75% 75%;pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem;transform:translateY(-100%)rotate(45deg);box-shadow:2px 2px}}.\32 xl\:collapse-plus{&>.collapse-title:after{top:.9rem;content:"+";pointer-events:none;width:.5rem;height:.5rem;transition-property:all;transition-duration:.3s;transition-timing-function:cubic-bezier(.4,0,.2,1);display:block;position:absolute;inset-inline-end:1.4rem}}.\32 xl\:collapse-title{width:100%;min-height:1lh;padding:1rem;padding-inline-end:3rem;transition:background-color .2s ease-out;position:relative}.\32 xl\:collapse-open{grid-template-rows:max-content 1fr;&>.collapse-content{visibility:visible;min-height:fit-content;padding-bottom:1rem;transition:padding .2s ease-out,background-color .2s ease-out}}}}
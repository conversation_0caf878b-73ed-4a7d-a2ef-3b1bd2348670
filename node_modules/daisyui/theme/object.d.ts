interface Theme {
  "color-scheme": string
  "--color-base-100": string
  "--color-base-200": string
  "--color-base-300": string
  "--color-base-content": string
  "--color-primary": string
  "--color-primary-content": string
  "--color-secondary": string
  "--color-secondary-content": string
  "--color-accent": string
  "--color-accent-content": string
  "--color-neutral": string
  "--color-neutral-content": string
  "--color-info": string
  "--color-info-content": string
  "--color-success": string
  "--color-success-content": string
  "--color-warning": string
  "--color-warning-content": string
  "--color-error": string
  "--color-error-content": string
  "--radius-selector": string
  "--radius-field": string
  "--radius-box": string
  "--size-selector": string
  "--size-field": string
  "--border": string
  "--depth": string
  "--noise": string
}


interface Themes {
  forest: Theme
  cyberpunk: Theme
  cupcake: Theme
  fantasy: Theme
  wireframe: Theme
  cmyk: Theme
  emerald: Theme
  nord: Theme
  halloween: Theme
  night: Theme
  silk: Theme
  coffee: Theme
  winter: Theme
  light: Theme
  valentine: Theme
  lemonade: Theme
  retro: Theme
  aqua: Theme
  dark: Theme
  lofi: Theme
  dim: Theme
  dracula: Theme
  corporate: Theme
  bumblebee: Theme
  business: Theme
  black: Theme
  acid: Theme
  luxury: Theme
  sunset: Theme
  garden: Theme
  autumn: Theme
  caramellatte: Theme
  pastel: Theme
  synthwave: Theme
  abyss: Theme
  [key: string]: Theme
}

declare const themes: Themes
export default themes
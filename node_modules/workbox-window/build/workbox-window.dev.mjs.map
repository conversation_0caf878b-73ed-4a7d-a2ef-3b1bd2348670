{"version": 3, "file": "workbox-window.dev.mjs", "sources": ["../_version.js", "../messageSW.js", "../../workbox-core/_version.js", "../../workbox-core/_private/Deferred.js", "../../workbox-core/_private/dontWaitFor.js", "../../workbox-core/_private/logger.js", "../utils/WorkboxEventTarget.js", "../utils/urlsMatch.js", "../utils/WorkboxEvent.js", "../Workbox.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:window:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Sends a data object to a service worker via `postMessage` and resolves with\n * a response (if any).\n *\n * A response can be set in a message handler in the service worker by\n * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n * returned by `messageSW()`. If no response is set, the promise will not\n * resolve.\n *\n * @param {ServiceWorker} sw The service worker to send the message to.\n * @param {Object} data An object to send to the service worker.\n * @return {Promise<Object|undefined>}\n * @memberof workbox-window\n */\n// Better not change type of data.\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction messageSW(sw, data) {\n    return new Promise((resolve) => {\n        const messageChannel = new MessageChannel();\n        messageChannel.port1.onmessage = (event) => {\n            resolve(event.data);\n        };\n        sw.postMessage(data, [messageChannel.port2]);\n    });\n}\nexport { messageSW };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production'\n    ? null\n    : (() => {\n        // Don't overwrite this value if it's already set.\n        // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-560470923\n        if (!('__WB_DISABLE_DEV_LOGS' in globalThis)) {\n            self.__WB_DISABLE_DEV_LOGS = false;\n        }\n        let inGroup = false;\n        const methodToColorMap = {\n            debug: `#7f8c8d`,\n            log: `#2ecc71`,\n            warn: `#f39c12`,\n            error: `#c0392b`,\n            groupCollapsed: `#3498db`,\n            groupEnd: null, // No colored prefix on groupEnd\n        };\n        const print = function (method, args) {\n            if (self.__WB_DISABLE_DEV_LOGS) {\n                return;\n            }\n            if (method === 'groupCollapsed') {\n                // Safari doesn't print all console.groupCollapsed() arguments:\n                // https://bugs.webkit.org/show_bug.cgi?id=182754\n                if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                    console[method](...args);\n                    return;\n                }\n            }\n            const styles = [\n                `background: ${methodToColorMap[method]}`,\n                `border-radius: 0.5em`,\n                `color: white`,\n                `font-weight: bold`,\n                `padding: 2px 0.5em`,\n            ];\n            // When in a group, the workbox prefix is not displayed.\n            const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n            console[method](...logPrefix, ...args);\n            if (method === 'groupCollapsed') {\n                inGroup = true;\n            }\n            if (method === 'groupEnd') {\n                inGroup = false;\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const api = {};\n        const loggerMethods = Object.keys(methodToColorMap);\n        for (const key of loggerMethods) {\n            const method = key;\n            api[method] = (...args) => {\n                print(method, args);\n            };\n        }\n        return api;\n    })());\nexport { logger };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n/**\n * A minimal `EventTarget` shim.\n * This is necessary because not all browsers support constructable\n * `EventTarget`, so using a real `EventTarget` will error.\n * @private\n */\nexport class WorkboxEventTarget {\n    constructor() {\n        this._eventListenerRegistry = new Map();\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    addEventListener(type, listener) {\n        const foo = this._getEventListenersByType(type);\n        foo.add(listener);\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    removeEventListener(type, listener) {\n        this._getEventListenersByType(type).delete(listener);\n    }\n    /**\n     * @param {Object} event\n     * @private\n     */\n    dispatchEvent(event) {\n        event.target = this;\n        const listeners = this._getEventListenersByType(event.type);\n        for (const listener of listeners) {\n            listener(event);\n        }\n    }\n    /**\n     * Returns a Set of listeners associated with the passed event type.\n     * If no handlers have been registered, an empty Set is returned.\n     *\n     * @param {string} type The event type.\n     * @return {Set<ListenerCallback>} An array of handler functions.\n     * @private\n     */\n    _getEventListenersByType(type) {\n        if (!this._eventListenerRegistry.has(type)) {\n            this._eventListenerRegistry.set(type, new Set());\n        }\n        return this._eventListenerRegistry.get(type);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns true if two URLs have the same `.href` property. The URLS can be\n * relative, and if they are the current location href is used to resolve URLs.\n *\n * @private\n * @param {string} url1\n * @param {string} url2\n * @return {boolean}\n */\nexport function urlsMatch(url1, url2) {\n    const { href } = location;\n    return new URL(url1, href).href === new URL(url2, href).href;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A minimal `Event` subclass shim.\n * This doesn't *actually* subclass `Event` because not all browsers support\n * constructable `EventTarget`, and using a real `Event` will error.\n * @private\n */\nexport class WorkboxEvent {\n    constructor(type, props) {\n        this.type = type;\n        Object.assign(this, props);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { messageSW } from './messageSW.js';\nimport { WorkboxEventTarget } from './utils/WorkboxEventTarget.js';\nimport { urlsMatch } from './utils/urlsMatch.js';\nimport { WorkboxEvent } from './utils/WorkboxEvent.js';\nimport './_version.js';\n// The time a SW must be in the waiting phase before we can conclude\n// `skipWaiting()` wasn't called. This 200 amount wasn't scientifically\n// chosen, but it seems to avoid false positives in my testing.\nconst WAITING_TIMEOUT_DURATION = 200;\n// The amount of time after a registration that we can reasonably conclude\n// that the registration didn't trigger an update.\nconst REGISTRATION_TIMEOUT_DURATION = 60000;\n// The de facto standard message that a service worker should be listening for\n// to trigger a call to skipWaiting().\nconst SKIP_WAITING_MESSAGE = { type: 'SKIP_WAITING' };\n/**\n * A class to aid in handling service worker registration, updates, and\n * reacting to service worker lifecycle events.\n *\n * @fires {@link workbox-window.Workbox#message}\n * @fires {@link workbox-window.Workbox#installed}\n * @fires {@link workbox-window.Workbox#waiting}\n * @fires {@link workbox-window.Workbox#controlling}\n * @fires {@link workbox-window.Workbox#activated}\n * @fires {@link workbox-window.Workbox#redundant}\n * @memberof workbox-window\n */\nclass Workbox extends WorkboxEventTarget {\n    /**\n     * Creates a new Workbox instance with a script URL and service worker\n     * options. The script URL and options are the same as those used when\n     * calling [navigator.serviceWorker.register(scriptURL, options)](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n     *\n     * @param {string|TrustedScriptURL} scriptURL The service worker script\n     *     associated with this instance. Using a\n     *     [`TrustedScriptURL`](https://web.dev/trusted-types/) is supported.\n     * @param {Object} [registerOptions] The service worker options associated\n     *     with this instance.\n     */\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(scriptURL, registerOptions = {}) {\n        super();\n        this._registerOptions = {};\n        this._updateFoundCount = 0;\n        // Deferreds we can resolve later.\n        this._swDeferred = new Deferred();\n        this._activeDeferred = new Deferred();\n        this._controllingDeferred = new Deferred();\n        this._registrationTime = 0;\n        this._ownSWs = new Set();\n        /**\n         * @private\n         */\n        this._onUpdateFound = () => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const installingSW = registration.installing;\n            // If the script URL passed to `navigator.serviceWorker.register()` is\n            // different from the current controlling SW's script URL, we know any\n            // successful registration calls will trigger an `updatefound` event.\n            // But if the registered script URL is the same as the current controlling\n            // SW's script URL, we'll only get an `updatefound` event if the file\n            // changed since it was last registered. This can be a problem if the user\n            // opens up the same page in a different tab, and that page registers\n            // a SW that triggers an update. It's a problem because this page has no\n            // good way of knowing whether the `updatefound` event came from the SW\n            // script it registered or from a registration attempt made by a newer\n            // version of the page running in another tab.\n            // To minimize the possibility of a false positive, we use the logic here:\n            const updateLikelyTriggeredExternally = \n            // Since we enforce only calling `register()` once, and since we don't\n            // add the `updatefound` event listener until the `register()` call, if\n            // `_updateFoundCount` is > 0 then it means this method has already\n            // been called, thus this SW must be external\n            this._updateFoundCount > 0 ||\n                // If the script URL of the installing SW is different from this\n                // instance's script URL, we know it's definitely not from our\n                // registration.\n                !urlsMatch(installingSW.scriptURL, this._scriptURL.toString()) ||\n                // If all of the above are false, then we use a time-based heuristic:\n                // Any `updatefound` event that occurs long after our registration is\n                // assumed to be external.\n                performance.now() > this._registrationTime + REGISTRATION_TIMEOUT_DURATION\n                ? // If any of the above are not true, we assume the update was\n                    // triggered by this instance.\n                    true\n                : false;\n            if (updateLikelyTriggeredExternally) {\n                this._externalSW = installingSW;\n                registration.removeEventListener('updatefound', this._onUpdateFound);\n            }\n            else {\n                // If the update was not triggered externally we know the installing\n                // SW is the one we registered, so we set it.\n                this._sw = installingSW;\n                this._ownSWs.add(installingSW);\n                this._swDeferred.resolve(installingSW);\n                // The `installing` state isn't something we have a dedicated\n                // callback for, but we do log messages for it in development.\n                if (process.env.NODE_ENV !== 'production') {\n                    if (navigator.serviceWorker.controller) {\n                        logger.log('Updated service worker found. Installing now...');\n                    }\n                    else {\n                        logger.log('Service worker is installing...');\n                    }\n                }\n            }\n            // Increment the `updatefound` count, so future invocations of this\n            // method can be sure they were triggered externally.\n            ++this._updateFoundCount;\n            // Add a `statechange` listener regardless of whether this update was\n            // triggered externally, since we have callbacks for both.\n            installingSW.addEventListener('statechange', this._onStateChange);\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onStateChange = (originalEvent) => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const sw = originalEvent.target;\n            const { state } = sw;\n            const isExternal = sw === this._externalSW;\n            const eventProps = {\n                sw,\n                isExternal,\n                originalEvent,\n            };\n            if (!isExternal && this._isUpdate) {\n                eventProps.isUpdate = true;\n            }\n            this.dispatchEvent(new WorkboxEvent(state, eventProps));\n            if (state === 'installed') {\n                // This timeout is used to ignore cases where the service worker calls\n                // `skipWaiting()` in the install event, thus moving it directly in the\n                // activating state. (Since all service workers *must* go through the\n                // waiting phase, the only way to detect `skipWaiting()` called in the\n                // install event is to observe that the time spent in the waiting phase\n                // is very short.)\n                // NOTE: we don't need separate timeouts for the own and external SWs\n                // since they can't go through these phases at the same time.\n                this._waitingTimeout = self.setTimeout(() => {\n                    // Ensure the SW is still waiting (it may now be redundant).\n                    if (state === 'installed' && registration.waiting === sw) {\n                        this.dispatchEvent(new WorkboxEvent('waiting', eventProps));\n                        if (process.env.NODE_ENV !== 'production') {\n                            if (isExternal) {\n                                logger.warn('An external service worker has installed but is ' +\n                                    'waiting for this client to close before activating...');\n                            }\n                            else {\n                                logger.warn('The service worker has installed but is waiting ' +\n                                    'for existing clients to close before activating...');\n                            }\n                        }\n                    }\n                }, WAITING_TIMEOUT_DURATION);\n            }\n            else if (state === 'activating') {\n                clearTimeout(this._waitingTimeout);\n                if (!isExternal) {\n                    this._activeDeferred.resolve(sw);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                switch (state) {\n                    case 'installed':\n                        if (isExternal) {\n                            logger.warn('An external service worker has installed. ' +\n                                'You may want to suggest users reload this page.');\n                        }\n                        else {\n                            logger.log('Registered service worker installed.');\n                        }\n                        break;\n                    case 'activated':\n                        if (isExternal) {\n                            logger.warn('An external service worker has activated.');\n                        }\n                        else {\n                            logger.log('Registered service worker activated.');\n                            if (sw !== navigator.serviceWorker.controller) {\n                                logger.warn('The registered service worker is active but ' +\n                                    'not yet controlling the page. Reload or run ' +\n                                    '`clients.claim()` in the service worker.');\n                            }\n                        }\n                        break;\n                    case 'redundant':\n                        if (sw === this._compatibleControllingSW) {\n                            logger.log('Previously controlling service worker now redundant!');\n                        }\n                        else if (!isExternal) {\n                            logger.log('Registered service worker now redundant!');\n                        }\n                        break;\n                }\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onControllerChange = (originalEvent) => {\n            const sw = this._sw;\n            const isExternal = sw !== navigator.serviceWorker.controller;\n            // Unconditionally dispatch the controlling event, with isExternal set\n            // to distinguish between controller changes due to the initial registration\n            // vs. an update-check or other tab's registration.\n            // See https://github.com/GoogleChrome/workbox/issues/2786\n            this.dispatchEvent(new WorkboxEvent('controlling', {\n                isExternal,\n                originalEvent,\n                sw,\n                isUpdate: this._isUpdate,\n            }));\n            if (!isExternal) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Registered service worker now controlling this page.');\n                }\n                this._controllingDeferred.resolve(sw);\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onMessage = async (originalEvent) => {\n            // Can't change type 'any' of data.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const { data, ports, source } = originalEvent;\n            // Wait until there's an \"own\" service worker. This is used to buffer\n            // `message` events that may be received prior to calling `register()`.\n            await this.getSW();\n            // If the service worker that sent the message is in the list of own\n            // service workers for this instance, dispatch a `message` event.\n            // NOTE: we check for all previously owned service workers rather than\n            // just the current one because some messages (e.g. cache updates) use\n            // a timeout when sent and may be delayed long enough for a service worker\n            // update to be found.\n            if (this._ownSWs.has(source)) {\n                this.dispatchEvent(new WorkboxEvent('message', {\n                    // Can't change type 'any' of data.\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                    data,\n                    originalEvent,\n                    ports,\n                    sw: source,\n                }));\n            }\n        };\n        this._scriptURL = scriptURL;\n        this._registerOptions = registerOptions;\n        // Add a message listener immediately since messages received during\n        // page load are buffered only until the DOMContentLoaded event:\n        // https://github.com/GoogleChrome/workbox/issues/2202\n        navigator.serviceWorker.addEventListener('message', this._onMessage);\n    }\n    /**\n     * Registers a service worker for this instances script URL and service\n     * worker options. By default this method delays registration until after\n     * the window has loaded.\n     *\n     * @param {Object} [options]\n     * @param {Function} [options.immediate=false] Setting this to true will\n     *     register the service worker immediately, even if the window has\n     *     not loaded (not recommended).\n     */\n    async register({ immediate = false } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._registrationTime) {\n                logger.error('Cannot re-register a Workbox instance after it has ' +\n                    'been registered. Create a new instance instead.');\n                return;\n            }\n        }\n        if (!immediate && document.readyState !== 'complete') {\n            await new Promise((res) => window.addEventListener('load', res));\n        }\n        // Set this flag to true if any service worker was controlling the page\n        // at registration time.\n        this._isUpdate = Boolean(navigator.serviceWorker.controller);\n        // Before registering, attempt to determine if a SW is already controlling\n        // the page, and if that SW script (and version, if specified) matches this\n        // instance's script.\n        this._compatibleControllingSW = this._getControllingSWIfCompatible();\n        this._registration = await this._registerScript();\n        // If we have a compatible controller, store the controller as the \"own\"\n        // SW, resolve active/controlling deferreds and add necessary listeners.\n        if (this._compatibleControllingSW) {\n            this._sw = this._compatibleControllingSW;\n            this._activeDeferred.resolve(this._compatibleControllingSW);\n            this._controllingDeferred.resolve(this._compatibleControllingSW);\n            this._compatibleControllingSW.addEventListener('statechange', this._onStateChange, { once: true });\n        }\n        // If there's a waiting service worker with a matching URL before the\n        // `updatefound` event fires, it likely means that this site is open\n        // in another tab, or the user refreshed the page (and thus the previous\n        // page wasn't fully unloaded before this page started loading).\n        // https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle#waiting\n        const waitingSW = this._registration.waiting;\n        if (waitingSW &&\n            urlsMatch(waitingSW.scriptURL, this._scriptURL.toString())) {\n            // Store the waiting SW as the \"own\" Sw, even if it means overwriting\n            // a compatible controller.\n            this._sw = waitingSW;\n            // Run this in the next microtask, so any code that adds an event\n            // listener after awaiting `register()` will get this event.\n            dontWaitFor(Promise.resolve().then(() => {\n                this.dispatchEvent(new WorkboxEvent('waiting', {\n                    sw: waitingSW,\n                    wasWaitingBeforeRegister: true,\n                }));\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn('A service worker was already waiting to activate ' +\n                        'before this script was registered...');\n                }\n            }));\n        }\n        // If an \"own\" SW is already set, resolve the deferred.\n        if (this._sw) {\n            this._swDeferred.resolve(this._sw);\n            this._ownSWs.add(this._sw);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log('Successfully registered service worker.', this._scriptURL.toString());\n            if (navigator.serviceWorker.controller) {\n                if (this._compatibleControllingSW) {\n                    logger.debug('A service worker with the same script URL ' +\n                        'is already controlling this page.');\n                }\n                else {\n                    logger.debug('A service worker with a different script URL is ' +\n                        'currently controlling the page. The browser is now fetching ' +\n                        'the new script now...');\n                }\n            }\n            const currentPageIsOutOfScope = () => {\n                const scopeURL = new URL(this._registerOptions.scope || this._scriptURL.toString(), document.baseURI);\n                const scopeURLBasePath = new URL('./', scopeURL.href).pathname;\n                return !location.pathname.startsWith(scopeURLBasePath);\n            };\n            if (currentPageIsOutOfScope()) {\n                logger.warn('The current page is not in scope for the registered ' +\n                    'service worker. Was this a mistake?');\n            }\n        }\n        this._registration.addEventListener('updatefound', this._onUpdateFound);\n        navigator.serviceWorker.addEventListener('controllerchange', this._onControllerChange);\n        return this._registration;\n    }\n    /**\n     * Checks for updates of the registered service worker.\n     */\n    async update() {\n        if (!this._registration) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error('Cannot update a Workbox instance without ' +\n                    'being registered. Register the Workbox instance first.');\n            }\n            return;\n        }\n        // Try to update registration\n        await this._registration.update();\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is active. If a service worker was already controlling at registration\n     * time then it will resolve to that if the script URLs (and optionally\n     * script versions) match, otherwise it will wait until an update is found\n     * and activates.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get active() {\n        return this._activeDeferred.promise;\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is controlling the page. If a service worker was already controlling at\n     * registration time then it will resolve to that if the script URLs (and\n     * optionally script versions) match, otherwise it will wait until an update\n     * is found and starts controlling the page.\n     * Note: the first time a service worker is installed it will active but\n     * not start controlling the page unless `clients.claim()` is called in the\n     * service worker.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get controlling() {\n        return this._controllingDeferred.promise;\n    }\n    /**\n     * Resolves with a reference to a service worker that matches the script URL\n     * of this instance, as soon as it's available.\n     *\n     * If, at registration time, there's already an active or waiting service\n     * worker with a matching script URL, it will be used (with the waiting\n     * service worker taking precedence over the active service worker if both\n     * match, since the waiting service worker would have been registered more\n     * recently).\n     * If there's no matching active or waiting service worker at registration\n     * time then the promise will not resolve until an update is found and starts\n     * installing, at which point the installing service worker is used.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    getSW() {\n        // If `this._sw` is set, resolve with that as we want `getSW()` to\n        // return the correct (new) service worker if an update is found.\n        return this._sw !== undefined\n            ? Promise.resolve(this._sw)\n            : this._swDeferred.promise;\n    }\n    /**\n     * Sends the passed data object to the service worker registered by this\n     * instance (via {@link workbox-window.Workbox#getSW}) and resolves\n     * with a response (if any).\n     *\n     * A response can be set in a message handler in the service worker by\n     * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n     * returned by `messageSW()`. If no response is set, the promise will never\n     * resolve.\n     *\n     * @param {Object} data An object to send to the service worker\n     * @return {Promise<Object>}\n     */\n    // We might be able to change the 'data' type to Record<string, unknown> in the future.\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    async messageSW(data) {\n        const sw = await this.getSW();\n        return messageSW(sw, data);\n    }\n    /**\n     * Sends a `{type: 'SKIP_WAITING'}` message to the service worker that's\n     * currently in the `waiting` state associated with the current registration.\n     *\n     * If there is no current registration or no service worker is `waiting`,\n     * calling this will have no effect.\n     */\n    messageSkipWaiting() {\n        if (this._registration && this._registration.waiting) {\n            void messageSW(this._registration.waiting, SKIP_WAITING_MESSAGE);\n        }\n    }\n    /**\n     * Checks for a service worker already controlling the page and returns\n     * it if its script URL matches.\n     *\n     * @private\n     * @return {ServiceWorker|undefined}\n     */\n    _getControllingSWIfCompatible() {\n        const controller = navigator.serviceWorker.controller;\n        if (controller &&\n            urlsMatch(controller.scriptURL, this._scriptURL.toString())) {\n            return controller;\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Registers a service worker for this instances script URL and register\n     * options and tracks the time registration was complete.\n     *\n     * @private\n     */\n    async _registerScript() {\n        try {\n            // this._scriptURL may be a TrustedScriptURL, but there's no support for\n            // passing that to register() in lib.dom right now.\n            // https://github.com/GoogleChrome/workbox/issues/2855\n            const reg = await navigator.serviceWorker.register(this._scriptURL, this._registerOptions);\n            // Keep track of when registration happened, so it can be used in the\n            // `this._onUpdateFound` heuristic. Also use the presence of this\n            // property as a way to see if `.register()` has been called.\n            this._registrationTime = performance.now();\n            return reg;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(error);\n            }\n            // Re-throw the error.\n            throw error;\n        }\n    }\n}\nexport { Workbox };\n// The jsdoc comments below outline the events this instance may dispatch:\n// -----------------------------------------------------------------------\n/**\n * The `message` event is dispatched any time a `postMessage` is received.\n *\n * @event workbox-window.Workbox#message\n * @type {WorkboxEvent}\n * @property {*} data The `data` property from the original `message` event.\n * @property {Event} originalEvent The original [`message`]{@link https://developer.mozilla.org/en-US/docs/Web/API/MessageEvent}\n *     event.\n * @property {string} type `message`.\n * @property {MessagePort[]} ports The `ports` value from `originalEvent`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `installed` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `installed`.\n *\n * Then can happen either the very first time a service worker is installed,\n * or after an update to the current service worker is found. In the case\n * of an update being found, the event's `isUpdate` property will be `true`.\n *\n * @event workbox-window.Workbox#installed\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `installed`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `waiting` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `installed` and then doesn't immediately change to `activating`.\n * It may also be dispatched if a service worker with the same\n * [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * was already waiting when the {@link workbox-window.Workbox#register}\n * method was called.\n *\n * @event workbox-window.Workbox#waiting\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event|undefined} originalEvent The original\n *    [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event, or `undefined` in the case where the service worker was waiting\n *     to before `.register()` was called.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {boolean|undefined} wasWaitingBeforeRegister True if a service worker with\n *     a matching `scriptURL` was already waiting when this `Workbox`\n *     instance called `register()`.\n * @property {string} type `waiting`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `controlling` event is dispatched if a\n * [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n * fires on the service worker [container]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer}\n * and the [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * of the new [controller]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/controller}\n * matches the `scriptURL` of the `Workbox` instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}.\n *\n * @event workbox-window.Workbox#controlling\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this service worker was registered.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `controlling`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `activated` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `activated`.\n *\n * @event workbox-window.Workbox#activated\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `activated`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `redundant` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `redundant`.\n *\n * @event workbox-window.Workbox#redundant\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {string} type `redundant`.\n * @property {Workbox} target The `Workbox` instance.\n */\n"], "names": ["self", "_", "e", "messageSW", "sw", "data", "Promise", "resolve", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "postMessage", "port2", "Deferred", "constructor", "promise", "reject", "dontWait<PERSON>or", "then", "logger", "globalThis", "__WB_DISABLE_DEV_LOGS", "inGroup", "methodToColorMap", "debug", "log", "warn", "error", "groupCollapsed", "groupEnd", "print", "method", "args", "test", "navigator", "userAgent", "console", "styles", "logPrefix", "join", "api", "loggerMethods", "Object", "keys", "key", "WorkboxEventTarget", "_eventListenerRegistry", "Map", "addEventListener", "type", "listener", "foo", "_getEventListenersByType", "add", "removeEventListener", "delete", "dispatchEvent", "target", "listeners", "has", "set", "Set", "get", "urlsMatch", "url1", "url2", "href", "location", "URL", "WorkboxEvent", "props", "assign", "WAITING_TIMEOUT_DURATION", "REGISTRATION_TIMEOUT_DURATION", "SKIP_WAITING_MESSAGE", "Workbox", "scriptURL", "registerOptions", "_registerOptions", "_updateFoundCount", "_swDeferred", "_activeD<PERSON><PERSON>red", "_controlling<PERSON><PERSON><PERSON><PERSON>", "_registrationTime", "_ownSWs", "_onUpdateFound", "registration", "_registration", "installingSW", "installing", "updateLikelyTriggeredExternally", "_scriptURL", "toString", "performance", "now", "_externalSW", "_sw", "serviceWorker", "controller", "_onStateChange", "originalEvent", "state", "isExternal", "eventProps", "_isUpdate", "isUpdate", "_waitingTimeout", "setTimeout", "waiting", "clearTimeout", "_compatibleControllingSW", "_onControllerChange", "_onMessage", "ports", "source", "getSW", "register", "immediate", "document", "readyState", "res", "window", "Boolean", "_getControllingSWIfCompatible", "_registerScript", "once", "waitingSW", "wasWaitingBeforeRegister", "currentPageIsOutOfScope", "scopeURL", "scope", "baseURI", "scopeURLBasePath", "pathname", "startsWith", "update", "active", "controlling", "undefined", "messageSkipWaiting", "reg"], "mappings": "AACA;AACA,IAAI;AACAA,EAAAA,IAAI,CAAC,sBAAsB,CAAC,IAAIC,CAAC,EAAE,CAAA;AACvC,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,EAAE,EAAEC,IAAI,EAAE;AACzB,EAAA,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;AAC5B,IAAA,MAAMC,cAAc,GAAG,IAAIC,cAAc,EAAE,CAAA;AAC3CD,IAAAA,cAAc,CAACE,KAAK,CAACC,SAAS,GAAIC,KAAK,IAAK;AACxCL,MAAAA,OAAO,CAACK,KAAK,CAACP,IAAI,CAAC,CAAA;KACtB,CAAA;IACDD,EAAE,CAACS,WAAW,CAACR,IAAI,EAAE,CAACG,cAAc,CAACM,KAAK,CAAC,CAAC,CAAA;AAChD,GAAC,CAAC,CAAA;AACN;;AC/BA;AACA,IAAI;AACAd,EAAAA,IAAI,CAAC,oBAAoB,CAAC,IAAIC,CAAC,EAAE,CAAA;AACrC,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,QAAQ,CAAC;AACX;AACJ;AACA;AACIC,EAAAA,WAAWA,GAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAIX,OAAO,CAAC,CAACC,OAAO,EAAEW,MAAM,KAAK;MAC5C,IAAI,CAACX,OAAO,GAAGA,OAAO,CAAA;MACtB,IAAI,CAACW,MAAM,GAAGA,MAAM,CAAA;AACxB,KAAC,CAAC,CAAA;AACN,GAAA;AACJ;;AC1BA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CAACF,OAAO,EAAE;AACjC;AACA,EAAA,KAAKA,OAAO,CAACG,IAAI,CAAC,MAAM,EAAG,CAAC,CAAA;AAChC;;ACfA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMC,MAAM,GAEN,CAAC,MAAM;AACL;AACA;AACA,EAAA,IAAI,EAAE,uBAAuB,IAAIC,UAAU,CAAC,EAAE;IAC1CtB,IAAI,CAACuB,qBAAqB,GAAG,KAAK,CAAA;AACtC,GAAA;EACA,IAAIC,OAAO,GAAG,KAAK,CAAA;AACnB,EAAA,MAAMC,gBAAgB,GAAG;AACrBC,IAAAA,KAAK,EAAG,CAAQ,OAAA,CAAA;AAChBC,IAAAA,GAAG,EAAG,CAAQ,OAAA,CAAA;AACdC,IAAAA,IAAI,EAAG,CAAQ,OAAA,CAAA;AACfC,IAAAA,KAAK,EAAG,CAAQ,OAAA,CAAA;AAChBC,IAAAA,cAAc,EAAG,CAAQ,OAAA,CAAA;IACzBC,QAAQ,EAAE,IAAI;GACjB,CAAA;AACD,EAAA,MAAMC,KAAK,GAAG,UAAUC,MAAM,EAAEC,IAAI,EAAE;IAClC,IAAIlC,IAAI,CAACuB,qBAAqB,EAAE;AAC5B,MAAA,OAAA;AACJ,KAAA;IACA,IAAIU,MAAM,KAAK,gBAAgB,EAAE;AAC7B;AACA;MACA,IAAI,gCAAgC,CAACE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;AAC5DC,QAAAA,OAAO,CAACL,MAAM,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAA;AACxB,QAAA,OAAA;AACJ,OAAA;AACJ,KAAA;AACA,IAAA,MAAMK,MAAM,GAAG,CACV,CAAcd,YAAAA,EAAAA,gBAAgB,CAACQ,MAAM,CAAE,CAAC,CAAA,EACxC,sBAAqB,EACrB,CAAA,YAAA,CAAa,EACb,CAAkB,iBAAA,CAAA,EAClB,oBAAmB,CACvB,CAAA;AACD;AACA,IAAA,MAAMO,SAAS,GAAGhB,OAAO,GAAG,EAAE,GAAG,CAAC,WAAW,EAAEe,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IAChEH,OAAO,CAACL,MAAM,CAAC,CAAC,GAAGO,SAAS,EAAE,GAAGN,IAAI,CAAC,CAAA;IACtC,IAAID,MAAM,KAAK,gBAAgB,EAAE;AAC7BT,MAAAA,OAAO,GAAG,IAAI,CAAA;AAClB,KAAA;IACA,IAAIS,MAAM,KAAK,UAAU,EAAE;AACvBT,MAAAA,OAAO,GAAG,KAAK,CAAA;AACnB,KAAA;GACH,CAAA;AACD;EACA,MAAMkB,GAAG,GAAG,EAAE,CAAA;AACd,EAAA,MAAMC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACpB,gBAAgB,CAAC,CAAA;AACnD,EAAA,KAAK,MAAMqB,GAAG,IAAIH,aAAa,EAAE;IAC7B,MAAMV,MAAM,GAAGa,GAAG,CAAA;AAClBJ,IAAAA,GAAG,CAACT,MAAM,CAAC,GAAG,CAAC,GAAGC,IAAI,KAAK;AACvBF,MAAAA,KAAK,CAACC,MAAM,EAAEC,IAAI,CAAC,CAAA;KACtB,CAAA;AACL,GAAA;AACA,EAAA,OAAOQ,GAAG,CAAA;AACd,CAAC,GAAI;;AC/DT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMK,kBAAkB,CAAC;AAC5B/B,EAAAA,WAAWA,GAAG;AACV,IAAA,IAAI,CAACgC,sBAAsB,GAAG,IAAIC,GAAG,EAAE,CAAA;AAC3C,GAAA;AACA;AACJ;AACA;AACA;AACA;AACIC,EAAAA,gBAAgBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;AAC7B,IAAA,MAAMC,GAAG,GAAG,IAAI,CAACC,wBAAwB,CAACH,IAAI,CAAC,CAAA;AAC/CE,IAAAA,GAAG,CAACE,GAAG,CAACH,QAAQ,CAAC,CAAA;AACrB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACII,EAAAA,mBAAmBA,CAACL,IAAI,EAAEC,QAAQ,EAAE;IAChC,IAAI,CAACE,wBAAwB,CAACH,IAAI,CAAC,CAACM,MAAM,CAACL,QAAQ,CAAC,CAAA;AACxD,GAAA;AACA;AACJ;AACA;AACA;EACIM,aAAaA,CAAC9C,KAAK,EAAE;IACjBA,KAAK,CAAC+C,MAAM,GAAG,IAAI,CAAA;IACnB,MAAMC,SAAS,GAAG,IAAI,CAACN,wBAAwB,CAAC1C,KAAK,CAACuC,IAAI,CAAC,CAAA;AAC3D,IAAA,KAAK,MAAMC,QAAQ,IAAIQ,SAAS,EAAE;MAC9BR,QAAQ,CAACxC,KAAK,CAAC,CAAA;AACnB,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI0C,wBAAwBA,CAACH,IAAI,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACH,sBAAsB,CAACa,GAAG,CAACV,IAAI,CAAC,EAAE;MACxC,IAAI,CAACH,sBAAsB,CAACc,GAAG,CAACX,IAAI,EAAE,IAAIY,GAAG,EAAE,CAAC,CAAA;AACpD,KAAA;AACA,IAAA,OAAO,IAAI,CAACf,sBAAsB,CAACgB,GAAG,CAACb,IAAI,CAAC,CAAA;AAChD,GAAA;AACJ;;AC3DA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASc,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAClC,MAAM;AAAEC,IAAAA,IAAAA;AAAK,GAAC,GAAGC,QAAQ,CAAA;AACzB,EAAA,OAAO,IAAIC,GAAG,CAACJ,IAAI,EAAEE,IAAI,CAAC,CAACA,IAAI,KAAK,IAAIE,GAAG,CAACH,IAAI,EAAEC,IAAI,CAAC,CAACA,IAAI,CAAA;AAChE;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMG,YAAY,CAAC;AACtBvD,EAAAA,WAAWA,CAACmC,IAAI,EAAEqB,KAAK,EAAE;IACrB,IAAI,CAACrB,IAAI,GAAGA,IAAI,CAAA;AAChBP,IAAAA,MAAM,CAAC6B,MAAM,CAAC,IAAI,EAAED,KAAK,CAAC,CAAA;AAC9B,GAAA;AACJ;;ACnBA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA,MAAME,wBAAwB,GAAG,GAAG,CAAA;AACpC;AACA;AACA,MAAMC,6BAA6B,GAAG,KAAK,CAAA;AAC3C;AACA;AACA,MAAMC,oBAAoB,GAAG;AAAEzB,EAAAA,IAAI,EAAE,cAAA;AAAe,CAAC,CAAA;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,OAAO,SAAS9B,kBAAkB,CAAC;AACrC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI;AACA/B,EAAAA,WAAWA,CAAC8D,SAAS,EAAEC,eAAe,GAAG,EAAE,EAAE;AACzC,IAAA,KAAK,EAAE,CAAA;AACP,IAAA,IAAI,CAACC,gBAAgB,GAAG,EAAE,CAAA;IAC1B,IAAI,CAACC,iBAAiB,GAAG,CAAC,CAAA;AAC1B;AACA,IAAA,IAAI,CAACC,WAAW,GAAG,IAAInE,QAAQ,EAAE,CAAA;AACjC,IAAA,IAAI,CAACoE,eAAe,GAAG,IAAIpE,QAAQ,EAAE,CAAA;AACrC,IAAA,IAAI,CAACqE,oBAAoB,GAAG,IAAIrE,QAAQ,EAAE,CAAA;IAC1C,IAAI,CAACsE,iBAAiB,GAAG,CAAC,CAAA;AAC1B,IAAA,IAAI,CAACC,OAAO,GAAG,IAAIvB,GAAG,EAAE,CAAA;AACxB;AACR;AACA;IACQ,IAAI,CAACwB,cAAc,GAAG,MAAM;AACxB;AACA,MAAA,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa,CAAA;AACvC,MAAA,MAAMC,YAAY,GAAGF,YAAY,CAACG,UAAU,CAAA;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,MAAMC,+BAA+B;AACrC;AACA;AACA;AACA;MACA,IAAI,CAACX,iBAAiB,GAAG,CAAC;AACtB;AACA;AACA;AACA,MAAA,CAAChB,SAAS,CAACyB,YAAY,CAACZ,SAAS,EAAE,IAAI,CAACe,UAAU,CAACC,QAAQ,EAAE,CAAC;AAC9D;AACA;AACA;MACAC,WAAW,CAACC,GAAG,EAAE,GAAG,IAAI,CAACX,iBAAiB,GAAGV,6BAA6B;AACxE;AACE;AACA,MAAA,IAAI,GACN,KAAK,CAAA;AACX,MAAA,IAAIiB,+BAA+B,EAAE;QACjC,IAAI,CAACK,WAAW,GAAGP,YAAY,CAAA;QAC/BF,YAAY,CAAChC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC+B,cAAc,CAAC,CAAA;AACxE,OAAC,MACI;AACD;AACA;QACA,IAAI,CAACW,GAAG,GAAGR,YAAY,CAAA;AACvB,QAAA,IAAI,CAACJ,OAAO,CAAC/B,GAAG,CAACmC,YAAY,CAAC,CAAA;AAC9B,QAAA,IAAI,CAACR,WAAW,CAAC3E,OAAO,CAACmF,YAAY,CAAC,CAAA;AACtC;AACA;AACA,QAA2C;AACvC,UAAA,IAAItD,SAAS,CAAC+D,aAAa,CAACC,UAAU,EAAE;AACpC/E,YAAAA,MAAM,CAACM,GAAG,CAAC,iDAAiD,CAAC,CAAA;AACjE,WAAC,MACI;AACDN,YAAAA,MAAM,CAACM,GAAG,CAAC,iCAAiC,CAAC,CAAA;AACjD,WAAA;AACJ,SAAA;AACJ,OAAA;AACA;AACA;MACA,EAAE,IAAI,CAACsD,iBAAiB,CAAA;AACxB;AACA;MACAS,YAAY,CAACxC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACmD,cAAc,CAAC,CAAA;KACpE,CAAA;AACD;AACR;AACA;AACA;AACQ,IAAA,IAAI,CAACA,cAAc,GAAIC,aAAa,IAAK;AACrC;AACA,MAAA,MAAMd,YAAY,GAAG,IAAI,CAACC,aAAa,CAAA;AACvC,MAAA,MAAMrF,EAAE,GAAGkG,aAAa,CAAC3C,MAAM,CAAA;MAC/B,MAAM;AAAE4C,QAAAA,KAAAA;AAAM,OAAC,GAAGnG,EAAE,CAAA;AACpB,MAAA,MAAMoG,UAAU,GAAGpG,EAAE,KAAK,IAAI,CAAC6F,WAAW,CAAA;AAC1C,MAAA,MAAMQ,UAAU,GAAG;QACfrG,EAAE;QACFoG,UAAU;AACVF,QAAAA,aAAAA;OACH,CAAA;AACD,MAAA,IAAI,CAACE,UAAU,IAAI,IAAI,CAACE,SAAS,EAAE;QAC/BD,UAAU,CAACE,QAAQ,GAAG,IAAI,CAAA;AAC9B,OAAA;MACA,IAAI,CAACjD,aAAa,CAAC,IAAIa,YAAY,CAACgC,KAAK,EAAEE,UAAU,CAAC,CAAC,CAAA;MACvD,IAAIF,KAAK,KAAK,WAAW,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,IAAI,CAACK,eAAe,GAAG5G,IAAI,CAAC6G,UAAU,CAAC,MAAM;AACzC;UACA,IAAIN,KAAK,KAAK,WAAW,IAAIf,YAAY,CAACsB,OAAO,KAAK1G,EAAE,EAAE;YACtD,IAAI,CAACsD,aAAa,CAAC,IAAIa,YAAY,CAAC,SAAS,EAAEkC,UAAU,CAAC,CAAC,CAAA;AAC3D,YAA2C;AACvC,cAAA,IAAID,UAAU,EAAE;AACZnF,gBAAAA,MAAM,CAACO,IAAI,CAAC,kDAAkD,GAC1D,uDAAuD,CAAC,CAAA;AAChE,eAAC,MACI;AACDP,gBAAAA,MAAM,CAACO,IAAI,CAAC,kDAAkD,GAC1D,oDAAoD,CAAC,CAAA;AAC7D,eAAA;AACJ,aAAA;AACJ,WAAA;SACH,EAAE8C,wBAAwB,CAAC,CAAA;AAChC,OAAC,MACI,IAAI6B,KAAK,KAAK,YAAY,EAAE;AAC7BQ,QAAAA,YAAY,CAAC,IAAI,CAACH,eAAe,CAAC,CAAA;QAClC,IAAI,CAACJ,UAAU,EAAE;AACb,UAAA,IAAI,CAACrB,eAAe,CAAC5E,OAAO,CAACH,EAAE,CAAC,CAAA;AACpC,SAAA;AACJ,OAAA;AACA,MAA2C;AACvC,QAAA,QAAQmG,KAAK;AACT,UAAA,KAAK,WAAW;AACZ,YAAA,IAAIC,UAAU,EAAE;AACZnF,cAAAA,MAAM,CAACO,IAAI,CAAC,4CAA4C,GACpD,iDAAiD,CAAC,CAAA;AAC1D,aAAC,MACI;AACDP,cAAAA,MAAM,CAACM,GAAG,CAAC,sCAAsC,CAAC,CAAA;AACtD,aAAA;AACA,YAAA,MAAA;AACJ,UAAA,KAAK,WAAW;AACZ,YAAA,IAAI6E,UAAU,EAAE;AACZnF,cAAAA,MAAM,CAACO,IAAI,CAAC,2CAA2C,CAAC,CAAA;AAC5D,aAAC,MACI;AACDP,cAAAA,MAAM,CAACM,GAAG,CAAC,sCAAsC,CAAC,CAAA;AAClD,cAAA,IAAIvB,EAAE,KAAKgC,SAAS,CAAC+D,aAAa,CAACC,UAAU,EAAE;gBAC3C/E,MAAM,CAACO,IAAI,CAAC,8CAA8C,GACtD,8CAA8C,GAC9C,0CAA0C,CAAC,CAAA;AACnD,eAAA;AACJ,aAAA;AACA,YAAA,MAAA;AACJ,UAAA,KAAK,WAAW;AACZ,YAAA,IAAIxB,EAAE,KAAK,IAAI,CAAC4G,wBAAwB,EAAE;AACtC3F,cAAAA,MAAM,CAACM,GAAG,CAAC,sDAAsD,CAAC,CAAA;AACtE,aAAC,MACI,IAAI,CAAC6E,UAAU,EAAE;AAClBnF,cAAAA,MAAM,CAACM,GAAG,CAAC,0CAA0C,CAAC,CAAA;AAC1D,aAAA;AACA,YAAA,MAAA;AACR,SAAA;AACJ,OAAA;KACH,CAAA;AACD;AACR;AACA;AACA;AACQ,IAAA,IAAI,CAACsF,mBAAmB,GAAIX,aAAa,IAAK;AAC1C,MAAA,MAAMlG,EAAE,GAAG,IAAI,CAAC8F,GAAG,CAAA;MACnB,MAAMM,UAAU,GAAGpG,EAAE,KAAKgC,SAAS,CAAC+D,aAAa,CAACC,UAAU,CAAA;AAC5D;AACA;AACA;AACA;AACA,MAAA,IAAI,CAAC1C,aAAa,CAAC,IAAIa,YAAY,CAAC,aAAa,EAAE;QAC/CiC,UAAU;QACVF,aAAa;QACblG,EAAE;QACFuG,QAAQ,EAAE,IAAI,CAACD,SAAAA;AACnB,OAAC,CAAC,CAAC,CAAA;MACH,IAAI,CAACF,UAAU,EAAE;AACb,QAA2C;AACvCnF,UAAAA,MAAM,CAACM,GAAG,CAAC,sDAAsD,CAAC,CAAA;AACtE,SAAA;AACA,QAAA,IAAI,CAACyD,oBAAoB,CAAC7E,OAAO,CAACH,EAAE,CAAC,CAAA;AACzC,OAAA;KACH,CAAA;AACD;AACR;AACA;AACA;AACQ,IAAA,IAAI,CAAC8G,UAAU,GAAG,MAAOZ,aAAa,IAAK;AACvC;AACA;MACA,MAAM;QAAEjG,IAAI;QAAE8G,KAAK;AAAEC,QAAAA,MAAAA;AAAO,OAAC,GAAGd,aAAa,CAAA;AAC7C;AACA;AACA,MAAA,MAAM,IAAI,CAACe,KAAK,EAAE,CAAA;AAClB;AACA;AACA;AACA;AACA;AACA;MACA,IAAI,IAAI,CAAC/B,OAAO,CAACzB,GAAG,CAACuD,MAAM,CAAC,EAAE;AAC1B,QAAA,IAAI,CAAC1D,aAAa,CAAC,IAAIa,YAAY,CAAC,SAAS,EAAE;AAC3C;AACA;UACAlE,IAAI;UACJiG,aAAa;UACba,KAAK;AACL/G,UAAAA,EAAE,EAAEgH,MAAAA;AACR,SAAC,CAAC,CAAC,CAAA;AACP,OAAA;KACH,CAAA;IACD,IAAI,CAACvB,UAAU,GAAGf,SAAS,CAAA;IAC3B,IAAI,CAACE,gBAAgB,GAAGD,eAAe,CAAA;AACvC;AACA;AACA;IACA3C,SAAS,CAAC+D,aAAa,CAACjD,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACgE,UAAU,CAAC,CAAA;AACxE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMI,QAAQA,CAAC;AAAEC,IAAAA,SAAS,GAAG,KAAA;GAAO,GAAG,EAAE,EAAE;AACvC,IAA2C;MACvC,IAAI,IAAI,CAAClC,iBAAiB,EAAE;AACxBhE,QAAAA,MAAM,CAACQ,KAAK,CAAC,qDAAqD,GAC9D,iDAAiD,CAAC,CAAA;AACtD,QAAA,OAAA;AACJ,OAAA;AACJ,KAAA;IACA,IAAI,CAAC0F,SAAS,IAAIC,QAAQ,CAACC,UAAU,KAAK,UAAU,EAAE;AAClD,MAAA,MAAM,IAAInH,OAAO,CAAEoH,GAAG,IAAKC,MAAM,CAACzE,gBAAgB,CAAC,MAAM,EAAEwE,GAAG,CAAC,CAAC,CAAA;AACpE,KAAA;AACA;AACA;IACA,IAAI,CAAChB,SAAS,GAAGkB,OAAO,CAACxF,SAAS,CAAC+D,aAAa,CAACC,UAAU,CAAC,CAAA;AAC5D;AACA;AACA;AACA,IAAA,IAAI,CAACY,wBAAwB,GAAG,IAAI,CAACa,6BAA6B,EAAE,CAAA;IACpE,IAAI,CAACpC,aAAa,GAAG,MAAM,IAAI,CAACqC,eAAe,EAAE,CAAA;AACjD;AACA;IACA,IAAI,IAAI,CAACd,wBAAwB,EAAE;AAC/B,MAAA,IAAI,CAACd,GAAG,GAAG,IAAI,CAACc,wBAAwB,CAAA;MACxC,IAAI,CAAC7B,eAAe,CAAC5E,OAAO,CAAC,IAAI,CAACyG,wBAAwB,CAAC,CAAA;MAC3D,IAAI,CAAC5B,oBAAoB,CAAC7E,OAAO,CAAC,IAAI,CAACyG,wBAAwB,CAAC,CAAA;MAChE,IAAI,CAACA,wBAAwB,CAAC9D,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACmD,cAAc,EAAE;AAAE0B,QAAAA,IAAI,EAAE,IAAA;AAAK,OAAC,CAAC,CAAA;AACtG,KAAA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,MAAMC,SAAS,GAAG,IAAI,CAACvC,aAAa,CAACqB,OAAO,CAAA;AAC5C,IAAA,IAAIkB,SAAS,IACT/D,SAAS,CAAC+D,SAAS,CAAClD,SAAS,EAAE,IAAI,CAACe,UAAU,CAACC,QAAQ,EAAE,CAAC,EAAE;AAC5D;AACA;MACA,IAAI,CAACI,GAAG,GAAG8B,SAAS,CAAA;AACpB;AACA;MACA7G,WAAW,CAACb,OAAO,CAACC,OAAO,EAAE,CAACa,IAAI,CAAC,MAAM;AACrC,QAAA,IAAI,CAACsC,aAAa,CAAC,IAAIa,YAAY,CAAC,SAAS,EAAE;AAC3CnE,UAAAA,EAAE,EAAE4H,SAAS;AACbC,UAAAA,wBAAwB,EAAE,IAAA;AAC9B,SAAC,CAAC,CAAC,CAAA;AACH,QAA2C;AACvC5G,UAAAA,MAAM,CAACO,IAAI,CAAC,mDAAmD,GAC3D,sCAAsC,CAAC,CAAA;AAC/C,SAAA;AACJ,OAAC,CAAC,CAAC,CAAA;AACP,KAAA;AACA;IACA,IAAI,IAAI,CAACsE,GAAG,EAAE;MACV,IAAI,CAAChB,WAAW,CAAC3E,OAAO,CAAC,IAAI,CAAC2F,GAAG,CAAC,CAAA;MAClC,IAAI,CAACZ,OAAO,CAAC/B,GAAG,CAAC,IAAI,CAAC2C,GAAG,CAAC,CAAA;AAC9B,KAAA;AACA,IAA2C;AACvC7E,MAAAA,MAAM,CAACM,GAAG,CAAC,yCAAyC,EAAE,IAAI,CAACkE,UAAU,CAACC,QAAQ,EAAE,CAAC,CAAA;AACjF,MAAA,IAAI1D,SAAS,CAAC+D,aAAa,CAACC,UAAU,EAAE;QACpC,IAAI,IAAI,CAACY,wBAAwB,EAAE;AAC/B3F,UAAAA,MAAM,CAACK,KAAK,CAAC,4CAA4C,GACrD,mCAAmC,CAAC,CAAA;AAC5C,SAAC,MACI;UACDL,MAAM,CAACK,KAAK,CAAC,kDAAkD,GAC3D,8DAA8D,GAC9D,uBAAuB,CAAC,CAAA;AAChC,SAAA;AACJ,OAAA;MACA,MAAMwG,uBAAuB,GAAGA,MAAM;QAClC,MAAMC,QAAQ,GAAG,IAAI7D,GAAG,CAAC,IAAI,CAACU,gBAAgB,CAACoD,KAAK,IAAI,IAAI,CAACvC,UAAU,CAACC,QAAQ,EAAE,EAAE0B,QAAQ,CAACa,OAAO,CAAC,CAAA;AACrG,QAAA,MAAMC,gBAAgB,GAAG,IAAIhE,GAAG,CAAC,IAAI,EAAE6D,QAAQ,CAAC/D,IAAI,CAAC,CAACmE,QAAQ,CAAA;QAC9D,OAAO,CAAClE,QAAQ,CAACkE,QAAQ,CAACC,UAAU,CAACF,gBAAgB,CAAC,CAAA;OACzD,CAAA;MACD,IAAIJ,uBAAuB,EAAE,EAAE;AAC3B7G,QAAAA,MAAM,CAACO,IAAI,CAAC,sDAAsD,GAC9D,qCAAqC,CAAC,CAAA;AAC9C,OAAA;AACJ,KAAA;IACA,IAAI,CAAC6D,aAAa,CAACvC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACqC,cAAc,CAAC,CAAA;IACvEnD,SAAS,CAAC+D,aAAa,CAACjD,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC+D,mBAAmB,CAAC,CAAA;IACtF,OAAO,IAAI,CAACxB,aAAa,CAAA;AAC7B,GAAA;AACA;AACJ;AACA;EACI,MAAMgD,MAAMA,GAAG;AACX,IAAA,IAAI,CAAC,IAAI,CAAChD,aAAa,EAAE;AACrB,MAA2C;AACvCpE,QAAAA,MAAM,CAACQ,KAAK,CAAC,2CAA2C,GACpD,wDAAwD,CAAC,CAAA;AACjE,OAAA;AACA,MAAA,OAAA;AACJ,KAAA;AACA;AACA,IAAA,MAAM,IAAI,CAAC4D,aAAa,CAACgD,MAAM,EAAE,CAAA;AACrC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,MAAMA,GAAG;AACT,IAAA,OAAO,IAAI,CAACvD,eAAe,CAAClE,OAAO,CAAA;AACvC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI0H,WAAWA,GAAG;AACd,IAAA,OAAO,IAAI,CAACvD,oBAAoB,CAACnE,OAAO,CAAA;AAC5C,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIoG,EAAAA,KAAKA,GAAG;AACJ;AACA;AACA,IAAA,OAAO,IAAI,CAACnB,GAAG,KAAK0C,SAAS,GACvBtI,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC2F,GAAG,CAAC,GACzB,IAAI,CAAChB,WAAW,CAACjE,OAAO,CAAA;AAClC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI;AACA;EACA,MAAMd,SAASA,CAACE,IAAI,EAAE;AAClB,IAAA,MAAMD,EAAE,GAAG,MAAM,IAAI,CAACiH,KAAK,EAAE,CAAA;AAC7B,IAAA,OAAOlH,SAAS,CAACC,EAAE,EAAEC,IAAI,CAAC,CAAA;AAC9B,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACIwI,EAAAA,kBAAkBA,GAAG;IACjB,IAAI,IAAI,CAACpD,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqB,OAAO,EAAE;MAClD,KAAK3G,SAAS,CAAC,IAAI,CAACsF,aAAa,CAACqB,OAAO,EAAElC,oBAAoB,CAAC,CAAA;AACpE,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACIiD,EAAAA,6BAA6BA,GAAG;AAC5B,IAAA,MAAMzB,UAAU,GAAGhE,SAAS,CAAC+D,aAAa,CAACC,UAAU,CAAA;AACrD,IAAA,IAAIA,UAAU,IACVnC,SAAS,CAACmC,UAAU,CAACtB,SAAS,EAAE,IAAI,CAACe,UAAU,CAACC,QAAQ,EAAE,CAAC,EAAE;AAC7D,MAAA,OAAOM,UAAU,CAAA;AACrB,KAAC,MACI;AACD,MAAA,OAAOwC,SAAS,CAAA;AACpB,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMd,eAAeA,GAAG;IACpB,IAAI;AACA;AACA;AACA;AACA,MAAA,MAAMgB,GAAG,GAAG,MAAM1G,SAAS,CAAC+D,aAAa,CAACmB,QAAQ,CAAC,IAAI,CAACzB,UAAU,EAAE,IAAI,CAACb,gBAAgB,CAAC,CAAA;AAC1F;AACA;AACA;AACA,MAAA,IAAI,CAACK,iBAAiB,GAAGU,WAAW,CAACC,GAAG,EAAE,CAAA;AAC1C,MAAA,OAAO8C,GAAG,CAAA;KACb,CACD,OAAOjH,KAAK,EAAE;AACV,MAA2C;AACvCR,QAAAA,MAAM,CAACQ,KAAK,CAACA,KAAK,CAAC,CAAA;AACvB,OAAA;AACA;AACA,MAAA,MAAMA,KAAK,CAAA;AACf,KAAA;AACJ,GAAA;AACJ,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;"}
{"version": 3, "file": "workbox-window.prod.umd.js", "sources": ["../_version.js", "../messageSW.js", "../../workbox-core/_version.js", "../../workbox-core/_private/Deferred.js", "../utils/urlsMatch.js", "../utils/WorkboxEvent.js", "../Workbox.js", "../../workbox-core/_private/dontWaitFor.js", "../utils/WorkboxEventTarget.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:window:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Sends a data object to a service worker via `postMessage` and resolves with\n * a response (if any).\n *\n * A response can be set in a message handler in the service worker by\n * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n * returned by `messageSW()`. If no response is set, the promise will not\n * resolve.\n *\n * @param {ServiceWorker} sw The service worker to send the message to.\n * @param {Object} data An object to send to the service worker.\n * @return {Promise<Object|undefined>}\n * @memberof workbox-window\n */\n// Better not change type of data.\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction messageSW(sw, data) {\n    return new Promise((resolve) => {\n        const messageChannel = new MessageChannel();\n        messageChannel.port1.onmessage = (event) => {\n            resolve(event.data);\n        };\n        sw.postMessage(data, [messageChannel.port2]);\n    });\n}\nexport { messageSW };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns true if two URLs have the same `.href` property. The URLS can be\n * relative, and if they are the current location href is used to resolve URLs.\n *\n * @private\n * @param {string} url1\n * @param {string} url2\n * @return {boolean}\n */\nexport function urlsMatch(url1, url2) {\n    const { href } = location;\n    return new URL(url1, href).href === new URL(url2, href).href;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A minimal `Event` subclass shim.\n * This doesn't *actually* subclass `Event` because not all browsers support\n * constructable `EventTarget`, and using a real `Event` will error.\n * @private\n */\nexport class WorkboxEvent {\n    constructor(type, props) {\n        this.type = type;\n        Object.assign(this, props);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { messageSW } from './messageSW.js';\nimport { WorkboxEventTarget } from './utils/WorkboxEventTarget.js';\nimport { urlsMatch } from './utils/urlsMatch.js';\nimport { WorkboxEvent } from './utils/WorkboxEvent.js';\nimport './_version.js';\n// The time a SW must be in the waiting phase before we can conclude\n// `skipWaiting()` wasn't called. This 200 amount wasn't scientifically\n// chosen, but it seems to avoid false positives in my testing.\nconst WAITING_TIMEOUT_DURATION = 200;\n// The amount of time after a registration that we can reasonably conclude\n// that the registration didn't trigger an update.\nconst REGISTRATION_TIMEOUT_DURATION = 60000;\n// The de facto standard message that a service worker should be listening for\n// to trigger a call to skipWaiting().\nconst SKIP_WAITING_MESSAGE = { type: 'SKIP_WAITING' };\n/**\n * A class to aid in handling service worker registration, updates, and\n * reacting to service worker lifecycle events.\n *\n * @fires {@link workbox-window.Workbox#message}\n * @fires {@link workbox-window.Workbox#installed}\n * @fires {@link workbox-window.Workbox#waiting}\n * @fires {@link workbox-window.Workbox#controlling}\n * @fires {@link workbox-window.Workbox#activated}\n * @fires {@link workbox-window.Workbox#redundant}\n * @memberof workbox-window\n */\nclass Workbox extends WorkboxEventTarget {\n    /**\n     * Creates a new Workbox instance with a script URL and service worker\n     * options. The script URL and options are the same as those used when\n     * calling [navigator.serviceWorker.register(scriptURL, options)](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n     *\n     * @param {string|TrustedScriptURL} scriptURL The service worker script\n     *     associated with this instance. Using a\n     *     [`TrustedScriptURL`](https://web.dev/trusted-types/) is supported.\n     * @param {Object} [registerOptions] The service worker options associated\n     *     with this instance.\n     */\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(scriptURL, registerOptions = {}) {\n        super();\n        this._registerOptions = {};\n        this._updateFoundCount = 0;\n        // Deferreds we can resolve later.\n        this._swDeferred = new Deferred();\n        this._activeDeferred = new Deferred();\n        this._controllingDeferred = new Deferred();\n        this._registrationTime = 0;\n        this._ownSWs = new Set();\n        /**\n         * @private\n         */\n        this._onUpdateFound = () => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const installingSW = registration.installing;\n            // If the script URL passed to `navigator.serviceWorker.register()` is\n            // different from the current controlling SW's script URL, we know any\n            // successful registration calls will trigger an `updatefound` event.\n            // But if the registered script URL is the same as the current controlling\n            // SW's script URL, we'll only get an `updatefound` event if the file\n            // changed since it was last registered. This can be a problem if the user\n            // opens up the same page in a different tab, and that page registers\n            // a SW that triggers an update. It's a problem because this page has no\n            // good way of knowing whether the `updatefound` event came from the SW\n            // script it registered or from a registration attempt made by a newer\n            // version of the page running in another tab.\n            // To minimize the possibility of a false positive, we use the logic here:\n            const updateLikelyTriggeredExternally = \n            // Since we enforce only calling `register()` once, and since we don't\n            // add the `updatefound` event listener until the `register()` call, if\n            // `_updateFoundCount` is > 0 then it means this method has already\n            // been called, thus this SW must be external\n            this._updateFoundCount > 0 ||\n                // If the script URL of the installing SW is different from this\n                // instance's script URL, we know it's definitely not from our\n                // registration.\n                !urlsMatch(installingSW.scriptURL, this._scriptURL.toString()) ||\n                // If all of the above are false, then we use a time-based heuristic:\n                // Any `updatefound` event that occurs long after our registration is\n                // assumed to be external.\n                performance.now() > this._registrationTime + REGISTRATION_TIMEOUT_DURATION\n                ? // If any of the above are not true, we assume the update was\n                    // triggered by this instance.\n                    true\n                : false;\n            if (updateLikelyTriggeredExternally) {\n                this._externalSW = installingSW;\n                registration.removeEventListener('updatefound', this._onUpdateFound);\n            }\n            else {\n                // If the update was not triggered externally we know the installing\n                // SW is the one we registered, so we set it.\n                this._sw = installingSW;\n                this._ownSWs.add(installingSW);\n                this._swDeferred.resolve(installingSW);\n                // The `installing` state isn't something we have a dedicated\n                // callback for, but we do log messages for it in development.\n                if (process.env.NODE_ENV !== 'production') {\n                    if (navigator.serviceWorker.controller) {\n                        logger.log('Updated service worker found. Installing now...');\n                    }\n                    else {\n                        logger.log('Service worker is installing...');\n                    }\n                }\n            }\n            // Increment the `updatefound` count, so future invocations of this\n            // method can be sure they were triggered externally.\n            ++this._updateFoundCount;\n            // Add a `statechange` listener regardless of whether this update was\n            // triggered externally, since we have callbacks for both.\n            installingSW.addEventListener('statechange', this._onStateChange);\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onStateChange = (originalEvent) => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const sw = originalEvent.target;\n            const { state } = sw;\n            const isExternal = sw === this._externalSW;\n            const eventProps = {\n                sw,\n                isExternal,\n                originalEvent,\n            };\n            if (!isExternal && this._isUpdate) {\n                eventProps.isUpdate = true;\n            }\n            this.dispatchEvent(new WorkboxEvent(state, eventProps));\n            if (state === 'installed') {\n                // This timeout is used to ignore cases where the service worker calls\n                // `skipWaiting()` in the install event, thus moving it directly in the\n                // activating state. (Since all service workers *must* go through the\n                // waiting phase, the only way to detect `skipWaiting()` called in the\n                // install event is to observe that the time spent in the waiting phase\n                // is very short.)\n                // NOTE: we don't need separate timeouts for the own and external SWs\n                // since they can't go through these phases at the same time.\n                this._waitingTimeout = self.setTimeout(() => {\n                    // Ensure the SW is still waiting (it may now be redundant).\n                    if (state === 'installed' && registration.waiting === sw) {\n                        this.dispatchEvent(new WorkboxEvent('waiting', eventProps));\n                        if (process.env.NODE_ENV !== 'production') {\n                            if (isExternal) {\n                                logger.warn('An external service worker has installed but is ' +\n                                    'waiting for this client to close before activating...');\n                            }\n                            else {\n                                logger.warn('The service worker has installed but is waiting ' +\n                                    'for existing clients to close before activating...');\n                            }\n                        }\n                    }\n                }, WAITING_TIMEOUT_DURATION);\n            }\n            else if (state === 'activating') {\n                clearTimeout(this._waitingTimeout);\n                if (!isExternal) {\n                    this._activeDeferred.resolve(sw);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                switch (state) {\n                    case 'installed':\n                        if (isExternal) {\n                            logger.warn('An external service worker has installed. ' +\n                                'You may want to suggest users reload this page.');\n                        }\n                        else {\n                            logger.log('Registered service worker installed.');\n                        }\n                        break;\n                    case 'activated':\n                        if (isExternal) {\n                            logger.warn('An external service worker has activated.');\n                        }\n                        else {\n                            logger.log('Registered service worker activated.');\n                            if (sw !== navigator.serviceWorker.controller) {\n                                logger.warn('The registered service worker is active but ' +\n                                    'not yet controlling the page. Reload or run ' +\n                                    '`clients.claim()` in the service worker.');\n                            }\n                        }\n                        break;\n                    case 'redundant':\n                        if (sw === this._compatibleControllingSW) {\n                            logger.log('Previously controlling service worker now redundant!');\n                        }\n                        else if (!isExternal) {\n                            logger.log('Registered service worker now redundant!');\n                        }\n                        break;\n                }\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onControllerChange = (originalEvent) => {\n            const sw = this._sw;\n            const isExternal = sw !== navigator.serviceWorker.controller;\n            // Unconditionally dispatch the controlling event, with isExternal set\n            // to distinguish between controller changes due to the initial registration\n            // vs. an update-check or other tab's registration.\n            // See https://github.com/GoogleChrome/workbox/issues/2786\n            this.dispatchEvent(new WorkboxEvent('controlling', {\n                isExternal,\n                originalEvent,\n                sw,\n                isUpdate: this._isUpdate,\n            }));\n            if (!isExternal) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Registered service worker now controlling this page.');\n                }\n                this._controllingDeferred.resolve(sw);\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onMessage = async (originalEvent) => {\n            // Can't change type 'any' of data.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const { data, ports, source } = originalEvent;\n            // Wait until there's an \"own\" service worker. This is used to buffer\n            // `message` events that may be received prior to calling `register()`.\n            await this.getSW();\n            // If the service worker that sent the message is in the list of own\n            // service workers for this instance, dispatch a `message` event.\n            // NOTE: we check for all previously owned service workers rather than\n            // just the current one because some messages (e.g. cache updates) use\n            // a timeout when sent and may be delayed long enough for a service worker\n            // update to be found.\n            if (this._ownSWs.has(source)) {\n                this.dispatchEvent(new WorkboxEvent('message', {\n                    // Can't change type 'any' of data.\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                    data,\n                    originalEvent,\n                    ports,\n                    sw: source,\n                }));\n            }\n        };\n        this._scriptURL = scriptURL;\n        this._registerOptions = registerOptions;\n        // Add a message listener immediately since messages received during\n        // page load are buffered only until the DOMContentLoaded event:\n        // https://github.com/GoogleChrome/workbox/issues/2202\n        navigator.serviceWorker.addEventListener('message', this._onMessage);\n    }\n    /**\n     * Registers a service worker for this instances script URL and service\n     * worker options. By default this method delays registration until after\n     * the window has loaded.\n     *\n     * @param {Object} [options]\n     * @param {Function} [options.immediate=false] Setting this to true will\n     *     register the service worker immediately, even if the window has\n     *     not loaded (not recommended).\n     */\n    async register({ immediate = false } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._registrationTime) {\n                logger.error('Cannot re-register a Workbox instance after it has ' +\n                    'been registered. Create a new instance instead.');\n                return;\n            }\n        }\n        if (!immediate && document.readyState !== 'complete') {\n            await new Promise((res) => window.addEventListener('load', res));\n        }\n        // Set this flag to true if any service worker was controlling the page\n        // at registration time.\n        this._isUpdate = Boolean(navigator.serviceWorker.controller);\n        // Before registering, attempt to determine if a SW is already controlling\n        // the page, and if that SW script (and version, if specified) matches this\n        // instance's script.\n        this._compatibleControllingSW = this._getControllingSWIfCompatible();\n        this._registration = await this._registerScript();\n        // If we have a compatible controller, store the controller as the \"own\"\n        // SW, resolve active/controlling deferreds and add necessary listeners.\n        if (this._compatibleControllingSW) {\n            this._sw = this._compatibleControllingSW;\n            this._activeDeferred.resolve(this._compatibleControllingSW);\n            this._controllingDeferred.resolve(this._compatibleControllingSW);\n            this._compatibleControllingSW.addEventListener('statechange', this._onStateChange, { once: true });\n        }\n        // If there's a waiting service worker with a matching URL before the\n        // `updatefound` event fires, it likely means that this site is open\n        // in another tab, or the user refreshed the page (and thus the previous\n        // page wasn't fully unloaded before this page started loading).\n        // https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle#waiting\n        const waitingSW = this._registration.waiting;\n        if (waitingSW &&\n            urlsMatch(waitingSW.scriptURL, this._scriptURL.toString())) {\n            // Store the waiting SW as the \"own\" Sw, even if it means overwriting\n            // a compatible controller.\n            this._sw = waitingSW;\n            // Run this in the next microtask, so any code that adds an event\n            // listener after awaiting `register()` will get this event.\n            dontWaitFor(Promise.resolve().then(() => {\n                this.dispatchEvent(new WorkboxEvent('waiting', {\n                    sw: waitingSW,\n                    wasWaitingBeforeRegister: true,\n                }));\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn('A service worker was already waiting to activate ' +\n                        'before this script was registered...');\n                }\n            }));\n        }\n        // If an \"own\" SW is already set, resolve the deferred.\n        if (this._sw) {\n            this._swDeferred.resolve(this._sw);\n            this._ownSWs.add(this._sw);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log('Successfully registered service worker.', this._scriptURL.toString());\n            if (navigator.serviceWorker.controller) {\n                if (this._compatibleControllingSW) {\n                    logger.debug('A service worker with the same script URL ' +\n                        'is already controlling this page.');\n                }\n                else {\n                    logger.debug('A service worker with a different script URL is ' +\n                        'currently controlling the page. The browser is now fetching ' +\n                        'the new script now...');\n                }\n            }\n            const currentPageIsOutOfScope = () => {\n                const scopeURL = new URL(this._registerOptions.scope || this._scriptURL.toString(), document.baseURI);\n                const scopeURLBasePath = new URL('./', scopeURL.href).pathname;\n                return !location.pathname.startsWith(scopeURLBasePath);\n            };\n            if (currentPageIsOutOfScope()) {\n                logger.warn('The current page is not in scope for the registered ' +\n                    'service worker. Was this a mistake?');\n            }\n        }\n        this._registration.addEventListener('updatefound', this._onUpdateFound);\n        navigator.serviceWorker.addEventListener('controllerchange', this._onControllerChange);\n        return this._registration;\n    }\n    /**\n     * Checks for updates of the registered service worker.\n     */\n    async update() {\n        if (!this._registration) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error('Cannot update a Workbox instance without ' +\n                    'being registered. Register the Workbox instance first.');\n            }\n            return;\n        }\n        // Try to update registration\n        await this._registration.update();\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is active. If a service worker was already controlling at registration\n     * time then it will resolve to that if the script URLs (and optionally\n     * script versions) match, otherwise it will wait until an update is found\n     * and activates.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get active() {\n        return this._activeDeferred.promise;\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is controlling the page. If a service worker was already controlling at\n     * registration time then it will resolve to that if the script URLs (and\n     * optionally script versions) match, otherwise it will wait until an update\n     * is found and starts controlling the page.\n     * Note: the first time a service worker is installed it will active but\n     * not start controlling the page unless `clients.claim()` is called in the\n     * service worker.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get controlling() {\n        return this._controllingDeferred.promise;\n    }\n    /**\n     * Resolves with a reference to a service worker that matches the script URL\n     * of this instance, as soon as it's available.\n     *\n     * If, at registration time, there's already an active or waiting service\n     * worker with a matching script URL, it will be used (with the waiting\n     * service worker taking precedence over the active service worker if both\n     * match, since the waiting service worker would have been registered more\n     * recently).\n     * If there's no matching active or waiting service worker at registration\n     * time then the promise will not resolve until an update is found and starts\n     * installing, at which point the installing service worker is used.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    getSW() {\n        // If `this._sw` is set, resolve with that as we want `getSW()` to\n        // return the correct (new) service worker if an update is found.\n        return this._sw !== undefined\n            ? Promise.resolve(this._sw)\n            : this._swDeferred.promise;\n    }\n    /**\n     * Sends the passed data object to the service worker registered by this\n     * instance (via {@link workbox-window.Workbox#getSW}) and resolves\n     * with a response (if any).\n     *\n     * A response can be set in a message handler in the service worker by\n     * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n     * returned by `messageSW()`. If no response is set, the promise will never\n     * resolve.\n     *\n     * @param {Object} data An object to send to the service worker\n     * @return {Promise<Object>}\n     */\n    // We might be able to change the 'data' type to Record<string, unknown> in the future.\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    async messageSW(data) {\n        const sw = await this.getSW();\n        return messageSW(sw, data);\n    }\n    /**\n     * Sends a `{type: 'SKIP_WAITING'}` message to the service worker that's\n     * currently in the `waiting` state associated with the current registration.\n     *\n     * If there is no current registration or no service worker is `waiting`,\n     * calling this will have no effect.\n     */\n    messageSkipWaiting() {\n        if (this._registration && this._registration.waiting) {\n            void messageSW(this._registration.waiting, SKIP_WAITING_MESSAGE);\n        }\n    }\n    /**\n     * Checks for a service worker already controlling the page and returns\n     * it if its script URL matches.\n     *\n     * @private\n     * @return {ServiceWorker|undefined}\n     */\n    _getControllingSWIfCompatible() {\n        const controller = navigator.serviceWorker.controller;\n        if (controller &&\n            urlsMatch(controller.scriptURL, this._scriptURL.toString())) {\n            return controller;\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Registers a service worker for this instances script URL and register\n     * options and tracks the time registration was complete.\n     *\n     * @private\n     */\n    async _registerScript() {\n        try {\n            // this._scriptURL may be a TrustedScriptURL, but there's no support for\n            // passing that to register() in lib.dom right now.\n            // https://github.com/GoogleChrome/workbox/issues/2855\n            const reg = await navigator.serviceWorker.register(this._scriptURL, this._registerOptions);\n            // Keep track of when registration happened, so it can be used in the\n            // `this._onUpdateFound` heuristic. Also use the presence of this\n            // property as a way to see if `.register()` has been called.\n            this._registrationTime = performance.now();\n            return reg;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(error);\n            }\n            // Re-throw the error.\n            throw error;\n        }\n    }\n}\nexport { Workbox };\n// The jsdoc comments below outline the events this instance may dispatch:\n// -----------------------------------------------------------------------\n/**\n * The `message` event is dispatched any time a `postMessage` is received.\n *\n * @event workbox-window.Workbox#message\n * @type {WorkboxEvent}\n * @property {*} data The `data` property from the original `message` event.\n * @property {Event} originalEvent The original [`message`]{@link https://developer.mozilla.org/en-US/docs/Web/API/MessageEvent}\n *     event.\n * @property {string} type `message`.\n * @property {MessagePort[]} ports The `ports` value from `originalEvent`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `installed` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `installed`.\n *\n * Then can happen either the very first time a service worker is installed,\n * or after an update to the current service worker is found. In the case\n * of an update being found, the event's `isUpdate` property will be `true`.\n *\n * @event workbox-window.Workbox#installed\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `installed`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `waiting` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `installed` and then doesn't immediately change to `activating`.\n * It may also be dispatched if a service worker with the same\n * [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * was already waiting when the {@link workbox-window.Workbox#register}\n * method was called.\n *\n * @event workbox-window.Workbox#waiting\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event|undefined} originalEvent The original\n *    [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event, or `undefined` in the case where the service worker was waiting\n *     to before `.register()` was called.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {boolean|undefined} wasWaitingBeforeRegister True if a service worker with\n *     a matching `scriptURL` was already waiting when this `Workbox`\n *     instance called `register()`.\n * @property {string} type `waiting`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `controlling` event is dispatched if a\n * [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n * fires on the service worker [container]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer}\n * and the [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * of the new [controller]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/controller}\n * matches the `scriptURL` of the `Workbox` instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}.\n *\n * @event workbox-window.Workbox#controlling\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this service worker was registered.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `controlling`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `activated` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `activated`.\n *\n * @event workbox-window.Workbox#activated\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `activated`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `redundant` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `redundant`.\n *\n * @event workbox-window.Workbox#redundant\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {string} type `redundant`.\n * @property {Workbox} target The `Workbox` instance.\n */\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n/**\n * A minimal `EventTarget` shim.\n * This is necessary because not all browsers support constructable\n * `EventTarget`, so using a real `EventTarget` will error.\n * @private\n */\nexport class WorkboxEventTarget {\n    constructor() {\n        this._eventListenerRegistry = new Map();\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    addEventListener(type, listener) {\n        const foo = this._getEventListenersByType(type);\n        foo.add(listener);\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    removeEventListener(type, listener) {\n        this._getEventListenersByType(type).delete(listener);\n    }\n    /**\n     * @param {Object} event\n     * @private\n     */\n    dispatchEvent(event) {\n        event.target = this;\n        const listeners = this._getEventListenersByType(event.type);\n        for (const listener of listeners) {\n            listener(event);\n        }\n    }\n    /**\n     * Returns a Set of listeners associated with the passed event type.\n     * If no handlers have been registered, an empty Set is returned.\n     *\n     * @param {string} type The event type.\n     * @return {Set<ListenerCallback>} An array of handler functions.\n     * @private\n     */\n    _getEventListenersByType(type) {\n        if (!this._eventListenerRegistry.has(type)) {\n            this._eventListenerRegistry.set(type, new Set());\n        }\n        return this._eventListenerRegistry.get(type);\n    }\n}\n"], "names": ["self", "_", "e", "messageSW", "sw", "data", "Promise", "resolve", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "postMessage", "port2", "Deferred", "_this", "this", "promise", "reject", "urlsMatch", "url1", "url2", "href", "location", "URL", "WorkboxEvent", "type", "props", "Object", "assign", "_await", "value", "then", "direct", "_empty", "SKIP_WAITING_MESSAGE", "_awaitIgnored", "Workbox", "_WorkboxEventTarget", "scriptURL", "registerOptions", "f", "call", "_registerOptions", "_updateFoundCount", "_swDeferred", "_activeD<PERSON><PERSON>red", "_controlling<PERSON><PERSON><PERSON><PERSON>", "_registrationTime", "_ownSWs", "Set", "_onUpdateFound", "registration", "_registration", "installingSW", "installing", "_scriptURL", "toString", "performance", "now", "_externalSW", "removeEventListener", "_sw", "add", "addEventListener", "_onStateChange", "originalEvent", "target", "state", "isExternal", "eventProps", "_isUpdate", "isUpdate", "dispatchEvent", "_waitingTimeout", "setTimeout", "waiting", "clearTimeout", "_onControllerChange", "navigator", "serviceWorker", "controller", "_onMessage", "ports", "source", "getSW", "has", "args", "i", "arguments", "length", "apply", "_proto", "prototype", "register", "_temp", "_ref$immediate", "immediate", "_this2", "body", "result", "_invoke", "document", "readyState", "res", "window", "Boolean", "_compatibleControllingSW", "_getControllingSWIfCompatible", "_registerScript", "_this2$_registerScrip", "once", "waitingSW", "wasWaitingBeforeRegister", "update", "undefined", "messageSkipWaiting", "_this5", "recover", "_catch", "reg", "error", "key", "get", "WorkboxEventTarget", "_eventListenerRegistry", "Map", "listener", "_getEventListenersByType", "delete", "_step", "_iterator", "_createForOfIteratorHelperLoose", "done", "set"], "mappings": "+OAEA,IACIA,KAAK,yBAA2BC,GACpC,CACA,MAAOC,GAAG,CCmBV,SAASC,EAAUC,EAAIC,GACnB,OAAO,IAAIC,SAAQ,SAACC,GAChB,IAAMC,EAAiB,IAAIC,eAC3BD,EAAeE,MAAMC,UAAY,SAACC,GAC9BL,EAAQK,EAAMP,OAElBD,EAAGS,YAAYR,EAAM,CAACG,EAAeM,OACzC,GACJ,45CC9BA,IACId,KAAK,uBAAyBC,GAClC,CACA,MAAOC,GAAG,CCGV,IAQMa,EAIF,WAAc,IAAAC,EAAAC,KACVA,KAAKC,QAAU,IAAIZ,SAAQ,SAACC,EAASY,GACjCH,EAAKT,QAAUA,EACfS,EAAKG,OAASA,CAClB,GACJ,ECRG,SAASC,EAAUC,EAAMC,GAC5B,IAAQC,EAASC,SAATD,KACR,OAAO,IAAIE,IAAIJ,EAAME,GAAMA,OAAS,IAAIE,IAAIH,EAAMC,GAAMA,IAC5D,CCNaG,IAAAA,EACT,SAAYC,EAAMC,GACdX,KAAKU,KAAOA,EACZE,OAAOC,OAAOb,KAAMW,EACxB,ECkEG,SAASG,EAAOC,EAAOC,EAAMC,GACnC,OAAIA,EACID,EAAOA,EAAKD,GAASA,GAExBA,GAAUA,EAAMC,OACpBD,EAAQ1B,QAAQC,QAAQyB,IAElBC,EAAOD,EAAMC,KAAKA,GAAQD,EAClC,CAogBO,SAASG,IAChB,CAzkBA,IAAMC,EAAuB,CAAET,KAAM,gBAuE9B,SAASU,EAAcL,EAAOE,GACpC,IAAKA,EACJ,OAAOF,GAASA,EAAMC,KAAOD,EAAMC,KAAKE,GAAU7B,QAAQC,SAE5D,CA9DM+B,IAAAA,WAAOC,GAaT,SAAAD,EAAYE,EAAWC,GAAsB,IAAAzB,EAoB1B0B,EAsMsD,YA1NnC,IAAfD,IAAAA,EAAkB,CAAA,IACrCzB,EAAAuB,EAAAI,YAAO1B,MACF2B,GAAmB,GACxB5B,EAAK6B,GAAoB,EAEzB7B,EAAK8B,GAAc,IAAI/B,EACvBC,EAAK+B,GAAkB,IAAIhC,EAC3BC,EAAKgC,GAAuB,IAAIjC,EAChCC,EAAKiC,GAAoB,EACzBjC,EAAKkC,GAAU,IAAIC,IAInBnC,EAAKoC,GAAiB,WAElB,IAAMC,EAAerC,EAAKsC,GACpBC,EAAeF,EAAaG,WAkBlCxC,EAAK6B,GAAoB,IAIpBzB,EAAUmC,EAAaf,UAAWxB,EAAKyC,GAAWC,aAInDC,YAAYC,MAAQ5C,EAAKiC,GAvEH,KA6EtBjC,EAAK6C,GAAcN,EACnBF,EAAaS,oBAAoB,cAAe9C,EAAKoC,MAKrDpC,EAAK+C,GAAMR,EACXvC,EAAKkC,GAAQc,IAAIT,GACjBvC,EAAK8B,GAAYvC,QAAQgD,MAc3BvC,EAAK6B,GAGPU,EAAaU,iBAAiB,cAAejD,EAAKkD,KAMtDlD,EAAKkD,GAAiB,SAACC,GAEnB,IAAMd,EAAerC,EAAKsC,GACpBlD,EAAK+D,EAAcC,OACjBC,EAAUjE,EAAViE,MACFC,EAAalE,IAAOY,EAAK6C,GACzBU,EAAa,CACfnE,GAAAA,EACAkE,WAAAA,EACAH,cAAAA,IAECG,GAActD,EAAKwD,KACpBD,EAAWE,UAAW,GAE1BzD,EAAK0D,cAAc,IAAIhD,EAAa2C,EAAOE,IAC7B,cAAVF,EASArD,EAAK2D,GAAkB3E,KAAK4E,YAAW,WAErB,cAAVP,GAAyBhB,EAAawB,UAAYzE,GAClDY,EAAK0D,cAAc,IAAIhD,EAAa,UAAW6C,GAYtD,GAtJgB,KAwJF,eAAVF,IACLS,aAAa9D,EAAK2D,IACbL,GACDtD,EAAK+B,GAAgBxC,QAAQH,KA0CzCY,EAAK+D,GAAsB,SAACZ,GACxB,IAAM/D,EAAKY,EAAK+C,GACVO,EAAalE,IAAO4E,UAAUC,cAAcC,WAKlDlE,EAAK0D,cAAc,IAAIhD,EAAa,cAAe,CAC/C4C,WAAAA,EACAH,cAAAA,EACA/D,GAAAA,EACAqE,SAAUzD,EAAKwD,MAEdF,GAIDtD,EAAKgC,GAAqBzC,QAAQH,IAO1CY,EAAKmE,IAzKUzC,EAyKA,SAAUyB,GAGrB,IAAQ9D,EAAwB8D,EAAxB9D,KAAM+E,EAAkBjB,EAAlBiB,MAAOC,EAAWlB,EAAXkB,OAErB,OAAAtD,EACMf,EAAKsE,SAAO,WAOdtE,EAAKkC,GAAQqC,IAAIF,IACjBrE,EAAK0D,cAAc,IAAIhD,EAAa,UAAW,CAG3CrB,KAAAA,EACA8D,cAAAA,EACAiB,MAAAA,EACAhF,GAAIiF,SA5LhB,WACN,IAAK,IAAIG,EAAO,GAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAChDD,EAAKC,GAAKC,UAAUD,GAErB,IACC,OAAOnF,QAAQC,QAAQmC,EAAEkD,MAAM3E,KAAMuE,GACrC,CAAC,MAAMtF,GACP,OAAOI,QAAQa,OAAOjB,EACvB,IAwLMc,EAAKyC,GAAajB,EAClBxB,EAAK4B,GAAmBH,EAIxBuC,UAAUC,cAAchB,iBAAiB,UAAWjD,EAAKmE,IAAYnE,CACzE,WACAuB,KAAAD,yEAAA,UAAAuD,EAAAvD,EAAAwD,UAqOC,OArODD,EAUME,SAAQA,SAAAC,GAAA,IAA2BC,YAA3BD,EAAyB,CAAE,EAAAA,GAAxBE,UAAAA,OAAY,IAAHD,GAAQA,EAAA,IAAS,IAAAE,EAE/BlF,KAKP,OAAAc,EAkQF,SAAiBqE,EAAMnE,GAC7B,IAAIoE,EAASD,IACb,GAAIC,GAAUA,EAAOpE,KACpB,OAAOoE,EAAOpE,KAAKA,GAEpB,OAAOA,EAAKoE,EACb,CAxQSC,EAAA,WAAA,IACIJ,GAAqC,aAAxBK,SAASC,WAAyB,OAAAnE,EAC1C,IAAI/B,SAAQ,SAACmG,GAAG,OAAKC,OAAOzC,iBAAiB,OAAQwC,EAAK,IAAA,IAAA,WAQC,OAJrEN,EAAK3B,GAAYmC,QAAQ3B,UAAUC,cAAcC,YAIjDiB,EAAKS,GAA2BT,EAAKU,KAAgC9E,EAC1CoE,EAAKW,eAAiBC,GAAjDZ,EAAK7C,GAAayD,EAGdZ,EAAKS,KACLT,EAAKpC,GAAMoC,EAAKS,GAChBT,EAAKpD,GAAgBxC,QAAQ4F,EAAKS,IAClCT,EAAKnD,GAAqBzC,QAAQ4F,EAAKS,IACvCT,EAAKS,GAAyB3C,iBAAiB,cAAekC,EAAKjC,GAAgB,CAAE8C,MAAM,KAO/F,IAAMC,EAAYd,EAAK7C,GAAcuB,QAiDrC,OAhDIoC,GACA7F,EAAU6F,EAAUzE,UAAW2D,EAAK1C,GAAWC,cAG/CyC,EAAKpC,GAAMkD,EAGC3G,QAAQC,UAAU0B,MAAK,WAC/BkE,EAAKzB,cAAc,IAAIhD,EAAa,UAAW,CAC3CtB,GAAI6G,EACJC,0BAA0B,IAMjC,IC3TIjF,MAAK,WAAM,KD8ThBkE,EAAKpC,KACLoC,EAAKrD,GAAYvC,QAAQ4F,EAAKpC,IAC9BoC,EAAKjD,GAAQc,IAAImC,EAAKpC,KAyB1BoC,EAAK7C,GAAcW,iBAAiB,cAAekC,EAAK/C,IACxD4B,UAAUC,cAAchB,iBAAiB,mBAAoBkC,EAAKpB,IAC3DoB,EAAK7C,EAAc,GAAA,IAC7B,OAAApD,GAAA,OAAAI,QAAAa,OAAAjB,EAAA,CAAA,EACD2F,EAGMsB,OAAM,WAAA,IACR,OAAKlG,KAAKqC,GAOVvB,EAAAM,EAPKpB,KAQMqC,GAAc6D,WAHrBpF,GAIP,OAAA7B,GAAA,OAAAI,QAAAa,OAAAjB,EAAA,CAAA,EA4BD2F,EAeAP,MAAA,WAGI,YAAoB8B,IAAbnG,KAAK8C,GACNzD,QAAQC,QAAQU,KAAK8C,IACrB9C,KAAK6B,GAAY5B,OAC3B,EAeA2E,EACM1F,UAASA,SAACE,GAAI,IACK,OAAA0B,EAAJd,KAAKqE,kBAAhBlF,GACN,OAAOD,EAAUC,EAAIC,EAAM,GAC9B,OAAAH,GAAA,OAAAI,QAAAa,OAAAjB,EAAA,CAAA,EACD2F,EAOAwB,mBAAA,WACQpG,KAAKqC,IAAiBrC,KAAKqC,GAAcuB,SACpC1E,EAAUc,KAAKqC,GAAcuB,QAASzC,EAEnD,EACAyD,EAOAgB,GAAA,WACI,IAAM3B,EAAaF,UAAUC,cAAcC,WAC3C,OAAIA,GACA9D,EAAU8D,EAAW1C,UAAWvB,KAAKwC,GAAWC,YACzCwB,OAGP,CAER,EACAW,EAMMiB,GAAe,WAAA,IAAG,IAAAQ,EAKmCrG,KAAI,OAAAc,EA6E5D,SAAgBqE,EAAMmB,GAC5B,IACC,IAAIlB,EAASD,GACb,CAAC,MAAMlG,GACP,OAAOqH,EAAQrH,EAChB,CACA,GAAImG,GAAUA,EAAOpE,KACpB,OAAOoE,EAAOpE,UAAK,EAAQsF,GAE5B,OAAOlB,CACR,CAvFmEmB,EAJvD,WAGA,OAAAzF,EACkBiD,UAAUC,cAAcc,SAASuB,EAAK7D,GAAY6D,EAAK1E,cAAnE6E,GAKN,OADAH,EAAKrE,GAAoBU,YAAYC,MAC9B6D,CAAI,GACd,IAAA,SACMC,GAKH,MAAMA,CACT,IACJ,OAAAxH,GAAA,OAAAI,QAAAa,OAAAjB,EAAA,CAAA,IAAAoC,KAAA,CAAA,CAAAqF,IAAA,SAAAC,IAjHD,WACI,OAAO3G,KAAK8B,GAAgB7B,OAChC,GACA,CAAAyG,IAAA,cAAAC,IAYA,WACI,OAAO3G,KAAK+B,GAAqB9B,OACrC,qFAAC,EEtY0B,WAC3B,SAAA2G,IACI5G,KAAK6G,GAAyB,IAAIC,GACtC,CACA,IAAAlC,EAAAgC,EAAA/B,UAyCC,OAzCDD,EAKA5B,iBAAA,SAAiBtC,EAAMqG,GACP/G,KAAKgH,GAAyBtG,GACtCqC,IAAIgE,EACZ,EACAnC,EAKA/B,oBAAA,SAAoBnC,EAAMqG,GACtB/G,KAAKgH,GAAyBtG,GAAMuG,OAAOF,EAC/C,EACAnC,EAIAnB,cAAA,SAAc9D,GACVA,EAAMwD,OAASnD,KAEf,IADA,IACgCkH,EAAhCC,EAAAC,EADkBpH,KAAKgH,GAAyBrH,EAAMe,SACtBwG,EAAAC,KAAAE,MAAE,EAC9BN,EADeG,EAAAnG,OACNpB,EACb,CACJ,EACAiF,EAQAoC,GAAA,SAAyBtG,GAIrB,OAHKV,KAAK6G,GAAuBvC,IAAI5D,IACjCV,KAAK6G,GAAuBS,IAAI5G,EAAM,IAAIwB,KAEvClC,KAAK6G,GAAuBF,IAAIjG,IAC1CkG,CAAA,CA7C0B"}
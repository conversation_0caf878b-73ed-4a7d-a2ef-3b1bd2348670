!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).workbox={})}(this,(function(t){"use strict";try{self["workbox:window:7.2.0"]&&_()}catch(t){}function n(t,n){return new Promise((function(r){var e=new MessageChannel;e.port1.onmessage=function(t){r(t.data)},t.postMessage(n,[e.port2])}))}function r(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,n||"default");if("object"!=typeof e)return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}function e(t,n){for(var e=0;e<n.length;e++){var i=n[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,r(i.key),i)}}function i(t,n){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},i(t,n)}function o(t,n){(null==n||n>t.length)&&(n=t.length);for(var r=0,e=new Array(n);r<n;r++)e[r]=t[r];return e}function u(t,n){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,n){if(t){if("string"==typeof t)return o(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(t,n):void 0}}(t))||n&&t&&"number"==typeof t.length){r&&(t=r);var e=0;return function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}try{self["workbox:core:7.2.0"]&&_()}catch(t){}var a=function(){var t=this;this.promise=new Promise((function(n,r){t.resolve=n,t.reject=r}))};function c(t,n){var r=location.href;return new URL(t,r).href===new URL(n,r).href}var f=function(t,n){this.type=t,Object.assign(this,n)};function s(t,n,r){return r?n?n(t):t:(t&&t.then||(t=Promise.resolve(t)),n?t.then(n):t)}function v(){}var h={type:"SKIP_WAITING"};function l(t,n){if(!n)return t&&t.then?t.then(v):Promise.resolve()}var d=function(t){function r(n,r){var e,i;return void 0===r&&(r={}),(e=t.call(this)||this).At={},e.It=0,e.Tt=new a,e.Mt=new a,e.Bt=new a,e.Lt=0,e.Nt=new Set,e.Gt=function(){var t=e.Kt,n=t.installing;e.It>0||!c(n.scriptURL,e.zt.toString())||performance.now()>e.Lt+6e4?(e.Dt=n,t.removeEventListener("updatefound",e.Gt)):(e.Ft=n,e.Nt.add(n),e.Tt.resolve(n)),++e.It,n.addEventListener("statechange",e.Ht)},e.Ht=function(t){var n=e.Kt,r=t.target,i=r.state,o=r===e.Dt,u={sw:r,isExternal:o,originalEvent:t};!o&&e.Jt&&(u.isUpdate=!0),e.dispatchEvent(new f(i,u)),"installed"===i?e.Qt=self.setTimeout((function(){"installed"===i&&n.waiting===r&&e.dispatchEvent(new f("waiting",u))}),200):"activating"===i&&(clearTimeout(e.Qt),o||e.Mt.resolve(r))},e.Vt=function(t){var n=e.Ft,r=n!==navigator.serviceWorker.controller;e.dispatchEvent(new f("controlling",{isExternal:r,originalEvent:t,sw:n,isUpdate:e.Jt})),r||e.Bt.resolve(n)},e.Xt=(i=function(t){var n=t.data,r=t.ports,i=t.source;return s(e.getSW(),(function(){e.Nt.has(i)&&e.dispatchEvent(new f("message",{data:n,originalEvent:t,ports:r,sw:i}))}))},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];try{return Promise.resolve(i.apply(this,t))}catch(t){return Promise.reject(t)}}),e.zt=n,e.At=r,navigator.serviceWorker.addEventListener("message",e.Xt),e}var o,u;u=t,(o=r).prototype=Object.create(u.prototype),o.prototype.constructor=o,i(o,u);var v,d,m,y=r.prototype;return y.register=function(t){var n=(void 0===t?{}:t).immediate,r=void 0!==n&&n;try{var e=this;return s(function(t,n){var r=t();if(r&&r.then)return r.then(n);return n(r)}((function(){if(!r&&"complete"!==document.readyState)return l(new Promise((function(t){return window.addEventListener("load",t)})))}),(function(){return e.Jt=Boolean(navigator.serviceWorker.controller),e.Yt=e.Zt(),s(e.tn(),(function(t){e.Kt=t,e.Yt&&(e.Ft=e.Yt,e.Mt.resolve(e.Yt),e.Bt.resolve(e.Yt),e.Yt.addEventListener("statechange",e.Ht,{once:!0}));var n=e.Kt.waiting;return n&&c(n.scriptURL,e.zt.toString())&&(e.Ft=n,Promise.resolve().then((function(){e.dispatchEvent(new f("waiting",{sw:n,wasWaitingBeforeRegister:!0}))})).then((function(){}))),e.Ft&&(e.Tt.resolve(e.Ft),e.Nt.add(e.Ft)),e.Kt.addEventListener("updatefound",e.Gt),navigator.serviceWorker.addEventListener("controllerchange",e.Vt),e.Kt}))})))}catch(t){return Promise.reject(t)}},y.update=function(){try{return this.Kt?s(l(this.Kt.update())):s()}catch(t){return Promise.reject(t)}},y.getSW=function(){return void 0!==this.Ft?Promise.resolve(this.Ft):this.Tt.promise},y.messageSW=function(t){try{return s(this.getSW(),(function(r){return n(r,t)}))}catch(t){return Promise.reject(t)}},y.messageSkipWaiting=function(){this.Kt&&this.Kt.waiting&&n(this.Kt.waiting,h)},y.Zt=function(){var t=navigator.serviceWorker.controller;return t&&c(t.scriptURL,this.zt.toString())?t:void 0},y.tn=function(){try{var t=this;return s(function(t,n){try{var r=t()}catch(t){return n(t)}if(r&&r.then)return r.then(void 0,n);return r}((function(){return s(navigator.serviceWorker.register(t.zt,t.At),(function(n){return t.Lt=performance.now(),n}))}),(function(t){throw t})))}catch(t){return Promise.reject(t)}},v=r,(d=[{key:"active",get:function(){return this.Mt.promise}},{key:"controlling",get:function(){return this.Bt.promise}}])&&e(v.prototype,d),m&&e(v,m),Object.defineProperty(v,"prototype",{writable:!1}),v}(function(){function t(){this.nn=new Map}var n=t.prototype;return n.addEventListener=function(t,n){this.rn(t).add(n)},n.removeEventListener=function(t,n){this.rn(t).delete(n)},n.dispatchEvent=function(t){t.target=this;for(var n,r=u(this.rn(t.type));!(n=r()).done;){(0,n.value)(t)}},n.rn=function(t){return this.nn.has(t)||this.nn.set(t,new Set),this.nn.get(t)},t}());t.Workbox=d,t.WorkboxEvent=f,t.messageSW=n,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=workbox-window.prod.umd.js.map

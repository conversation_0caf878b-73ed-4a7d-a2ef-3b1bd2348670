{"name": "workbox-window", "version": "7.3.0", "license": "MIT", "author": "Google's Web DevRel Team and Google's Aurora Team", "description": "Simplifies communications with Workbox packages running in the service worker", "repository": {"type": "git", "url": "git+https://github.com/googlechrome/workbox.git"}, "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "window", "message", "postMessage"], "workbox": {"packageType": "window", "primaryBuild": "build/workbox-window.prod.mjs"}, "main": "build/workbox-window.prod.umd.js", "module": "build/workbox-window.prod.es5.mjs", "types": "index.d.ts", "dependencies": {"@types/trusted-types": "^2.0.2", "workbox-core": "7.3.0"}, "gitHead": "c77dceb54d4af1749db95316710d6430e82b0c48"}
{"version": 3, "file": "swipe.js", "sources": ["../../src/util/swipe.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n"], "names": ["NAME", "EVENT_KEY", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "CLASS_NAME_POINTER_EVENT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "DefaultType", "Swipe", "Config", "constructor", "element", "config", "_element", "isSupported", "_config", "_getConfig", "_deltaX", "_supportPointerEvents", "Boolean", "window", "PointerEvent", "_initEvents", "dispose", "EventHandler", "off", "_start", "event", "touches", "clientX", "_eventIsPointerPenTouch", "_end", "_handleSwipe", "execute", "_move", "length", "absDeltaX", "Math", "abs", "direction", "on", "classList", "add", "pointerType", "document", "documentElement", "navigator", "maxTouchPoints"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAMA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,OAAO;EACpB,MAAMC,SAAS,GAAG,WAAW;EAC7B,MAAMC,gBAAgB,GAAG,CAAaD,UAAAA,EAAAA,SAAS,CAAE,CAAA;EACjD,MAAME,eAAe,GAAG,CAAYF,SAAAA,EAAAA,SAAS,CAAE,CAAA;EAC/C,MAAMG,cAAc,GAAG,CAAWH,QAAAA,EAAAA,SAAS,CAAE,CAAA;EAC7C,MAAMI,iBAAiB,GAAG,CAAcJ,WAAAA,EAAAA,SAAS,CAAE,CAAA;EACnD,MAAMK,eAAe,GAAG,CAAYL,SAAAA,EAAAA,SAAS,CAAE,CAAA;EAC/C,MAAMM,kBAAkB,GAAG,OAAO;EAClC,MAAMC,gBAAgB,GAAG,KAAK;EAC9B,MAAMC,wBAAwB,GAAG,eAAe;EAChD,MAAMC,eAAe,GAAG,EAAE;EAE1B,MAAMC,OAAO,GAAG;EACdC,EAAAA,WAAW,EAAE,IAAI;EACjBC,EAAAA,YAAY,EAAE,IAAI;EAClBC,EAAAA,aAAa,EAAE;EACjB,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBH,EAAAA,WAAW,EAAE,iBAAiB;EAC9BC,EAAAA,YAAY,EAAE,iBAAiB;EAC/BC,EAAAA,aAAa,EAAE;EACjB,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,KAAK,SAASC,MAAM,CAAC;EACzBC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,KAAK,EAAE;MACP,IAAI,CAACC,QAAQ,GAAGF,OAAO;MAEvB,IAAI,CAACA,OAAO,IAAI,CAACH,KAAK,CAACM,WAAW,EAAE,EAAE;EACpC,MAAA;EACF;MAEA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACJ,MAAM,CAAC;MACtC,IAAI,CAACK,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,qBAAqB,GAAGC,OAAO,CAACC,MAAM,CAACC,YAAY,CAAC;MACzD,IAAI,CAACC,WAAW,EAAE;EACpB;;EAEA;IACA,WAAWnB,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB;IAEA,WAAWI,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB;IAEA,WAAWf,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb;;EAEA;EACA+B,EAAAA,OAAOA,GAAG;MACRC,YAAY,CAACC,GAAG,CAAC,IAAI,CAACZ,QAAQ,EAAEpB,SAAS,CAAC;EAC5C;;EAEA;IACAiC,MAAMA,CAACC,KAAK,EAAE;EACZ,IAAA,IAAI,CAAC,IAAI,CAACT,qBAAqB,EAAE;QAC/B,IAAI,CAACD,OAAO,GAAGU,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;EAEvC,MAAA;EACF;EAEA,IAAA,IAAI,IAAI,CAACC,uBAAuB,CAACH,KAAK,CAAC,EAAE;EACvC,MAAA,IAAI,CAACV,OAAO,GAAGU,KAAK,CAACE,OAAO;EAC9B;EACF;IAEAE,IAAIA,CAACJ,KAAK,EAAE;EACV,IAAA,IAAI,IAAI,CAACG,uBAAuB,CAACH,KAAK,CAAC,EAAE;QACvC,IAAI,CAACV,OAAO,GAAGU,KAAK,CAACE,OAAO,GAAG,IAAI,CAACZ,OAAO;EAC7C;MAEA,IAAI,CAACe,YAAY,EAAE;EACnBC,IAAAA,gBAAO,CAAC,IAAI,CAAClB,OAAO,CAACX,WAAW,CAAC;EACnC;IAEA8B,KAAKA,CAACP,KAAK,EAAE;EACX,IAAA,IAAI,CAACV,OAAO,GAAGU,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,CAACO,MAAM,GAAG,CAAC,GACtD,CAAC,GACDR,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,IAAI,CAACZ,OAAO;EAC3C;EAEAe,EAAAA,YAAYA,GAAG;MACb,MAAMI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,OAAO,CAAC;MAExC,IAAImB,SAAS,IAAIlC,eAAe,EAAE;EAChC,MAAA;EACF;EAEA,IAAA,MAAMqC,SAAS,GAAGH,SAAS,GAAG,IAAI,CAACnB,OAAO;MAE1C,IAAI,CAACA,OAAO,GAAG,CAAC;MAEhB,IAAI,CAACsB,SAAS,EAAE;EACd,MAAA;EACF;EAEAN,IAAAA,gBAAO,CAACM,SAAS,GAAG,CAAC,GAAG,IAAI,CAACxB,OAAO,CAACT,aAAa,GAAG,IAAI,CAACS,OAAO,CAACV,YAAY,CAAC;EACjF;EAEAiB,EAAAA,WAAWA,GAAG;MACZ,IAAI,IAAI,CAACJ,qBAAqB,EAAE;EAC9BM,MAAAA,YAAY,CAACgB,EAAE,CAAC,IAAI,CAAC3B,QAAQ,EAAEhB,iBAAiB,EAAE8B,KAAK,IAAI,IAAI,CAACD,MAAM,CAACC,KAAK,CAAC,CAAC;EAC9EH,MAAAA,YAAY,CAACgB,EAAE,CAAC,IAAI,CAAC3B,QAAQ,EAAEf,eAAe,EAAE6B,KAAK,IAAI,IAAI,CAACI,IAAI,CAACJ,KAAK,CAAC,CAAC;QAE1E,IAAI,CAACd,QAAQ,CAAC4B,SAAS,CAACC,GAAG,CAACzC,wBAAwB,CAAC;EACvD,KAAC,MAAM;EACLuB,MAAAA,YAAY,CAACgB,EAAE,CAAC,IAAI,CAAC3B,QAAQ,EAAEnB,gBAAgB,EAAEiC,KAAK,IAAI,IAAI,CAACD,MAAM,CAACC,KAAK,CAAC,CAAC;EAC7EH,MAAAA,YAAY,CAACgB,EAAE,CAAC,IAAI,CAAC3B,QAAQ,EAAElB,eAAe,EAAEgC,KAAK,IAAI,IAAI,CAACO,KAAK,CAACP,KAAK,CAAC,CAAC;EAC3EH,MAAAA,YAAY,CAACgB,EAAE,CAAC,IAAI,CAAC3B,QAAQ,EAAEjB,cAAc,EAAE+B,KAAK,IAAI,IAAI,CAACI,IAAI,CAACJ,KAAK,CAAC,CAAC;EAC3E;EACF;IAEAG,uBAAuBA,CAACH,KAAK,EAAE;EAC7B,IAAA,OAAO,IAAI,CAACT,qBAAqB,KAAKS,KAAK,CAACgB,WAAW,KAAK3C,gBAAgB,IAAI2B,KAAK,CAACgB,WAAW,KAAK5C,kBAAkB,CAAC;EAC3H;;EAEA;IACA,OAAOe,WAAWA,GAAG;MACnB,OAAO,cAAc,IAAI8B,QAAQ,CAACC,eAAe,IAAIC,SAAS,CAACC,cAAc,GAAG,CAAC;EACnF;EACF;;;;;;;;"}
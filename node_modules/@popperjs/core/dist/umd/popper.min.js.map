{"version": 3, "file": "popper.min.js", "sources": ["../../src/dom-utils/getWindow.js", "../../src/dom-utils/instanceOf.js", "../../src/utils/math.js", "../../src/utils/userAgent.js", "../../src/dom-utils/isLayoutViewport.js", "../../src/dom-utils/getBoundingClientRect.js", "../../src/dom-utils/getWindowScroll.js", "../../src/dom-utils/getNodeName.js", "../../src/dom-utils/getDocumentElement.js", "../../src/dom-utils/getWindowScrollBarX.js", "../../src/dom-utils/getComputedStyle.js", "../../src/dom-utils/isScrollParent.js", "../../src/dom-utils/getCompositeRect.js", "../../src/dom-utils/getNodeScroll.js", "../../src/dom-utils/getHTMLElementScroll.js", "../../src/dom-utils/getLayoutRect.js", "../../src/dom-utils/getParentNode.js", "../../src/dom-utils/getScrollParent.js", "../../src/dom-utils/listScrollParents.js", "../../src/dom-utils/isTableElement.js", "../../src/dom-utils/getOffsetParent.js", "../../src/enums.js", "../../src/utils/orderModifiers.js", "../../src/dom-utils/contains.js", "../../src/utils/rectToClientRect.js", "../../src/dom-utils/getClippingRect.js", "../../src/dom-utils/getViewportRect.js", "../../src/dom-utils/getDocumentRect.js", "../../src/utils/getBasePlacement.js", "../../src/utils/getVariation.js", "../../src/utils/getMainAxisFromPlacement.js", "../../src/utils/computeOffsets.js", "../../src/utils/mergePaddingObject.js", "../../src/utils/getFreshSideObject.js", "../../src/utils/expandToHashMap.js", "../../src/utils/detectOverflow.js", "../../src/createPopper.js", "../../src/utils/debounce.js", "../../src/utils/mergeByName.js", "../../src/modifiers/eventListeners.js", "../../src/modifiers/popperOffsets.js", "../../src/modifiers/computeStyles.js", "../../src/modifiers/applyStyles.js", "../../src/modifiers/offset.js", "../../src/utils/getOppositePlacement.js", "../../src/utils/getOppositeVariationPlacement.js", "../../src/utils/computeAutoPlacement.js", "../../src/modifiers/flip.js", "../../src/utils/within.js", "../../src/modifiers/preventOverflow.js", "../../src/utils/getAltAxis.js", "../../src/modifiers/arrow.js", "../../src/modifiers/hide.js", "../../src/popper-lite.js", "../../src/popper.js"], "sourcesContent": ["// @flow\nimport type { Window } from '../types';\ndeclare function getWindow(node: Node | Window): Window;\n\nexport default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    const ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n", "// @flow\nimport getWindow from './getWindow';\n\ndeclare function isElement(node: mixed): boolean %checks(node instanceof\n  Element);\nfunction isElement(node) {\n  const OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\ndeclare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement);\nfunction isHTMLElement(node) {\n  const OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\ndeclare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot);\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };\n", "// @flow\nexport const max = Math.max;\nexport const min = Math.min;\nexport const round = Math.round;\n", "// @flow\ntype Navigator = Navigator & { userAgentData?: NavigatorUAData };\n\ninterface NavigatorUAData {\n  brands: Array<{ brand: string, version: string }>;\n  mobile: boolean;\n  platform: string;\n}\n\nexport default function getUAString(): string {\n  const uaData = (navigator: Navigator).userAgentData;\n\n  if (uaData?.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands\n      .map((item) => `${item.brand}/${item.version}`)\n      .join(' ');\n  }\n\n  return navigator.userAgent;\n}\n", "// @flow\nimport getUAString from '../utils/userAgent';\n\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n", "// @flow\nimport type { ClientRectObject, VirtualElement } from '../types';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport { round } from '../utils/math';\nimport getWindow from './getWindow';\nimport isLayoutViewport from './isLayoutViewport';\n\nexport default function getBoundingClientRect(\n  element: Element | VirtualElement,\n  includeScale: boolean = false,\n  isFixedStrategy: boolean = false\n): ClientRectObject {\n  const clientRect = element.getBoundingClientRect();\n  let scaleX = 1;\n  let scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX =\n      (element: HTMLElement).offsetWidth > 0\n        ? round(clientRect.width) / (element: HTMLElement).offsetWidth || 1\n        : 1;\n    scaleY =\n      (element: HTMLElement).offsetHeight > 0\n        ? round(clientRect.height) / (element: HTMLElement).offsetHeight || 1\n        : 1;\n  }\n\n  const { visualViewport } = isElement(element) ? getWindow(element) : window;\n  const addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n\n  const x =\n    (clientRect.left +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) /\n    scaleX;\n  const y =\n    (clientRect.top +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) /\n    scaleY;\n  const width = clientRect.width / scaleX;\n  const height = clientRect.height / scaleY;\n\n  return {\n    width,\n    height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x,\n    y,\n  };\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport type { Window } from '../types';\n\nexport default function getWindowScroll(node: Node | Window) {\n  const win = getWindow(node);\n  const scrollLeft = win.pageXOffset;\n  const scrollTop = win.pageYOffset;\n\n  return {\n    scrollLeft,\n    scrollTop,\n  };\n}\n", "// @flow\nimport type { Window } from '../types';\n\nexport default function getNodeName(element: ?Node | Window): ?string {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n", "// @flow\nimport { isElement } from './instanceOf';\nimport type { Window } from '../types';\n\nexport default function getDocumentElement(\n  element: Element | Window\n): HTMLElement {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (\n    (isElement(element)\n      ? element.ownerDocument\n      : // $FlowFixMe[prop-missing]\n        element.document) || window.document\n  ).documentElement;\n}\n", "// @flow\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScroll from './getWindowScroll';\n\nexport default function getWindowScrollBarX(element: Element): number {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (\n    getBoundingClientRect(getDocumentElement(element)).left +\n    getWindowScroll(element).scrollLeft\n  );\n}\n", "// @flow\nimport getWindow from './getWindow';\n\nexport default function getComputedStyle(\n  element: Element\n): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "// @flow\nimport getComputedStyle from './getComputedStyle';\n\nexport default function isScrollParent(element: HTMLElement): boolean {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n", "// @flow\nimport type { Rect, VirtualElement, Window } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getNodeScroll from './getNodeScroll';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getDocumentElement from './getDocumentElement';\nimport isScrollParent from './isScrollParent';\nimport { round } from '../utils/math';\n\nfunction isElementScaled(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n  const scaleX = round(rect.width) / element.offsetWidth || 1;\n  const scaleY = round(rect.height) / element.offsetHeight || 1;\n\n  return scaleX !== 1 || scaleY !== 1;\n}\n\n// Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nexport default function getCompositeRect(\n  elementOrVirtualElement: Element | VirtualElement,\n  offsetParent: Element | Window,\n  isFixed: boolean = false\n): Rect {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const offsetParentIsScaled =\n    isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(\n    elementOrVirtualElement,\n    offsetParentIsScaled,\n    isFixed\n  );\n\n  let scroll = { scrollLeft: 0, scrollTop: 0 };\n  let offsets = { x: 0, y: 0 };\n\n  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {\n    if (\n      getNodeName(offsetParent) !== 'body' ||\n      // https://github.com/popperjs/popper-core/issues/1078\n      isScrollParent(documentElement)\n    ) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height,\n  };\n}\n", "// @flow\nimport getWindowScroll from './getWindowScroll';\nimport getWindow from './getWindow';\nimport { isHTMLElement } from './instanceOf';\nimport getHTMLElementScroll from './getHTMLElementScroll';\nimport type { Window } from '../types';\n\nexport default function getNodeScroll(node: Node | Window) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n", "// @flow\n\nexport default function getHTMLElementScroll(element: HTMLElement) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\n\n// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element: HTMLElement): Rect {\n  const clientRect = getBoundingClientRect(element);\n\n  // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width,\n    height,\n  };\n}\n", "// @flow\nimport getNodeName from './getNodeName';\nimport getDocumentElement from './getDocumentElement';\nimport { isShadowRoot } from './instanceOf';\n\nexport default function getParentNode(element: Node | ShadowRoot): Node {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}\n", "// @flow\nimport getParentNode from './getParentNode';\nimport isScrollParent from './isScrollParent';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\n\nexport default function getScrollParent(node: Node): HTMLElement {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n", "// @flow\nimport getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getWindow from './getWindow';\nimport type { Window, VisualViewport } from '../types';\nimport isScrollParent from './isScrollParent';\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\nexport default function listScrollParents(\n  element: Node,\n  list: Array<Element | Window> = []\n): Array<Element | Window | VisualViewport> {\n  const scrollParent = getScrollParent(element);\n  const isBody = scrollParent === element.ownerDocument?.body;\n  const win = getWindow(scrollParent);\n  const target = isBody\n    ? [win].concat(\n        win.visualViewport || [],\n        isScrollParent(scrollParent) ? scrollParent : []\n      )\n    : scrollParent;\n  const updatedList = list.concat(target);\n\n  return isBody\n    ? updatedList\n    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n      updatedList.concat(listScrollParents(getParentNode(target)));\n}\n", "// @flow\nimport getNodeName from './getNodeName';\n\nexport default function isTableElement(element: Element): boolean {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getNodeName from './getNodeName';\nimport getComputedStyle from './getComputedStyle';\nimport { isHTMLElement, isShadowRoot } from './instanceOf';\nimport isTableElement from './isTableElement';\nimport getParentNode from './getParentNode';\nimport getUAString from '../utils/userAgent';\n\nfunction getTrueOffsetParent(element: Element): ?Element {\n  if (\n    !isHTMLElement(element) ||\n    // https://github.com/popperjs/popper-core/issues/837\n    getComputedStyle(element).position === 'fixed'\n  ) {\n    return null;\n  }\n\n  return element.offsetParent;\n}\n\n// `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element: Element) {\n  const isFirefox = /firefox/i.test(getUAString());\n  const isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    const elementCss = getComputedStyle(element);\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  let currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (\n    isHTMLElement(currentNode) &&\n    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0\n  ) {\n    const css = getComputedStyle(currentNode);\n\n    // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    if (\n      css.transform !== 'none' ||\n      css.perspective !== 'none' ||\n      css.contain === 'paint' ||\n      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||\n      (isFirefox && css.willChange === 'filter') ||\n      (isFirefox && css.filter && css.filter !== 'none')\n    ) {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nexport default function getOffsetParent(element: Element) {\n  const window = getWindow(element);\n\n  let offsetParent = getTrueOffsetParent(element);\n\n  while (\n    offsetParent &&\n    isTableElement(offsetParent) &&\n    getComputedStyle(offsetParent).position === 'static'\n  ) {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (\n    offsetParent &&\n    (getNodeName(offsetParent) === 'html' ||\n      (getNodeName(offsetParent) === 'body' &&\n        getComputedStyle(offsetParent).position === 'static'))\n  ) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n", "// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary = Element | Array<Element> | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n", "// @flow\nimport type { Modifier } from '../types';\nimport { modifierPhases } from '../enums';\n\n// source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n  const map = new Map();\n  const visited = new Set();\n  const result = [];\n\n  modifiers.forEach(modifier => {\n    map.set(modifier.name, modifier);\n  });\n\n  // On visiting object, check for its dependencies and visit them recursively\n  function sort(modifier: Modifier<any, any>) {\n    visited.add(modifier.name);\n\n    const requires = [\n      ...(modifier.requires || []),\n      ...(modifier.requiresIfExists || []),\n    ];\n\n    requires.forEach(dep => {\n      if (!visited.has(dep)) {\n        const depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n\n    result.push(modifier);\n  }\n\n  modifiers.forEach(modifier => {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n\n  return result;\n}\n\nexport default function orderModifiers(\n  modifiers: Array<Modifier<any, any>>\n): Array<Modifier<any, any>> {\n  // order based on dependencies\n  const orderedModifiers = order(modifiers);\n\n  // order based on phase\n  return modifierPhases.reduce((acc, phase) => {\n    return acc.concat(\n      orderedModifiers.filter(modifier => modifier.phase === phase)\n    );\n  }, []);\n}\n", "// @flow\nimport { isShadowRoot } from './instanceOf';\n\nexport default function contains(parent: Element, child: Element) {\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      }\n      // $FlowFixMe[prop-missing]: need a better way to handle this...\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n", "// @flow\nimport type { Rect, ClientRectObject } from '../types';\n\nexport default function rectToClientRect(rect: Rect): ClientRectObject {\n  return {\n    ...rect,\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height,\n  };\n}\n", "// @flow\nimport type { ClientRectObject, PositioningStrategy } from '../types';\nimport type { Boundary, RootBoundary } from '../enums';\nimport { viewport } from '../enums';\nimport getViewportRect from './getViewportRect';\nimport getDocumentRect from './getDocumentRect';\nimport listScrollParents from './listScrollParents';\nimport getOffsetParent from './getOffsetParent';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getParentNode from './getParentNode';\nimport contains from './contains';\nimport getNodeName from './getNodeName';\nimport rectToClientRect from '../utils/rectToClientRect';\nimport { max, min } from '../utils/math';\n\nfunction getInnerBoundingClientRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const rect = getBoundingClientRect(element, false, strategy === 'fixed');\n\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n\n  return rect;\n}\n\nfunction getClientRectFromMixedType(\n  element: Element,\n  clippingParent: Element | RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  return clippingParent === viewport\n    ? rectToClientRect(getViewportRect(element, strategy))\n    : isElement(clippingParent)\n    ? getInnerBoundingClientRect(clippingParent, strategy)\n    : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n}\n\n// A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element: Element): Array<Element> {\n  const clippingParents = listScrollParents(getParentNode(element));\n  const canEscapeClipping =\n    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  const clipperElement =\n    canEscapeClipping && isHTMLElement(element)\n      ? getOffsetParent(element)\n      : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  }\n\n  // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n  return clippingParents.filter(\n    (clippingParent) =>\n      isElement(clippingParent) &&\n      contains(clippingParent, clipperElement) &&\n      getNodeName(clippingParent) !== 'body'\n  );\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nexport default function getClippingRect(\n  element: Element,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  const mainClippingParents =\n    boundary === 'clippingParents'\n      ? getClippingParents(element)\n      : [].concat(boundary);\n  const clippingParents = [...mainClippingParents, rootBoundary];\n  const firstClippingParent = clippingParents[0];\n\n  const clippingRect = clippingParents.reduce((accRect, clippingParent) => {\n    const rect = getClientRectFromMixedType(element, clippingParent, strategy);\n\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n\n  return clippingRect;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport isLayoutViewport from './isLayoutViewport';\nimport type { PositioningStrategy } from '../types';\n\nexport default function getViewportRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n\n    const layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || (!layoutViewport && strategy === 'fixed')) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width,\n    height,\n    x: x + getWindowScrollBarX(element),\n    y,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getWindowScroll from './getWindowScroll';\nimport { max } from '../utils/math';\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nexport default function getDocumentRect(element: HTMLElement): Rect {\n  const html = getDocumentElement(element);\n  const winScroll = getWindowScroll(element);\n  const body = element.ownerDocument?.body;\n\n  const width = max(\n    html.scrollWidth,\n    html.clientWidth,\n    body ? body.scrollWidth : 0,\n    body ? body.clientWidth : 0\n  );\n  const height = max(\n    html.scrollHeight,\n    html.clientHeight,\n    body ? body.scrollHeight : 0,\n    body ? body.clientHeight : 0\n  );\n\n  let x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return { width, height, x, y };\n}\n", "// @flow\nimport { type BasePlacement, type Placement, auto } from '../enums';\n\nexport default function getBasePlacement(\n  placement: Placement | typeof auto\n): BasePlacement {\n  return (placement.split('-')[0]: any);\n}\n", "// @flow\nimport { type Variation, type Placement } from '../enums';\n\nexport default function getVariation(placement: Placement): ?Variation {\n  return (placement.split('-')[1]: any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nexport default function getMainAxisFromPlacement(\n  placement: Placement\n): 'x' | 'y' {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n", "// @flow\nimport getBasePlacement from './getBasePlacement';\nimport getVariation from './getVariation';\nimport getMainAxisFromPlacement from './getMainAxisFromPlacement';\nimport type {\n  Rect,\n  PositioningStrategy,\n  Offsets,\n  ClientRectObject,\n} from '../types';\nimport { top, right, bottom, left, start, end, type Placement } from '../enums';\n\nexport default function computeOffsets({\n  reference,\n  element,\n  placement,\n}: {\n  reference: Rect | ClientRectObject,\n  element: Rect | ClientRectObject,\n  strategy: PositioningStrategy,\n  placement?: Placement,\n}): Offsets {\n  const basePlacement = placement ? getBasePlacement(placement) : null;\n  const variation = placement ? getVariation(placement) : null;\n  const commonX = reference.x + reference.width / 2 - element.width / 2;\n  const commonY = reference.y + reference.height / 2 - element.height / 2;\n\n  let offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height,\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height,\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY,\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY,\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y,\n      };\n  }\n\n  const mainAxis = basePlacement\n    ? getMainAxisFromPlacement(basePlacement)\n    : null;\n\n  if (mainAxis != null) {\n    const len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] =\n          offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] =\n          offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n\n  return offsets;\n}\n", "// @flow\nimport type { SideObject } from '../types';\nimport getFreshSideObject from './getFreshSideObject';\n\nexport default function mergePaddingObject(\n  paddingObject: $Shape<SideObject>\n): SideObject {\n  return {\n    ...getFreshSideObject(),\n    ...paddingObject,\n  };\n}\n", "// @flow\nimport type { SideObject } from '../types';\n\nexport default function getFreshSideObject(): SideObject {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n  };\n}\n", "// @flow\n\nexport default function expandToHashMap<\n  T: number | string | boolean,\n  K: string\n>(value: T, keys: Array<K>): { [key: string]: T } {\n  return keys.reduce((hashMap, key) => {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n", "// @flow\nimport type { State, SideObject, Padding, PositioningStrategy } from '../types';\nimport type { Placement, Boundary, RootBoundary, Context } from '../enums';\nimport getClippingRect from '../dom-utils/getClippingRect';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getBoundingClientRect from '../dom-utils/getBoundingClientRect';\nimport computeOffsets from './computeOffsets';\nimport rectToClientRect from './rectToClientRect';\nimport {\n  clippingParents,\n  reference,\n  popper,\n  bottom,\n  top,\n  right,\n  basePlacements,\n  viewport,\n} from '../enums';\nimport { isElement } from '../dom-utils/instanceOf';\nimport mergePaddingObject from './mergePaddingObject';\nimport expandToHashMap from './expandToHashMap';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  placement: Placement,\n  strategy: PositioningStrategy,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  elementContext: Context,\n  altBoundary: boolean,\n  padding: Padding,\n};\n\nexport default function detectOverflow(\n  state: State,\n  options: $Shape<Options> = {}\n): SideObject {\n  const {\n    placement = state.placement,\n    strategy = state.strategy,\n    boundary = clippingParents,\n    rootBoundary = viewport,\n    elementContext = popper,\n    altBoundary = false,\n    padding = 0,\n  } = options;\n\n  const paddingObject = mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n\n  const altContext = elementContext === popper ? reference : popper;\n\n  const popperRect = state.rects.popper;\n  const element = state.elements[altBoundary ? altContext : elementContext];\n\n  const clippingClientRect = getClippingRect(\n    isElement(element)\n      ? element\n      : element.contextElement || getDocumentElement(state.elements.popper),\n    boundary,\n    rootBoundary,\n    strategy\n  );\n\n  const referenceClientRect = getBoundingClientRect(state.elements.reference);\n\n  const popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement,\n  });\n\n  const popperClientRect = rectToClientRect({\n    ...popperRect,\n    ...popperOffsets,\n  });\n\n  const elementClientRect =\n    elementContext === popper ? popperClientRect : referenceClientRect;\n\n  // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n  const overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom:\n      elementClientRect.bottom -\n      clippingClientRect.bottom +\n      paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right:\n      elementClientRect.right - clippingClientRect.right + paddingObject.right,\n  };\n\n  const offsetData = state.modifiersData.offset;\n\n  // Offsets can be applied only to the popper element\n  if (elementContext === popper && offsetData) {\n    const offset = offsetData[placement];\n\n    Object.keys(overflowOffsets).forEach((key) => {\n      const multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      const axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n", "// @flow\nimport type {\n  State,\n  OptionsGeneric,\n  Modifier,\n  Instance,\n  VirtualElement,\n} from './types';\nimport getCompositeRect from './dom-utils/getCompositeRect';\nimport getLayoutRect from './dom-utils/getLayoutRect';\nimport listScrollParents from './dom-utils/listScrollParents';\nimport getOffsetParent from './dom-utils/getOffsetParent';\nimport orderModifiers from './utils/orderModifiers';\nimport debounce from './utils/debounce';\nimport mergeByName from './utils/mergeByName';\nimport detectOverflow from './utils/detectOverflow';\nimport { isElement } from './dom-utils/instanceOf';\n\nconst DEFAULT_OPTIONS: OptionsGeneric<any> = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute',\n};\n\ntype PopperGeneratorArgs = {\n  defaultModifiers?: Array<Modifier<any, any>>,\n  defaultOptions?: $Shape<OptionsGeneric<any>>,\n};\n\nfunction areValidElements(...args: Array<any>): boolean {\n  return !args.some(\n    (element) =>\n      !(element && typeof element.getBoundingClientRect === 'function')\n  );\n}\n\nexport function popperGenerator(generatorOptions: PopperGeneratorArgs = {}) {\n  const { defaultModifiers = [], defaultOptions = DEFAULT_OPTIONS } =\n    generatorOptions;\n\n  return function createPopper<TModifier: $Shape<Modifier<any, any>>>(\n    reference: Element | VirtualElement,\n    popper: HTMLElement,\n    options: $Shape<OptionsGeneric<TModifier>> = defaultOptions\n  ): Instance {\n    let state: $Shape<State> = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: { ...DEFAULT_OPTIONS, ...defaultOptions },\n      modifiersData: {},\n      elements: {\n        reference,\n        popper,\n      },\n      attributes: {},\n      styles: {},\n    };\n\n    let effectCleanupFns: Array<() => void> = [];\n    let isDestroyed = false;\n\n    const instance = {\n      state,\n      setOptions(setOptionsAction) {\n        const options =\n          typeof setOptionsAction === 'function'\n            ? setOptionsAction(state.options)\n            : setOptionsAction;\n\n        cleanupModifierEffects();\n\n        state.options = {\n          // $FlowFixMe[exponential-spread]\n          ...defaultOptions,\n          ...state.options,\n          ...options,\n        };\n\n        state.scrollParents = {\n          reference: isElement(reference)\n            ? listScrollParents(reference)\n            : reference.contextElement\n            ? listScrollParents(reference.contextElement)\n            : [],\n          popper: listScrollParents(popper),\n        };\n\n        // Orders the modifiers based on their dependencies and `phase`\n        // properties\n        const orderedModifiers = orderModifiers(\n          mergeByName([...defaultModifiers, ...state.options.modifiers])\n        );\n\n        // Strip out disabled modifiers\n        state.orderedModifiers = orderedModifiers.filter((m) => m.enabled);\n\n        runModifierEffects();\n\n        return instance.update();\n      },\n\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        const { reference, popper } = state.elements;\n\n        // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n        if (!areValidElements(reference, popper)) {\n          return;\n        }\n\n        // Store the reference and popper rects to be read by modifiers\n        state.rects = {\n          reference: getCompositeRect(\n            reference,\n            getOffsetParent(popper),\n            state.options.strategy === 'fixed'\n          ),\n          popper: getLayoutRect(popper),\n        };\n\n        // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n        state.reset = false;\n\n        state.placement = state.options.placement;\n\n        // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n        state.orderedModifiers.forEach(\n          (modifier) =>\n            (state.modifiersData[modifier.name] = {\n              ...modifier.data,\n            })\n        );\n\n        for (let index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          const { fn, options = {}, name } = state.orderedModifiers[index];\n\n          if (typeof fn === 'function') {\n            state = fn({ state, options, name, instance }) || state;\n          }\n        }\n      },\n\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce<$Shape<State>>(\n        () =>\n          new Promise<$Shape<State>>((resolve) => {\n            instance.forceUpdate();\n            resolve(state);\n          })\n      ),\n\n      destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      },\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then((state) => {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    });\n\n    // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(({ name, options = {}, effect }) => {\n        if (typeof effect === 'function') {\n          const cleanupFn = effect({ state, name, instance, options });\n          const noopFn = () => {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach((fn) => fn());\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nexport const createPopper = popperGenerator();\n\n// eslint-disable-next-line import/no-unused-modules\nexport { detectOverflow };\n", "// @flow\n\nexport default function debounce<T>(fn: Function): () => Promise<T> {\n  let pending;\n  return () => {\n    if (!pending) {\n      pending = new Promise<T>(resolve => {\n        Promise.resolve().then(() => {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n", "// @flow\nimport type { Modifier } from '../types';\n\nexport default function mergeByName(\n  modifiers: Array<$Shape<Modifier<any, any>>>\n): Array<$Shape<Modifier<any, any>>> {\n  const merged = modifiers.reduce((merged, current) => {\n    const existing = merged[current.name];\n    merged[current.name] = existing\n      ? {\n          ...existing,\n          ...current,\n          options: { ...existing.options, ...current.options },\n          data: { ...existing.data, ...current.data },\n        }\n      : current;\n    return merged;\n  }, {});\n\n  // IE11 does not support Object.values\n  return Object.keys(merged).map(key => merged[key]);\n}\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport getWindow from '../dom-utils/getWindow';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  scroll: boolean,\n  resize: boolean,\n};\n\nconst passive = { passive: true };\n\nfunction effect({ state, instance, options }: ModifierArguments<Options>) {\n  const { scroll = true, resize = true } = options;\n\n  const window = getWindow(state.elements.popper);\n  const scrollParents = [\n    ...state.scrollParents.reference,\n    ...state.scrollParents.popper,\n  ];\n\n  if (scroll) {\n    scrollParents.forEach(scrollParent => {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return () => {\n    if (scroll) {\n      scrollParents.forEach(scrollParent => {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type EventListenersModifier = Modifier<'eventListeners', Options>;\nexport default ({\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: () => {},\n  effect,\n  data: {},\n}: EventListenersModifier);\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport computeOffsets from '../utils/computeOffsets';\n\nfunction popperOffsets({ state, name }: ModifierArguments<{||}>) {\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement,\n  });\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PopperOffsetsModifier = Modifier<'popperOffsets', {||}>;\nexport default ({\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {},\n}: PopperOffsetsModifier);\n", "// @flow\nimport type {\n  PositioningStrategy,\n  Offsets,\n  Modifier,\n  ModifierArguments,\n  Rect,\n  Window,\n} from '../types';\nimport {\n  type BasePlacement,\n  type Variation,\n  top,\n  left,\n  right,\n  bottom,\n  end,\n} from '../enums';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getWindow from '../dom-utils/getWindow';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getComputedStyle from '../dom-utils/getComputedStyle';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getVariation from '../utils/getVariation';\nimport { round } from '../utils/math';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type RoundOffsets = (\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>\n) => Offsets;\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets?: boolean | RoundOffsets,\n};\n\nconst unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto',\n};\n\n// Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\nfunction roundOffsetsByDPR({ x, y }, win: Window): Offsets {\n  const dpr = win.devicePixelRatio || 1;\n\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0,\n  };\n}\n\nexport function mapToStyles({\n  popper,\n  popperRect,\n  placement,\n  variation,\n  offsets,\n  position,\n  gpuAcceleration,\n  adaptive,\n  roundOffsets,\n  isFixed,\n}: {\n  popper: HTMLElement,\n  popperRect: Rect,\n  placement: BasePlacement,\n  variation: ?Variation,\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>,\n  position: PositioningStrategy,\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets: boolean | RoundOffsets,\n  isFixed: boolean,\n}) {\n  let { x = 0, y = 0 } = offsets;\n\n  ({ x, y } =\n    typeof roundOffsets === 'function' ? roundOffsets({ x, y }) : { x, y });\n\n  const hasX = offsets.hasOwnProperty('x');\n  const hasY = offsets.hasOwnProperty('y');\n\n  let sideX: string = left;\n  let sideY: string = top;\n\n  const win: Window = window;\n\n  if (adaptive) {\n    let offsetParent = getOffsetParent(popper);\n    let heightProp = 'clientHeight';\n    let widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (\n        getComputedStyle(offsetParent).position !== 'static' &&\n        position === 'absolute'\n      ) {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    }\n\n    // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n    offsetParent = (offsetParent: Element);\n\n    if (\n      placement === top ||\n      ((placement === left || placement === right) && variation === end)\n    ) {\n      sideY = bottom;\n      const offsetY =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.height\n          : // $FlowFixMe[prop-missing]\n            offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (\n      placement === left ||\n      ((placement === top || placement === bottom) && variation === end)\n    ) {\n      sideX = right;\n      const offsetX =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.width\n          : // $FlowFixMe[prop-missing]\n            offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  const commonStyles = {\n    position,\n    ...(adaptive && unsetSides),\n  };\n\n  ({ x, y } =\n    roundOffsets === true\n      ? roundOffsetsByDPR({ x, y }, getWindow(popper))\n      : { x, y });\n\n  if (gpuAcceleration) {\n    return {\n      ...commonStyles,\n      [sideY]: hasY ? '0' : '',\n      [sideX]: hasX ? '0' : '',\n      // Layer acceleration can disable subpixel rendering which causes slightly\n      // blurry text on low PPI displays, so we want to use 2D transforms\n      // instead\n      transform:\n        (win.devicePixelRatio || 1) <= 1\n          ? `translate(${x}px, ${y}px)`\n          : `translate3d(${x}px, ${y}px, 0)`,\n    };\n  }\n\n  return {\n    ...commonStyles,\n    [sideY]: hasY ? `${y}px` : '',\n    [sideX]: hasX ? `${x}px` : '',\n    transform: '',\n  };\n}\n\nfunction computeStyles({ state, options }: ModifierArguments<Options>) {\n  const {\n    gpuAcceleration = true,\n    adaptive = true,\n    // defaults to use builtin `roundOffsetsByDPR`\n    roundOffsets = true,\n  } = options;\n\n  const commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed',\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = {\n      ...state.styles.popper,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.popperOffsets,\n        position: state.options.strategy,\n        adaptive,\n        roundOffsets,\n      }),\n    };\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = {\n      ...state.styles.arrow,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.arrow,\n        position: 'absolute',\n        adaptive: false,\n        roundOffsets,\n      }),\n    };\n  }\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-placement': state.placement,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ComputeStylesModifier = Modifier<'computeStyles', Options>;\nexport default ({\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {},\n}: ComputeStylesModifier);\n", "// @flow\nimport type { Modifier, ModifierArguments } from '../types';\nimport getNodeName from '../dom-utils/getNodeName';\nimport { isHTMLElement } from '../dom-utils/instanceOf';\n\n// This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles({ state }: ModifierArguments<{||}>) {\n  Object.keys(state.elements).forEach((name) => {\n    const style = state.styles[name] || {};\n\n    const attributes = state.attributes[name] || {};\n    const element = state.elements[name];\n\n    // arrow is optional + virtual elements\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    }\n\n    // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n    Object.assign(element.style, style);\n\n    Object.keys(attributes).forEach((name) => {\n      const value = attributes[name];\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect({ state }: ModifierArguments<{||}>) {\n  const initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0',\n    },\n    arrow: {\n      position: 'absolute',\n    },\n    reference: {},\n  };\n\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return () => {\n    Object.keys(state.elements).forEach((name) => {\n      const element = state.elements[name];\n      const attributes = state.attributes[name] || {};\n\n      const styleProperties = Object.keys(\n        state.styles.hasOwnProperty(name)\n          ? state.styles[name]\n          : initialStyles[name]\n      );\n\n      // Set all values to an empty string to unset them\n      const style = styleProperties.reduce((style, property) => {\n        style[property] = '';\n        return style;\n      }, {});\n\n      // arrow is optional + virtual elements\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n\n      Object.keys(attributes).forEach((attribute) => {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ApplyStylesModifier = Modifier<'applyStyles', {||}>;\nexport default ({\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect,\n  requires: ['computeStyles'],\n}: ApplyStylesModifier);\n", "// @flow\nimport type { Placement } from '../enums';\nimport type { ModifierArguments, Modifier, Rect, Offsets } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport { top, left, right, placements } from '../enums';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type OffsetsFunction = ({\n  popper: Rect,\n  reference: Rect,\n  placement: Placement,\n}) => [?number, ?number];\n\ntype Offset = OffsetsFunction | [?number, ?number];\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  offset: Offset,\n};\n\nexport function distanceAndSkiddingToXY(\n  placement: Placement,\n  rects: { popper: Rect, reference: Rect },\n  offset: Offset\n): Offsets {\n  const basePlacement = getBasePlacement(placement);\n  const invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  let [skidding, distance] =\n    typeof offset === 'function'\n      ? offset({\n          ...rects,\n          placement,\n        })\n      : offset;\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n\n  return [left, right].indexOf(basePlacement) >= 0\n    ? { x: distance, y: skidding }\n    : { x: skidding, y: distance };\n}\n\nfunction offset({ state, options, name }: ModifierArguments<Options>) {\n  const { offset = [0, 0] } = options;\n\n  const data = placements.reduce((acc, placement) => {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n\n  const { x, y } = data[state.placement];\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type OffsetModifier = Modifier<'offset', Options>;\nexport default ({\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset,\n}: OffsetModifier);\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n\nexport default function getOppositePlacement(placement: Placement): Placement {\n  return (placement.replace(\n    /left|right|bottom|top/g,\n    matched => hash[matched]\n  ): any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { start: 'end', end: 'start' };\n\nexport default function getOppositeVariationPlacement(\n  placement: Placement\n): Placement {\n  return (placement.replace(/start|end/g, matched => hash[matched]): any);\n}\n", "// @flow\nimport type { State, Padding } from '../types';\nimport type {\n  Placement,\n  ComputedPlacement,\n  Boundary,\n  RootBoundary,\n} from '../enums';\nimport getVariation from './getVariation';\nimport {\n  variationPlacements,\n  basePlacements,\n  placements as allPlacements,\n} from '../enums';\nimport detectOverflow from './detectOverflow';\nimport getBasePlacement from './getBasePlacement';\n\ntype Options = {\n  placement: Placement,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  flipVariations: boolean,\n  allowedAutoPlacements?: Array<Placement>,\n};\n\ntype OverflowsMap = { [ComputedPlacement]: number };\n\nexport default function computeAutoPlacement(\n  state: $Shape<State>,\n  options: Options = {}\n): Array<ComputedPlacement> {\n  const {\n    placement,\n    boundary,\n    rootBoundary,\n    padding,\n    flipVariations,\n    allowedAutoPlacements = allPlacements,\n  } = options;\n\n  const variation = getVariation(placement);\n\n  const placements = variation\n    ? flipVariations\n      ? variationPlacements\n      : variationPlacements.filter(\n          (placement) => getVariation(placement) === variation\n        )\n    : basePlacements;\n\n  let allowedPlacements = placements.filter(\n    (placement) => allowedAutoPlacements.indexOf(placement) >= 0\n  );\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  }\n\n  // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n  const overflows: OverflowsMap = allowedPlacements.reduce((acc, placement) => {\n    acc[placement] = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      padding,\n    })[getBasePlacement(placement)];\n\n    return acc;\n  }, {});\n\n  return Object.keys(overflows).sort((a, b) => overflows[a] - overflows[b]);\n}\n", "// @flow\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { ModifierArguments, Modifier, Padding } from '../types';\nimport getOppositePlacement from '../utils/getOppositePlacement';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getOppositeVariationPlacement from '../utils/getOppositeVariationPlacement';\nimport detectOverflow from '../utils/detectOverflow';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\nimport { bottom, top, start, right, left, auto } from '../enums';\nimport getVariation from '../utils/getVariation';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  mainAxis: boolean,\n  altAxis: boolean,\n  fallbackPlacements: Array<Placement>,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  altBoundary: boolean,\n  flipVariations: boolean,\n  allowedAutoPlacements: Array<Placement>,\n};\n\nfunction getExpandedFallbackPlacements(placement: Placement): Array<Placement> {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  const oppositePlacement = getOppositePlacement(placement);\n\n  return [\n    getOppositeVariationPlacement(placement),\n    oppositePlacement,\n    getOppositeVariationPlacement(oppositePlacement),\n  ];\n}\n\nfunction flip({ state, options, name }: ModifierArguments<Options>) {\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = true,\n    fallbackPlacements: specifiedFallbackPlacements,\n    padding,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    flipVariations = true,\n    allowedAutoPlacements,\n  } = options;\n\n  const preferredPlacement = state.options.placement;\n  const basePlacement = getBasePlacement(preferredPlacement);\n  const isBasePlacement = basePlacement === preferredPlacement;\n\n  const fallbackPlacements =\n    specifiedFallbackPlacements ||\n    (isBasePlacement || !flipVariations\n      ? [getOppositePlacement(preferredPlacement)]\n      : getExpandedFallbackPlacements(preferredPlacement));\n\n  const placements = [preferredPlacement, ...fallbackPlacements].reduce(\n    (acc, placement) => {\n      return acc.concat(\n        getBasePlacement(placement) === auto\n          ? computeAutoPlacement(state, {\n              placement,\n              boundary,\n              rootBoundary,\n              padding,\n              flipVariations,\n              allowedAutoPlacements,\n            })\n          : placement\n      );\n    },\n    []\n  );\n\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n\n  const checksMap = new Map();\n  let makeFallbackChecks = true;\n  let firstFittingPlacement = placements[0];\n\n  for (let i = 0; i < placements.length; i++) {\n    const placement = placements[i];\n    const basePlacement = getBasePlacement(placement);\n    const isStartVariation = getVariation(placement) === start;\n    const isVertical = [top, bottom].indexOf(basePlacement) >= 0;\n    const len = isVertical ? 'width' : 'height';\n\n    const overflow = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      altBoundary,\n      padding,\n    });\n\n    let mainVariationSide: any = isVertical\n      ? isStartVariation\n        ? right\n        : left\n      : isStartVariation\n      ? bottom\n      : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    const altVariationSide: any = getOppositePlacement(mainVariationSide);\n\n    const checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(\n        overflow[mainVariationSide] <= 0,\n        overflow[altVariationSide] <= 0\n      );\n    }\n\n    if (checks.every((check) => check)) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    const numberOfChecks = flipVariations ? 3 : 1;\n\n    for (let i = numberOfChecks; i > 0; i--) {\n      const fittingPlacement = placements.find((placement) => {\n        const checks = checksMap.get(placement);\n        if (checks) {\n          return checks.slice(0, i).every((check) => check);\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        break;\n      }\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type FlipModifier = Modifier<'flip', Options>;\nexport default ({\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: { _skip: false },\n}: FlipModifier);\n", "// @flow\nimport { max as mathMax, min as mathMin } from './math';\n\nexport function within(min: number, value: number, max: number): number {\n  return mathMax(min, mathMin(value, max));\n}\n\nexport function withinMaxClamp(min: number, value: number, max: number) {\n  const v = within(min, value, max);\n  return v > max ? max : v;\n}\n", "// @flow\nimport { top, left, right, bottom, start } from '../enums';\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { Rect, ModifierArguments, Modifier, Padding } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport getAltAxis from '../utils/getAltAxis';\nimport { within, withinMaxClamp } from '../utils/within';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport detectOverflow from '../utils/detectOverflow';\nimport getVariation from '../utils/getVariation';\nimport getFreshSideObject from '../utils/getFreshSideObject';\nimport { min as mathMin, max as mathMax } from '../utils/math';\n\ntype TetherOffset =\n  | (({\n      popper: Rect,\n      reference: Rect,\n      placement: Placement,\n    }) => number | { mainAxis: number, altAxis: number })\n  | number\n  | { mainAxis: number, altAxis: number };\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  /* Prevents boundaries overflow on the main axis */\n  mainAxis: boolean,\n  /* Prevents boundaries overflow on the alternate axis */\n  altAxis: boolean,\n  /* The area to check the popper is overflowing in */\n  boundary: Boundary,\n  /* If the popper is not overflowing the main area, fallback to this one */\n  rootBoundary: RootBoundary,\n  /* Use the reference's \"clippingParents\" boundary context */\n  altBoundary: boolean,\n  /**\n   * Allows the popper to overflow from its boundaries to keep it near its\n   * reference element\n   */\n  tether: boolean,\n  /* Offsets when the `tether` option should activate */\n  tetherOffset: TetherOffset,\n  /* Sets a padding to the provided boundary */\n  padding: Padding,\n};\n\nfunction preventOverflow({ state, options, name }: ModifierArguments<Options>) {\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = false,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    padding,\n    tether = true,\n    tetherOffset = 0,\n  } = options;\n\n  const overflow = detectOverflow(state, {\n    boundary,\n    rootBoundary,\n    padding,\n    altBoundary,\n  });\n  const basePlacement = getBasePlacement(state.placement);\n  const variation = getVariation(state.placement);\n  const isBasePlacement = !variation;\n  const mainAxis = getMainAxisFromPlacement(basePlacement);\n  const altAxis = getAltAxis(mainAxis);\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const tetherOffsetValue =\n    typeof tetherOffset === 'function'\n      ? tetherOffset({\n          ...state.rects,\n          placement: state.placement,\n        })\n      : tetherOffset;\n  const normalizedTetherOffsetValue =\n    typeof tetherOffsetValue === 'number'\n      ? { mainAxis: tetherOffsetValue, altAxis: tetherOffsetValue }\n      : { mainAxis: 0, altAxis: 0, ...tetherOffsetValue };\n  const offsetModifierState = state.modifiersData.offset\n    ? state.modifiersData.offset[state.placement]\n    : null;\n\n  const data = { x: 0, y: 0 };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    const mainSide = mainAxis === 'y' ? top : left;\n    const altSide = mainAxis === 'y' ? bottom : right;\n    const len = mainAxis === 'y' ? 'height' : 'width';\n    const offset = popperOffsets[mainAxis];\n\n    const min = offset + overflow[mainSide];\n    const max = offset - overflow[altSide];\n\n    const additive = tether ? -popperRect[len] / 2 : 0;\n\n    const minLen = variation === start ? referenceRect[len] : popperRect[len];\n    const maxLen = variation === start ? -popperRect[len] : -referenceRect[len];\n\n    // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n    const arrowElement = state.elements.arrow;\n    const arrowRect =\n      tether && arrowElement\n        ? getLayoutRect(arrowElement)\n        : { width: 0, height: 0 };\n    const arrowPaddingObject = state.modifiersData['arrow#persistent']\n      ? state.modifiersData['arrow#persistent'].padding\n      : getFreshSideObject();\n    const arrowPaddingMin = arrowPaddingObject[mainSide];\n    const arrowPaddingMax = arrowPaddingObject[altSide];\n\n    // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n    const arrowLen = within(0, referenceRect[len], arrowRect[len]);\n\n    const minOffset = isBasePlacement\n      ? referenceRect[len] / 2 -\n        additive -\n        arrowLen -\n        arrowPaddingMin -\n        normalizedTetherOffsetValue.mainAxis\n      : minLen -\n        arrowLen -\n        arrowPaddingMin -\n        normalizedTetherOffsetValue.mainAxis;\n    const maxOffset = isBasePlacement\n      ? -referenceRect[len] / 2 +\n        additive +\n        arrowLen +\n        arrowPaddingMax +\n        normalizedTetherOffsetValue.mainAxis\n      : maxLen +\n        arrowLen +\n        arrowPaddingMax +\n        normalizedTetherOffsetValue.mainAxis;\n\n    const arrowOffsetParent =\n      state.elements.arrow && getOffsetParent(state.elements.arrow);\n    const clientOffset = arrowOffsetParent\n      ? mainAxis === 'y'\n        ? arrowOffsetParent.clientTop || 0\n        : arrowOffsetParent.clientLeft || 0\n      : 0;\n\n    const offsetModifierValue = offsetModifierState?.[mainAxis] ?? 0;\n    const tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    const tetherMax = offset + maxOffset - offsetModifierValue;\n\n    const preventedOffset = within(\n      tether ? mathMin(min, tetherMin) : min,\n      offset,\n      tether ? mathMax(max, tetherMax) : max\n    );\n\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    const mainSide = mainAxis === 'x' ? top : left;\n    const altSide = mainAxis === 'x' ? bottom : right;\n    const offset = popperOffsets[altAxis];\n\n    const len = altAxis === 'y' ? 'height' : 'width';\n\n    const min = offset + overflow[mainSide];\n    const max = offset - overflow[altSide];\n\n    const isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    const offsetModifierValue = offsetModifierState?.[altAxis] ?? 0;\n    const tetherMin = isOriginSide\n      ? min\n      : offset -\n        referenceRect[len] -\n        popperRect[len] -\n        offsetModifierValue +\n        normalizedTetherOffsetValue.altAxis;\n    const tetherMax = isOriginSide\n      ? offset +\n        referenceRect[len] +\n        popperRect[len] -\n        offsetModifierValue -\n        normalizedTetherOffsetValue.altAxis\n      : max;\n\n    const preventedOffset =\n      tether && isOriginSide\n        ? withinMaxClamp(tetherMin, offset, tetherMax)\n        : within(tether ? tetherMin : min, offset, tether ? tetherMax : max);\n\n    popperOffsets[altAxis] = preventedOffset;\n    data[altAxis] = preventedOffset - offset;\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PreventOverflowModifier = Modifier<'preventOverflow', Options>;\nexport default ({\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset'],\n}: PreventOverflowModifier);\n", "// @flow\n\nexport default function getAltAxis(axis: 'x' | 'y'): 'x' | 'y' {\n  return axis === 'x' ? 'y' : 'x';\n}\n", "// @flow\nimport type { Modifier, ModifierArguments, Padding, Rect } from '../types';\nimport type { Placement } from '../enums';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport contains from '../dom-utils/contains';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport { within } from '../utils/within';\nimport mergePaddingObject from '../utils/mergePaddingObject';\nimport expandToHashMap from '../utils/expandToHashMap';\nimport { left, right, basePlacements, top, bottom } from '../enums';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  element: HTMLElement | string | null,\n  padding:\n    | Padding\n    | (({|\n        popper: Rect,\n        reference: Rect,\n        placement: Placement,\n      |}) => Padding),\n};\n\nconst toPaddingObject = (padding, state) => {\n  padding =\n    typeof padding === 'function'\n      ? padding({ ...state.rects, placement: state.placement })\n      : padding;\n\n  return mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n};\n\nfunction arrow({ state, name, options }: ModifierArguments<Options>) {\n  const arrowElement = state.elements.arrow;\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const basePlacement = getBasePlacement(state.placement);\n  const axis = getMainAxisFromPlacement(basePlacement);\n  const isVertical = [left, right].indexOf(basePlacement) >= 0;\n  const len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  const paddingObject = toPaddingObject(options.padding, state);\n  const arrowRect = getLayoutRect(arrowElement);\n  const minProp = axis === 'y' ? top : left;\n  const maxProp = axis === 'y' ? bottom : right;\n\n  const endDiff =\n    state.rects.reference[len] +\n    state.rects.reference[axis] -\n    popperOffsets[axis] -\n    state.rects.popper[len];\n  const startDiff = popperOffsets[axis] - state.rects.reference[axis];\n\n  const arrowOffsetParent = getOffsetParent(arrowElement);\n  const clientSize = arrowOffsetParent\n    ? axis === 'y'\n      ? arrowOffsetParent.clientHeight || 0\n      : arrowOffsetParent.clientWidth || 0\n    : 0;\n\n  const centerToReference = endDiff / 2 - startDiff / 2;\n\n  // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n  const min = paddingObject[minProp];\n  const max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  const center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  const offset = within(min, center, max);\n\n  // Prevents breaking syntax highlighting...\n  const axisProp: string = axis;\n  state.modifiersData[name] = {\n    [axisProp]: offset,\n    centerOffset: offset - center,\n  };\n}\n\nfunction effect({ state, options }: ModifierArguments<Options>) {\n  let { element: arrowElement = '[data-popper-arrow]' } = options;\n\n  if (arrowElement == null) {\n    return;\n  }\n\n  // CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ArrowModifier = Modifier<'arrow', Options>;\nexport default ({\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow'],\n}: ArrowModifier);\n", "// @flow\nimport type {\n  ModifierArguments,\n  Modifier,\n  Rect,\n  SideObject,\n  Offsets,\n} from '../types';\nimport { top, bottom, left, right } from '../enums';\nimport detectOverflow from '../utils/detectOverflow';\n\nfunction getSideOffsets(\n  overflow: SideObject,\n  rect: Rect,\n  preventedOffsets: Offsets = { x: 0, y: 0 }\n): SideObject {\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x,\n  };\n}\n\nfunction isAnySideFullyClipped(overflow: SideObject): boolean {\n  return [top, right, bottom, left].some((side) => overflow[side] >= 0);\n}\n\nfunction hide({ state, name }: ModifierArguments<{||}>) {\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const preventedOffsets = state.modifiersData.preventOverflow;\n\n  const referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference',\n  });\n  const popperAltOverflow = detectOverflow(state, {\n    altBoundary: true,\n  });\n\n  const referenceClippingOffsets = getSideOffsets(\n    referenceOverflow,\n    referenceRect\n  );\n  const popperEscapeOffsets = getSideOffsets(\n    popperAltOverflow,\n    popperRect,\n    preventedOffsets\n  );\n\n  const isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  const hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n\n  state.modifiersData[name] = {\n    referenceClippingOffsets,\n    popperEscapeOffsets,\n    isReferenceHidden,\n    hasPopperEscaped,\n  };\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type HideModifier = Modifier<'hide', {||}>;\nexport default ({\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide,\n}: HideModifier);\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\nimport offset from './modifiers/offset';\nimport flip from './modifiers/flip';\nimport preventOverflow from './modifiers/preventOverflow';\nimport arrow from './modifiers/arrow';\nimport hide from './modifiers/hide';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n  offset,\n  flip,\n  preventOverflow,\n  arrow,\n  hide,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper as createPopperLite } from './popper-lite';\n// eslint-disable-next-line import/no-unused-modules\nexport * from './modifiers';\n"], "names": ["getWindow", "node", "window", "toString", "ownerDocument", "defaultView", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "map", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom", "getWindowScroll", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getNodeName", "nodeName", "toLowerCase", "getDocumentElement", "document", "documentElement", "getWindowScrollBarX", "getComputedStyle", "isScrollParent", "overflow", "overflowX", "overflowY", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "rect", "isElementScaled", "scroll", "offsets", "clientLeft", "clientTop", "getLayoutRect", "abs", "getParentNode", "assignedSlot", "parentNode", "host", "getScrollParent", "indexOf", "body", "listScrollParents", "list", "scrollParent", "isBody", "_element$ownerDocumen", "target", "concat", "updatedList", "isTableElement", "getTrueOffsetParent", "position", "getOffsetParent", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getContainingBlock", "auto", "basePlacements", "start", "end", "viewport", "popper", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "order", "modifiers", "Map", "visited", "Set", "result", "sort", "modifier", "add", "name", "requires", "requiresIfExists", "for<PERSON>ach", "dep", "has", "depModifier", "get", "push", "set", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "strategy", "html", "clientWidth", "clientHeight", "layoutViewport", "getViewportRect", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "direction", "getDocumentRect", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "clippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getBasePlacement", "split", "getVariation", "getMainAxisFromPlacement", "computeOffsets", "reference", "basePlacement", "variation", "commonX", "commonY", "mainAxis", "len", "mergePaddingObject", "paddingObject", "expandToHashMap", "value", "keys", "hashMap", "key", "detectOverflow", "state", "options", "elementContext", "altBoundary", "padding", "altContext", "popperRect", "rects", "elements", "clippingClientRect", "contextElement", "referenceClientRect", "popperOffsets", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "modifiersData", "offset", "Object", "multiply", "axis", "DEFAULT_OPTIONS", "areValidElements", "args", "some", "popperGenerator", "generatorOptions", "defaultModifiers", "defaultOptions", "fn", "pending", "orderedModifiers", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "merged", "phase", "orderModifiers", "current", "existing", "data", "m", "enabled", "effect", "cleanupFn", "noopFn", "update", "forceUpdate", "reset", "index", "length", "Promise", "resolve", "then", "undefined", "destroy", "onFirstUpdate", "passive", "resize", "addEventListener", "removeEventListener", "unsetSides", "mapToStyles", "gpuAcceleration", "adaptive", "roundOffsets", "hasX", "hasOwnProperty", "hasY", "sideX", "sideY", "heightProp", "widthProp", "commonStyles", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "arrow", "style", "assign", "removeAttribute", "setAttribute", "initialStyles", "margin", "property", "attribute", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "hash", "getOppositePlacement", "replace", "matched", "getOppositeVariationPlacement", "computeAutoPlacement", "flipVariations", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "a", "b", "_skip", "checkMainAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "fittingPlacement", "find", "slice", "within", "mathMax", "mathMin", "tether", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowElement", "arrowRect", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "arrowOffsetParent", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "isOriginSide", "tetherMin", "v", "withinMaxClamp", "toPaddingObject", "minProp", "maxProp", "endDiff", "startDiff", "clientSize", "centerToReference", "center", "axisProp", "centerOffset", "querySelector", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "side", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "createPopper", "eventListeners", "computeStyles", "applyStyles", "flip", "hide"], "mappings": ";;;;8OAIe,SAASA,EAAUC,MACpB,MAARA,SACKC,UAGe,oBAApBD,EAAKE,WAAkC,KACnCC,EAAgBH,EAAKG,qBACpBA,GAAgBA,EAAcC,aAAwBH,cAGxDD,ECTT,SAASK,EAAUL,UAEVA,aADYD,EAAUC,GAAMM,SACEN,aAAgBM,QAKvD,SAASC,EAAcP,UAEdA,aADYD,EAAUC,GAAMQ,aACER,aAAgBQ,YAKvD,SAASC,EAAaT,SAEM,oBAAfU,aAIJV,aADYD,EAAUC,GAAMU,YACEV,aAAgBU,YCxBhD,IAAMC,EAAMC,KAAKD,IACXE,EAAMD,KAAKC,IACXC,EAAQF,KAAKE,MCMX,SAASC,QAChBC,EAAUC,UAAsBC,2BAElCF,GAAAA,EAAQG,QAAUC,MAAMC,QAAQL,EAAOG,QAClCH,EAAOG,OACXG,KAAI,SAACC,UAAYA,EAAKC,UAASD,EAAKE,WACpCC,KAAK,KAGHT,UAAUU,UCfJ,SAASC,WACd,iCAAiCC,KAAKd,KCGjC,SAASe,EACtBC,EACAC,EACAC,YADAD,IAAAA,GAAwB,YACxBC,IAAAA,GAA2B,OAErBC,EAAaH,EAAQD,wBACvBK,EAAS,EACTC,EAAS,EAETJ,GAAgBzB,EAAcwB,KAChCI,EACGJ,EAAsBM,YAAc,GACjCvB,EAAMoB,EAAWI,OAAUP,EAAsBM,aACjD,EACND,EACGL,EAAsBQ,aAAe,GAClCzB,EAAMoB,EAAWM,QAAWT,EAAsBQ,cAClD,OAGAE,GAAmBpC,EAAU0B,GAAWhC,EAAUgC,GAAW9B,QAA7DwC,eACFC,GAAoBd,KAAsBK,EAE1CU,GACHT,EAAWU,MACTF,GAAoBD,EAAiBA,EAAeI,WAAa,IACpEV,EACIW,GACHZ,EAAWa,KACTL,GAAoBD,EAAiBA,EAAeO,UAAY,IACnEZ,EACIE,EAAQJ,EAAWI,MAAQH,EAC3BK,EAASN,EAAWM,OAASJ,QAE5B,CACLE,MAAAA,EACAE,OAAAA,EACAO,IAAKD,EACLG,MAAON,EAAIL,EACXY,OAAQJ,EAAIN,EACZI,KAAMD,EACNA,EAAAA,EACAG,EAAAA,GC7CW,SAASK,EAAgBnD,OAChCoD,EAAMrD,EAAUC,SAIf,CACLqD,WAJiBD,EAAIE,YAKrBC,UAJgBH,EAAII,aCJT,SAASC,EAAY1B,UAC3BA,GAAWA,EAAQ2B,UAAY,IAAIC,cAAgB,KCA7C,SAASC,EACtB7B,WAIG1B,EAAU0B,GACPA,EAAQ5B,cAER4B,EAAQ8B,WAAa5D,OAAO4D,UAChCC,gBCRW,SAASC,EAAoBhC,UASxCD,EAAsB8B,EAAmB7B,IAAUa,KACnDO,EAAgBpB,GAASsB,WCZd,SAASW,EACtBjC,UAEOhC,EAAUgC,GAASiC,iBAAiBjC,GCH9B,SAASkC,EAAelC,SAEMiC,EAAiBjC,GAApDmC,IAAAA,SAAUC,IAAAA,UAAWC,IAAAA,gBACtB,6BAA6BvC,KAAKqC,EAAWE,EAAYD,GCenD,SAASE,EACtBC,EACAC,EACAC,YAAAA,IAAAA,GAAmB,OCjBiBxE,ECLO+B,EFwBrC0C,EAA0BlE,EAAcgE,GACxCG,EACJnE,EAAcgE,IAjBlB,SAAyBxC,OACjB4C,EAAO5C,EAAQD,wBACfK,EAASrB,EAAM6D,EAAKrC,OAASP,EAAQM,aAAe,EACpDD,EAAStB,EAAM6D,EAAKnC,QAAUT,EAAQQ,cAAgB,SAE1C,IAAXJ,GAA2B,IAAXC,EAYUwC,CAAgBL,GAC3CT,EAAkBF,EAAmBW,GACrCI,EAAO7C,EACXwC,EACAI,EACAF,GAGEK,EAAS,CAAExB,WAAY,EAAGE,UAAW,GACrCuB,EAAU,CAAEnC,EAAG,EAAGG,EAAG,UAErB2B,IAA6BA,IAA4BD,MAE3B,SAA9Bf,EAAYc,IAEZN,EAAeH,MAEfe,GCtCgC7E,EDsCTuE,KCrCdxE,EAAUC,IAAUO,EAAcP,GCLxC,CACLqD,YAFyCtB,EDSb/B,GCPRqD,WACpBE,UAAWxB,EAAQwB,WDIZJ,EAAgBnD,IDuCnBO,EAAcgE,KAChBO,EAAUhD,EAAsByC,GAAc,IACtC5B,GAAK4B,EAAaQ,WAC1BD,EAAQhC,GAAKyB,EAAaS,WACjBlB,IACTgB,EAAQnC,EAAIoB,EAAoBD,KAI7B,CACLnB,EAAGgC,EAAK/B,KAAOiC,EAAOxB,WAAayB,EAAQnC,EAC3CG,EAAG6B,EAAK5B,IAAM8B,EAAOtB,UAAYuB,EAAQhC,EACzCR,MAAOqC,EAAKrC,MACZE,OAAQmC,EAAKnC,QGvDF,SAASyC,EAAclD,OAC9BG,EAAaJ,EAAsBC,GAIrCO,EAAQP,EAAQM,YAChBG,EAAST,EAAQQ,oBAEjB3B,KAAKsE,IAAIhD,EAAWI,MAAQA,IAAU,IACxCA,EAAQJ,EAAWI,OAGjB1B,KAAKsE,IAAIhD,EAAWM,OAASA,IAAW,IAC1CA,EAASN,EAAWM,QAGf,CACLG,EAAGZ,EAAQc,WACXC,EAAGf,EAAQiB,UACXV,MAAAA,EACAE,OAAAA,GCrBW,SAAS2C,EAAcpD,SACP,SAAzB0B,EAAY1B,GACPA,EAOPA,EAAQqD,cACRrD,EAAQsD,aACP5E,EAAasB,GAAWA,EAAQuD,KAAO,OAExC1B,EAAmB7B,GCZR,SAASwD,EAAgBvF,SAClC,CAAC,OAAQ,OAAQ,aAAawF,QAAQ/B,EAAYzD,KAAU,EAEvDA,EAAKG,cAAcsF,KAGxBlF,EAAcP,IAASiE,EAAejE,GACjCA,EAGFuF,EAAgBJ,EAAcnF,ICHxB,SAAS0F,EACtB3D,EACA4D,kBAAAA,IAAAA,EAAgC,QAE1BC,EAAeL,EAAgBxD,GAC/B8D,EAASD,cAAiB7D,EAAQ5B,sBAAR2F,EAAuBL,MACjDrC,EAAMrD,EAAU6F,GAChBG,EAASF,EACX,CAACzC,GAAK4C,OACJ5C,EAAIX,gBAAkB,GACtBwB,EAAe2B,GAAgBA,EAAe,IAEhDA,EACEK,EAAcN,EAAKK,OAAOD,UAEzBF,EACHI,EAEAA,EAAYD,OAAON,EAAkBP,EAAcY,KC5B1C,SAASG,EAAenE,SAC9B,CAAC,QAAS,KAAM,MAAMyD,QAAQ/B,EAAY1B,KAAa,ECKhE,SAASoE,EAAoBpE,UAExBxB,EAAcwB,IAEwB,UAAvCiC,EAAiBjC,GAASqE,SAKrBrE,EAAQwC,aAHN,KAsDI,SAAS8B,EAAgBtE,WAChC9B,EAASF,EAAUgC,GAErBwC,EAAe4B,EAAoBpE,GAGrCwC,GACA2B,EAAe3B,IAC6B,WAA5CP,EAAiBO,GAAc6B,UAE/B7B,EAAe4B,EAAoB5B,UAInCA,IAC+B,SAA9Bd,EAAYc,IACoB,SAA9Bd,EAAYc,IACiC,WAA5CP,EAAiBO,GAAc6B,UAE5BnG,EAGFsE,GApET,SAA4BxC,OACpBuE,EAAY,WAAWzE,KAAKd,QACrB,WAAWc,KAAKd,MAEjBR,EAAcwB,IAGI,UADTiC,EAAiBjC,GACrBqE,gBACN,SAIPG,EAAcpB,EAAcpD,OAE5BtB,EAAa8F,KACfA,EAAcA,EAAYjB,MAI1B/E,EAAcgG,IACd,CAAC,OAAQ,QAAQf,QAAQ/B,EAAY8C,IAAgB,GACrD,KACMC,EAAMxC,EAAiBuC,MAMT,SAAlBC,EAAIC,WACgB,SAApBD,EAAIE,aACY,UAAhBF,EAAIG,UACsD,IAA1D,CAAC,YAAa,eAAenB,QAAQgB,EAAII,aACxCN,GAAgC,WAAnBE,EAAII,YACjBN,GAAaE,EAAIK,QAAyB,SAAfL,EAAIK,cAEzBN,EAEPA,EAAcA,EAAYlB,kBAIvB,KA2BgByB,CAAmB/E,IAAY9B,EC1FjD,IAAM8C,EAAa,MACbG,EAAmB,SACnBD,EAAiB,QACjBL,EAAe,OACfmE,EAAe,OAMfC,EAAuC,CAACjE,EAAKG,EAAQD,EAAOL,GAE5DqE,EAAiB,QACjBC,EAAa,MAIbC,EAAuB,WAIvBC,EAAmB,SAiBnBC,EAAiDL,EAAeM,QAC3E,SAACC,EAAgCC,UAC/BD,EAAIvB,OAAO,CAAKwB,MAAaP,EAAmBO,MAAaN,MAC/D,IAEWO,EAA+B,UAAIT,GAAgBD,IAAMO,QACpE,SACEC,EACAC,UAEAD,EAAIvB,OAAO,CACTwB,EACIA,MAAaP,EACbO,MAAaN,MAErB,IAeWQ,EAAwC,CAXb,aACZ,OACU,YAEE,aACZ,OACU,YAEI,cACZ,QACU,cC/DxC,SAASC,EAAMC,OACPtG,EAAM,IAAIuG,IACVC,EAAU,IAAIC,IACdC,EAAS,YAONC,EAAKC,GACZJ,EAAQK,IAAID,EAASE,gBAGfF,EAASG,UAAY,GACrBH,EAASI,kBAAoB,IAG1BC,SAAQ,SAAAC,OACVV,EAAQW,IAAID,GAAM,KACfE,EAAcpH,EAAIqH,IAAIH,GAExBE,GACFT,EAAKS,OAKXV,EAAOY,KAAKV,UAvBdN,EAAUW,SAAQ,SAAAL,GAChB5G,EAAIuH,IAAIX,EAASE,KAAMF,MAyBzBN,EAAUW,SAAQ,SAAAL,GACXJ,EAAQW,IAAIP,EAASE,OAExBH,EAAKC,MAIFF,ECxCM,SAASc,EAASC,EAAiBC,OAC1CC,EAAWD,EAAME,aAAeF,EAAME,iBAGxCH,EAAOD,SAASE,UACX,EAGJ,GAAIC,GAAYxI,EAAawI,GAAW,KACvCE,EAAOH,IACR,IACGG,GAAQJ,EAAOK,WAAWD,UACrB,EAGTA,EAAOA,EAAK9D,YAAc8D,EAAK7D,WACxB6D,UAIJ,ECpBM,SAASE,EAAiB1E,2BAElCA,GACH/B,KAAM+B,EAAKhC,EACXI,IAAK4B,EAAK7B,EACVG,MAAO0B,EAAKhC,EAAIgC,EAAKrC,MACrBY,OAAQyB,EAAK7B,EAAI6B,EAAKnC,SC2B1B,SAAS8G,EACPvH,EACAwH,EACAC,UAEOD,IAAmBpC,EACtBkC,ECnCS,SACbtH,EACAyH,OAEMpG,EAAMrD,EAAUgC,GAChB0H,EAAO7F,EAAmB7B,GAC1BU,EAAiBW,EAAIX,eAEvBH,EAAQmH,EAAKC,YACblH,EAASiH,EAAKE,aACdhH,EAAI,EACJG,EAAI,KAEJL,EAAgB,CAClBH,EAAQG,EAAeH,MACvBE,EAASC,EAAeD,WAElBoH,EAAiBhI,KAEnBgI,IAAoBA,GAA+B,UAAbJ,KACxC7G,EAAIF,EAAeI,WACnBC,EAAIL,EAAeO,iBAIhB,CACLV,MAAAA,EACAE,OAAAA,EACAG,EAAGA,EAAIoB,EAAoBhC,GAC3Be,EAAAA,GDMmB+G,CAAgB9H,EAASyH,IAC1CnJ,EAAUkJ,GAzBhB,SACExH,EACAyH,OAEM7E,EAAO7C,EAAsBC,GAAS,EAAoB,UAAbyH,UAEnD7E,EAAK5B,IAAM4B,EAAK5B,IAAMhB,EAAQiD,UAC9BL,EAAK/B,KAAO+B,EAAK/B,KAAOb,EAAQgD,WAChCJ,EAAKzB,OAASyB,EAAK5B,IAAMhB,EAAQ4H,aACjChF,EAAK1B,MAAQ0B,EAAK/B,KAAOb,EAAQ2H,YACjC/E,EAAKrC,MAAQP,EAAQ2H,YACrB/E,EAAKnC,OAAST,EAAQ4H,aACtBhF,EAAKhC,EAAIgC,EAAK/B,KACd+B,EAAK7B,EAAI6B,EAAK5B,IAEP4B,EAWHmF,CAA2BP,EAAgBC,GAC3CH,EEnCS,SAAyBtH,SAChC0H,EAAO7F,EAAmB7B,GAC1BgI,EAAY5G,EAAgBpB,GAC5B0D,WAAO1D,EAAQ5B,sBAAR2F,EAAuBL,KAE9BnD,EAAQ3B,EACZ8I,EAAKO,YACLP,EAAKC,YACLjE,EAAOA,EAAKuE,YAAc,EAC1BvE,EAAOA,EAAKiE,YAAc,GAEtBlH,EAAS7B,EACb8I,EAAKQ,aACLR,EAAKE,aACLlE,EAAOA,EAAKwE,aAAe,EAC3BxE,EAAOA,EAAKkE,aAAe,GAGzBhH,GAAKoH,EAAU1G,WAAaU,EAAoBhC,GAC9Ce,GAAKiH,EAAUxG,gBAE4B,QAA7CS,EAAiByB,GAAQgE,GAAMS,YACjCvH,GAAKhC,EAAI8I,EAAKC,YAAajE,EAAOA,EAAKiE,YAAc,GAAKpH,GAGrD,CAAEA,MAAAA,EAAOE,OAAAA,EAAQG,EAAAA,EAAGG,EAAAA,GFUNqH,CAAgBvG,EAAmB7B,KA8B3C,SAASqI,EACtBrI,EACAsI,EACAC,EACAd,OAEMe,EACS,oBAAbF,EA/BJ,SAA4BtI,OACpByI,EAAkB9E,EAAkBP,EAAcpD,IAGlD0I,EADJ,CAAC,WAAY,SAASjF,QAAQxB,EAAiBjC,GAASqE,WAAa,GAEhD7F,EAAcwB,GAC/BsE,EAAgBtE,GAChBA,SAED1B,EAAUoK,GAKRD,EAAgB3D,QACrB,SAAC0C,UACClJ,EAAUkJ,IACVT,EAASS,EAAgBkB,IACO,SAAhChH,EAAY8F,MARP,GAsBHmB,CAAmB3I,GACnB,GAAGiE,OAAOqE,GACVG,YAAsBD,GAAqBD,IAC3CK,EAAsBH,EAAgB,GAEtCI,EAAeJ,EAAgBlD,QAAO,SAACuD,EAAStB,OAC9C5E,EAAO2E,EAA2BvH,EAASwH,EAAgBC,UAEjEqB,EAAQ9H,IAAMpC,EAAIgE,EAAK5B,IAAK8H,EAAQ9H,KACpC8H,EAAQ5H,MAAQpC,EAAI8D,EAAK1B,MAAO4H,EAAQ5H,OACxC4H,EAAQ3H,OAASrC,EAAI8D,EAAKzB,OAAQ2H,EAAQ3H,QAC1C2H,EAAQjI,KAAOjC,EAAIgE,EAAK/B,KAAMiI,EAAQjI,MAE/BiI,IACNvB,EAA2BvH,EAAS4I,EAAqBnB,WAE5DoB,EAAatI,MAAQsI,EAAa3H,MAAQ2H,EAAahI,KACvDgI,EAAapI,OAASoI,EAAa1H,OAAS0H,EAAa7H,IACzD6H,EAAajI,EAAIiI,EAAahI,KAC9BgI,EAAa9H,EAAI8H,EAAa7H,IAEvB6H,EGrGM,SAASE,EACtBtD,UAEQA,EAAUuD,MAAM,KAAK,GCHhB,SAASC,EAAaxD,UAC3BA,EAAUuD,MAAM,KAAK,GCDhB,SAASE,EACtBzD,SAEO,CAAC,MAAO,UAAUhC,QAAQgC,IAAc,EAAI,IAAM,ICM5C,SAAS0D,SAelBpG,EAdJqG,IAAAA,UACApJ,IAAAA,QACAyF,IAAAA,UAOM4D,EAAgB5D,EAAYsD,EAAiBtD,GAAa,KAC1D6D,EAAY7D,EAAYwD,EAAaxD,GAAa,KAClD8D,EAAUH,EAAUxI,EAAIwI,EAAU7I,MAAQ,EAAIP,EAAQO,MAAQ,EAC9DiJ,EAAUJ,EAAUrI,EAAIqI,EAAU3I,OAAS,EAAIT,EAAQS,OAAS,SAG9D4I,QACDrI,EACH+B,EAAU,CACRnC,EAAG2I,EACHxI,EAAGqI,EAAUrI,EAAIf,EAAQS,mBAGxBU,EACH4B,EAAU,CACRnC,EAAG2I,EACHxI,EAAGqI,EAAUrI,EAAIqI,EAAU3I,mBAG1BS,EACH6B,EAAU,CACRnC,EAAGwI,EAAUxI,EAAIwI,EAAU7I,MAC3BQ,EAAGyI,cAGF3I,EACHkC,EAAU,CACRnC,EAAGwI,EAAUxI,EAAIZ,EAAQO,MACzBQ,EAAGyI,iBAILzG,EAAU,CACRnC,EAAGwI,EAAUxI,EACbG,EAAGqI,EAAUrI,OAIb0I,EAAWJ,EACbH,EAAyBG,GACzB,QAEY,MAAZI,EAAkB,KACdC,EAAmB,MAAbD,EAAmB,SAAW,eAElCH,QACDpE,EACHnC,EAAQ0G,GACN1G,EAAQ0G,IAAaL,EAAUM,GAAO,EAAI1J,EAAQ0J,GAAO,cAExDvE,EACHpC,EAAQ0G,GACN1G,EAAQ0G,IAAaL,EAAUM,GAAO,EAAI1J,EAAQ0J,GAAO,WAM1D3G,EC5EM,SAAS4G,EACtBC,2BCDO,CACL5I,IAAK,EACLE,MAAO,EACPC,OAAQ,EACRN,KAAM,GDCH+I,GEPQ,SAASC,EAGtBC,EAAUC,UACHA,EAAKxE,QAAO,SAACyE,EAASC,UAC3BD,EAAQC,GAAOH,EACRE,IACN,ICwBU,SAASE,EACtBC,EACAC,YAAAA,IAAAA,EAA2B,UAUvBA,MAPF3E,UAAAA,aAAY0E,EAAM1E,gBAClBgC,SAAAA,aAAW0C,EAAM1C,eACjBa,SAAAA,advB8C,wBcwB9CC,aAAAA,aAAenD,QACfiF,eAAAA,aAAiBhF,QACjBiF,YAAAA,oBACAC,QAAAA,aAAU,IAGNX,EAAgBD,EACD,iBAAZY,EACHA,EACAV,EAAgBU,EAAStF,IAGzBuF,EAAaH,IAAmBhF,Ed9BF,Yc8BuBA,EAErDoF,EAAaN,EAAMO,MAAMrF,OACzBrF,EAAUmK,EAAMQ,SAASL,EAAcE,EAAaH,GAEpDO,EAAqBvC,EACzB/J,EAAU0B,GACNA,EACAA,EAAQ6K,gBAAkBhJ,EAAmBsI,EAAMQ,SAAStF,QAChEiD,EACAC,EACAd,GAGIqD,EAAsB/K,EAAsBoK,EAAMQ,SAASvB,WAE3D2B,EAAgB5B,EAAe,CACnCC,UAAW0B,EACX9K,QAASyK,EACThD,SAAU,WACVhC,UAAAA,IAGIuF,EAAmB1D,mBACpBmD,EACAM,IAGCE,EACJZ,IAAmBhF,EAAS2F,EAAmBF,EAI3CI,EAAkB,CACtBlK,IAAK4J,EAAmB5J,IAAMiK,EAAkBjK,IAAM4I,EAAc5I,IACpEG,OACE8J,EAAkB9J,OAClByJ,EAAmBzJ,OACnByI,EAAczI,OAChBN,KAAM+J,EAAmB/J,KAAOoK,EAAkBpK,KAAO+I,EAAc/I,KACvEK,MACE+J,EAAkB/J,MAAQ0J,EAAmB1J,MAAQ0I,EAAc1I,OAGjEiK,EAAahB,EAAMiB,cAAcC,UAGnChB,IAAmBhF,GAAU8F,EAAY,KACrCE,EAASF,EAAW1F,GAE1B6F,OAAOvB,KAAKmB,GAAiB1E,SAAQ,SAACyD,OAC9BsB,EAAW,CAACrK,EAAOC,GAAQsC,QAAQwG,IAAQ,EAAI,GAAK,EACpDuB,EAAO,CAACxK,EAAKG,GAAQsC,QAAQwG,IAAQ,EAAI,IAAM,IACrDiB,EAAgBjB,IAAQoB,EAAOG,GAAQD,YAIpCL,EC5FT,IAAMO,EAAuC,CAC3ChG,UAAW,SACXI,UAAW,GACX4B,SAAU,YAQZ,SAASiE,+BAAoBC,2BAAAA,yBACnBA,EAAKC,MACX,SAAC5L,WACGA,GAAoD,mBAAlCA,EAAQD,0BAI3B,SAAS8L,EAAgBC,YAAAA,IAAAA,EAAwC,UAEpEA,MADMC,iBAAAA,aAAmB,SAAIC,eAAAA,aAAiBP,WAGzC,SACLrC,EACA/D,EACA+E,YAAAA,IAAAA,EAA6C4B,OCzCbC,EAC9BC,ED0CE/B,EAAuB,CACzB1E,UAAW,SACX0G,iBAAkB,GAClB/B,yBAAcqB,EAAoBO,GAClCZ,cAAe,GACfT,SAAU,CACRvB,UAAAA,EACA/D,OAAAA,GAEF+G,WAAY,GACZC,OAAQ,IAGNC,EAAsC,GACtCC,GAAc,EAEZC,EAAW,CACfrC,MAAAA,EACAsC,oBAAWC,OACHtC,EACwB,mBAArBsC,EACHA,EAAiBvC,EAAMC,SACvBsC,EAENC,IAEAxC,EAAMC,yBAED4B,EACA7B,EAAMC,QACNA,GAGLD,EAAMyC,cAAgB,CACpBxD,UAAW9K,EAAU8K,GACjBzF,EAAkByF,GAClBA,EAAUyB,eACVlH,EAAkByF,EAAUyB,gBAC5B,GACJxF,OAAQ1B,EAAkB0B,QEhFlCQ,EAEMgH,EFmFMV,Ed3CC,SACbtG,OAGMsG,EAAmBvG,EAAMC,UAGxBF,EAAeJ,QAAO,SAACC,EAAKsH,UAC1BtH,EAAIvB,OACTkI,EAAiBrH,QAAO,SAAAqB,UAAYA,EAAS2G,QAAUA,QAExD,IcgC4BC,EErF/BlH,YFsFwBkG,EAAqB5B,EAAMC,QAAQvE,WEpFrDgH,EAAShH,EAAUN,QAAO,SAACsH,EAAQG,OACjCC,EAAWJ,EAAOG,EAAQ3G,aAChCwG,EAAOG,EAAQ3G,MAAQ4G,mBAEdA,EACAD,GACH5C,yBAAc6C,EAAS7C,QAAY4C,EAAQ5C,SAC3C8C,sBAAWD,EAASC,KAASF,EAAQE,QAEvCF,EACGH,IACN,IAGIvB,OAAOvB,KAAK8C,GAAQtN,KAAI,SAAA0K,UAAO4C,EAAO5C,eF0EvCE,EAAMgC,iBAAmBA,EAAiBrH,QAAO,SAACqI,UAAMA,EAAEC,WAsG5DjD,EAAMgC,iBAAiB3F,SAAQ,gBAAGH,IAAAA,SAAM+D,QAAAA,aAAU,KAAIiD,IAAAA,UAC9B,mBAAXA,EAAuB,KAC1BC,EAAYD,EAAO,CAAElD,MAAAA,EAAO9D,KAAAA,EAAMmG,SAAAA,EAAUpC,QAAAA,IAC5CmD,EAAS,aACfjB,EAAiBzF,KAAKyG,GAAaC,OAtG9Bf,EAASgB,UAQlBC,2BACMlB,SAI0BpC,EAAMQ,SAA5BvB,IAAAA,UAAW/D,IAAAA,UAIdqG,EAAiBtC,EAAW/D,IAKjC8E,EAAMO,MAAQ,CACZtB,UAAW9G,EACT8G,EACA9E,EAAgBe,GACW,UAA3B8E,EAAMC,QAAQ3C,UAEhBpC,OAAQnC,EAAcmC,IAQxB8E,EAAMuD,OAAQ,EAEdvD,EAAM1E,UAAY0E,EAAMC,QAAQ3E,UAMhC0E,EAAMgC,iBAAiB3F,SACrB,SAACL,UACEgE,EAAMiB,cAAcjF,EAASE,uBACzBF,EAAS+G,aAIb,IAAIS,EAAQ,EAAGA,EAAQxD,EAAMgC,iBAAiByB,OAAQD,QACrC,IAAhBxD,EAAMuD,aAMyBvD,EAAMgC,iBAAiBwB,GAAlD1B,IAAAA,OAAI7B,QAAAA,aAAU,KAAI/D,IAAAA,KAER,mBAAP4F,IACT9B,EAAQ8B,EAAG,CAAE9B,MAAAA,EAAOC,QAAAA,EAAS/D,KAAAA,EAAMmG,SAAAA,KAAerC,QARlDA,EAAMuD,OAAQ,EACdC,GAAS,KAcfH,QCpK8BvB,EDqK5B,kBACE,IAAI4B,SAAuB,SAACC,GAC1BtB,EAASiB,cACTK,EAAQ3D,OCtKX,kBACA+B,IACHA,EAAU,IAAI2B,SAAW,SAAAC,GACvBD,QAAQC,UAAUC,MAAK,WACrB7B,OAAU8B,EACVF,EAAQ7B,YAKPC,IDgKL+B,mBACEtB,IACAJ,GAAc,QAIbb,EAAiBtC,EAAW/D,UACxBmH,WAwBAG,IACPL,EAAiB9F,SAAQ,SAACyF,UAAOA,OACjCK,EAAmB,UAvBrBE,EAASC,WAAWrC,GAAS2D,MAAK,SAAC5D,IAC5BoC,GAAenC,EAAQ8D,eAC1B9D,EAAQ8D,cAAc/D,MAwBnBqC,GGxMX,IAAM2B,EAAU,CAAEA,SAAS,UAoCX,CACd9H,KAAM,iBACN+G,SAAS,EACTN,MAAO,QACPb,GAAI,aACJoB,OAvCF,gBAAkBlD,IAAAA,MAAOqC,IAAAA,SAAUpC,IAAAA,UACQA,EAAjCtH,OAAAA,kBAAiCsH,EAAlBgE,OAAAA,gBAEjBlQ,EAASF,EAAUmM,EAAMQ,SAAStF,QAClCuH,YACDzC,EAAMyC,cAAcxD,UACpBe,EAAMyC,cAAcvH,eAGrBvC,GACF8J,EAAcpG,SAAQ,SAAA3C,GACpBA,EAAawK,iBAAiB,SAAU7B,EAASgB,OAAQW,MAIzDC,GACFlQ,EAAOmQ,iBAAiB,SAAU7B,EAASgB,OAAQW,GAG9C,WACDrL,GACF8J,EAAcpG,SAAQ,SAAA3C,GACpBA,EAAayK,oBAAoB,SAAU9B,EAASgB,OAAQW,MAI5DC,GACFlQ,EAAOoQ,oBAAoB,SAAU9B,EAASgB,OAAQW,KAa1DjB,KAAM,WCjCQ,CACd7G,KAAM,gBACN+G,SAAS,EACTN,MAAO,OACPb,GAnBF,gBAAyB9B,IAAAA,MAAO9D,IAAAA,KAK9B8D,EAAMiB,cAAc/E,GAAQ8C,EAAe,CACzCC,UAAWe,EAAMO,MAAMtB,UACvBpJ,QAASmK,EAAMO,MAAMrF,OACrBoC,SAAU,WACVhC,UAAW0E,EAAM1E,aAWnByH,KAAM,ICcFqB,GAAa,CACjBvN,IAAK,OACLE,MAAO,OACPC,OAAQ,OACRN,KAAM,QAeD,SAAS2N,YACdnJ,IAAAA,OACAoF,IAAAA,WACAhF,IAAAA,UACA6D,IAAAA,UACAvG,IAAAA,QACAsB,IAAAA,SACAoK,IAAAA,gBACAC,IAAAA,SACAC,IAAAA,aACAlM,IAAAA,UAauBM,EAAjBnC,EAAAA,aAAI,MAAamC,EAAVhC,EAAAA,aAAI,MAGS,mBAAjB4N,EAA8BA,EAAa,CAAE/N,EAAAA,EAAGG,EAAAA,IAAO,CAAEH,EAAAA,EAAGG,EAAAA,GADlEH,IAAAA,EAAGG,IAAAA,MAGA6N,EAAO7L,EAAQ8L,eAAe,KAC9BC,EAAO/L,EAAQ8L,eAAe,KAEhCE,EAAgBlO,EAChBmO,EAAgBhO,EAEdK,EAAcnD,UAEhBwQ,EAAU,KACRlM,EAAe8B,EAAgBe,GAC/B4J,EAAa,eACbC,EAAY,iBAEZ1M,IAAiBxE,EAAUqH,IAIiB,WAA5CpD,EAHFO,EAAeX,EAAmBwD,IAGDhB,UAClB,aAAbA,IAEA4K,EAAa,eACbC,EAAY,eAKhB1M,EAAgBA,EAGdiD,IAAczE,IACZyE,IAAc5E,GAAQ4E,IAAcvE,IAAUoI,IAAcnE,EAE9D6J,EAAQ7N,EAMRJ,IAJE0B,GAAWD,IAAiBnB,GAAOA,EAAIX,eACnCW,EAAIX,eAAeD,OAEnB+B,EAAayM,IACJxE,EAAWhK,OAC1BM,GAAK0N,EAAkB,GAAK,KAI5BhJ,IAAc5E,IACZ4E,IAAczE,GAAOyE,IAActE,IAAWmI,IAAcnE,EAE9D4J,EAAQ7N,EAMRN,IAJE6B,GAAWD,IAAiBnB,GAAOA,EAAIX,eACnCW,EAAIX,eAAeH,MAEnBiC,EAAa0M,IACJzE,EAAWlK,MAC1BK,GAAK6N,EAAkB,GAAK,QAI1BU,iBACJ9K,SAAAA,GACIqK,GAAYH,OAIC,IAAjBI,EApGJ,WAAqCtN,OAART,IAAAA,EAAGG,IAAAA,EACxBqO,EAAM/N,EAAIgO,kBAAoB,QAE7B,CACLzO,EAAG7B,EAAM6B,EAAIwO,GAAOA,GAAO,EAC3BrO,EAAGhC,EAAMgC,EAAIqO,GAAOA,GAAO,GAgGvBE,CAAkB,CAAE1O,EAAAA,EAAGG,EAAAA,GAAK/C,EAAUqH,IACtC,CAAEzE,EAAAA,EAAGG,EAAAA,UAHRH,IAAAA,EAAGG,IAAAA,EAKF0N,mBAEGU,UACFH,GAAQF,EAAO,IAAM,KACrBC,GAAQH,EAAO,IAAM,KAItBlK,WACGrD,EAAIgO,kBAAoB,IAAM,eACdzO,SAAQG,uBACNH,SAAQG,gCAK5BoO,UACFH,GAAQF,EAAU/N,OAAQ,KAC1BgO,GAAQH,EAAUhO,OAAQ,KAC3B8D,UAAW,cAuDC,CACd2B,KAAM,gBACN+G,SAAS,EACTN,MAAO,cACPb,GAvDF,gBAAyB9B,IAAAA,MAAOC,IAAAA,UAM1BA,EAJFqE,gBAAAA,kBAIErE,EAHFsE,SAAAA,kBAGEtE,EADFuE,aAAAA,gBAGIQ,EAAe,CACnB1J,UAAWsD,EAAiBoB,EAAM1E,WAClC6D,UAAWL,EAAakB,EAAM1E,WAC9BJ,OAAQ8E,EAAMQ,SAAStF,OACvBoF,WAAYN,EAAMO,MAAMrF,OACxBoJ,gBAAAA,EACAhM,QAAoC,UAA3B0H,EAAMC,QAAQ3C,UAGgB,MAArC0C,EAAMiB,cAAcL,gBACtBZ,EAAMkC,OAAOhH,wBACR8E,EAAMkC,OAAOhH,OACbmJ,oBACEW,GACHpM,QAASoH,EAAMiB,cAAcL,cAC7B1G,SAAU8F,EAAMC,QAAQ3C,SACxBiH,SAAAA,EACAC,aAAAA,OAK2B,MAA7BxE,EAAMiB,cAAcmE,QACtBpF,EAAMkC,OAAOkD,uBACRpF,EAAMkC,OAAOkD,MACbf,oBACEW,GACHpM,QAASoH,EAAMiB,cAAcmE,MAC7BlL,SAAU,WACVqK,UAAU,EACVC,aAAAA,OAKNxE,EAAMiC,WAAW/G,wBACZ8E,EAAMiC,WAAW/G,gCACK8E,EAAM1E,aAWjCyH,KAAM,WC7IQ,CACd7G,KAAM,cACN+G,SAAS,EACTN,MAAO,QACPb,GAtFF,gBAAuB9B,IAAAA,MACrBmB,OAAOvB,KAAKI,EAAMQ,UAAUnE,SAAQ,SAACH,OAC7BmJ,EAAQrF,EAAMkC,OAAOhG,IAAS,GAE9B+F,EAAajC,EAAMiC,WAAW/F,IAAS,GACvCrG,EAAUmK,EAAMQ,SAAStE,GAG1B7H,EAAcwB,IAAa0B,EAAY1B,KAO5CsL,OAAOmE,OAAOzP,EAAQwP,MAAOA,GAE7BlE,OAAOvB,KAAKqC,GAAY5F,SAAQ,SAACH,OACzByD,EAAQsC,EAAW/F,IACX,IAAVyD,EACF9J,EAAQ0P,gBAAgBrJ,GAExBrG,EAAQ2P,aAAatJ,GAAgB,IAAVyD,EAAiB,GAAKA,WAiEvDuD,OA3DF,gBAAkBlD,IAAAA,MACVyF,EAAgB,CACpBvK,OAAQ,CACNhB,SAAU8F,EAAMC,QAAQ3C,SACxB5G,KAAM,IACNG,IAAK,IACL6O,OAAQ,KAEVN,MAAO,CACLlL,SAAU,YAEZ+E,UAAW,WAGbkC,OAAOmE,OAAOtF,EAAMQ,SAAStF,OAAOmK,MAAOI,EAAcvK,QACzD8E,EAAMkC,OAASuD,EAEXzF,EAAMQ,SAAS4E,OACjBjE,OAAOmE,OAAOtF,EAAMQ,SAAS4E,MAAMC,MAAOI,EAAcL,OAGnD,WACLjE,OAAOvB,KAAKI,EAAMQ,UAAUnE,SAAQ,SAACH,OAC7BrG,EAAUmK,EAAMQ,SAAStE,GACzB+F,EAAajC,EAAMiC,WAAW/F,IAAS,GASvCmJ,EAPkBlE,OAAOvB,KAC7BI,EAAMkC,OAAOwC,eAAexI,GACxB8D,EAAMkC,OAAOhG,GACbuJ,EAAcvJ,IAIUd,QAAO,SAACiK,EAAOM,UAC3CN,EAAMM,GAAY,GACXN,IACN,IAGEhR,EAAcwB,IAAa0B,EAAY1B,KAI5CsL,OAAOmE,OAAOzP,EAAQwP,MAAOA,GAE7BlE,OAAOvB,KAAKqC,GAAY5F,SAAQ,SAACuJ,GAC/B/P,EAAQ0P,gBAAgBK,YAc9BzJ,SAAU,CAAC,yBChCG,CACdD,KAAM,SACN+G,SAAS,EACTN,MAAO,OACPxG,SAAU,CAAC,iBACX2F,GAzBF,gBAAkB9B,IAAAA,MAAOC,IAAAA,QAAS/D,IAAAA,OACJ+D,EAApBiB,OAAAA,aAAS,CAAC,EAAG,KAEf6B,EAAOxH,EAAWH,QAAO,SAACC,EAAKC,UACnCD,EAAIC,GA5BD,SACLA,EACAiF,EACAW,OAEMhC,EAAgBN,EAAiBtD,GACjCuK,EAAiB,CAACnP,EAAMG,GAAKyC,QAAQ4F,IAAkB,GAAK,EAAI,IAGlD,mBAAXgC,EACHA,mBACKX,GACHjF,UAAAA,KAEF4F,EAND4E,OAAUC,cAQfD,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EAEtB,CAACnP,EAAMK,GAAOuC,QAAQ4F,IAAkB,EAC3C,CAAEzI,EAAGsP,EAAUnP,EAAGkP,GAClB,CAAErP,EAAGqP,EAAUlP,EAAGmP,GAOHC,CAAwB1K,EAAW0E,EAAMO,MAAOW,GAC1D7F,IACN,MAEc0H,EAAK/C,EAAM1E,WAApB7E,IAAAA,EAAGG,IAAAA,EAE8B,MAArCoJ,EAAMiB,cAAcL,gBACtBZ,EAAMiB,cAAcL,cAAcnK,GAAKA,EACvCuJ,EAAMiB,cAAcL,cAAchK,GAAKA,GAGzCoJ,EAAMiB,cAAc/E,GAAQ6G,ICxDxBkD,GAAO,CAAEvP,KAAM,QAASK,MAAO,OAAQC,OAAQ,MAAOH,IAAK,UAElD,SAASqP,GAAqB5K,UACnCA,EAAU6K,QAChB,0BACA,SAAAC,UAAWH,GAAKG,MCLpB,IAAMH,GAAO,CAAElL,MAAO,MAAOC,IAAK,SAEnB,SAASqL,GACtB/K,UAEQA,EAAU6K,QAAQ,cAAc,SAAAC,UAAWH,GAAKG,MCoB3C,SAASE,GACtBtG,EACAC,YAAAA,IAAAA,EAAmB,UASfA,EANF3E,IAAAA,UACA6C,IAAAA,SACAC,IAAAA,aACAgC,IAAAA,QACAmG,IAAAA,mBACAC,sBAAAA,aAAwBC,IAGpBtH,EAAYL,EAAaxD,GAEzBC,EAAa4D,EACfoH,EACEpL,EACAA,EAAoBR,QAClB,SAACW,UAAcwD,EAAaxD,KAAe6D,KAE/CrE,EAEA4L,EAAoBnL,EAAWZ,QACjC,SAACW,UAAckL,EAAsBlN,QAAQgC,IAAc,KAG5B,IAA7BoL,EAAkBjD,SACpBiD,EAAoBnL,OAIhBoL,EAA0BD,EAAkBtL,QAAO,SAACC,EAAKC,UAC7DD,EAAIC,GAAayE,EAAeC,EAAO,CACrC1E,UAAAA,EACA6C,SAAAA,EACAC,aAAAA,EACAgC,QAAAA,IACCxB,EAAiBtD,IAEbD,IACN,WAEI8F,OAAOvB,KAAK+G,GAAW5K,MAAK,SAAC6K,EAAGC,UAAMF,EAAUC,GAAKD,EAAUE,aCkGxD,CACd3K,KAAM,OACN+G,SAAS,EACTN,MAAO,OACPb,GAvIF,gBAAgB9B,IAAAA,MAAOC,IAAAA,QAAS/D,IAAAA,SAC1B8D,EAAMiB,cAAc/E,GAAM4K,iBAc1B7G,EATFX,SAAUyH,kBASR9G,EARF+G,QAASC,gBACWC,EAOlBjH,EAPFkH,mBACA/G,EAMEH,EANFG,QACAjC,EAKE8B,EALF9B,SACAC,EAIE6B,EAJF7B,aACA+B,EAGEF,EAHFE,cAGEF,EAFFsG,eAAAA,gBACAC,EACEvG,EADFuG,sBAGIY,EAAqBpH,EAAMC,QAAQ3E,UACnC4D,EAAgBN,EAAiBwI,GAGjCD,EACJD,IAHsBhI,IAAkBkI,IAInBb,EACjB,CAACL,GAAqBkB,IAtC9B,SAAuC9L,MACjCsD,EAAiBtD,KAAeT,QAC3B,OAGHwM,EAAoBnB,GAAqB5K,SAExC,CACL+K,GAA8B/K,GAC9B+L,EACAhB,GAA8BgB,IA6B1BC,CAA8BF,IAE9B7L,EAAa,CAAC6L,UAAuBD,GAAoB/L,QAC7D,SAACC,EAAKC,UACGD,EAAIvB,OACT8E,EAAiBtD,KAAeT,EAC5ByL,GAAqBtG,EAAO,CAC1B1E,UAAAA,EACA6C,SAAAA,EACAC,aAAAA,EACAgC,QAAAA,EACAmG,eAAAA,EACAC,sBAAAA,IAEFlL,KAGR,IAGIiM,EAAgBvH,EAAMO,MAAMtB,UAC5BqB,EAAaN,EAAMO,MAAMrF,OAEzBsM,EAAY,IAAI7L,IAClB8L,GAAqB,EACrBC,EAAwBnM,EAAW,GAE9BoM,EAAI,EAAGA,EAAIpM,EAAWkI,OAAQkE,IAAK,KACpCrM,EAAYC,EAAWoM,GACvBzI,EAAgBN,EAAiBtD,GACjCsM,EAAmB9I,EAAaxD,KAAeP,EAC/C8M,EAAa,CAAChR,EAAKG,GAAQsC,QAAQ4F,IAAkB,EACrDK,EAAMsI,EAAa,QAAU,SAE7B7P,EAAW+H,EAAeC,EAAO,CACrC1E,UAAAA,EACA6C,SAAAA,EACAC,aAAAA,EACA+B,YAAAA,EACAC,QAAAA,IAGE0H,EAAyBD,EACzBD,EACE7Q,EACAL,EACFkR,EACA5Q,EACAH,EAEA0Q,EAAchI,GAAOe,EAAWf,KAClCuI,EAAoB5B,GAAqB4B,QAGrCC,EAAwB7B,GAAqB4B,GAE7CE,EAAS,MAEXjB,GACFiB,EAAOtL,KAAK1E,EAASkH,IAAkB,GAGrC+H,GACFe,EAAOtL,KACL1E,EAAS8P,IAAsB,EAC/B9P,EAAS+P,IAAqB,GAI9BC,EAAOC,OAAM,SAACC,UAAUA,KAAQ,CAClCR,EAAwBpM,EACxBmM,GAAqB,QAIvBD,EAAU7K,IAAIrB,EAAW0M,MAGvBP,qBAIOE,OACDQ,EAAmB5M,EAAW6M,MAAK,SAAC9M,OAClC0M,EAASR,EAAU/K,IAAInB,MACzB0M,SACKA,EAAOK,MAAM,EAAGV,GAAGM,OAAM,SAACC,UAAUA,WAI3CC,SACFT,EAAwBS,WATnBR,EAFcpB,EAAiB,EAAI,EAEfoB,EAAI,EAAGA,IAAK,gBAAhCA,GAUL,MAKF3H,EAAM1E,YAAcoM,IACtB1H,EAAMiB,cAAc/E,GAAM4K,OAAQ,EAClC9G,EAAM1E,UAAYoM,EAClB1H,EAAMuD,OAAQ,KAWhBnH,iBAAkB,CAAC,UACnB2G,KAAM,CAAE+D,OAAO,IC5KV,SAASwB,GAAO3T,EAAagL,EAAelL,UAC1C8T,EAAQ5T,EAAK6T,EAAQ7I,EAAOlL,WCiNrB,CACdyH,KAAM,kBACN+G,SAAS,EACTN,MAAO,OACPb,GA1KF,gBAA2B9B,IAAAA,MAAOC,IAAAA,QAAS/D,IAAAA,OAUrC+D,EARFX,SAAUyH,kBAQR9G,EAPF+G,QAASC,gBACT9I,EAME8B,EANF9B,SACAC,EAKE6B,EALF7B,aACA+B,EAIEF,EAJFE,YACAC,EAGEH,EAHFG,UAGEH,EAFFwI,OAAAA,kBAEExI,EADFyI,aAAAA,aAAe,IAGX1Q,EAAW+H,EAAeC,EAAO,CACrC7B,SAAAA,EACAC,aAAAA,EACAgC,QAAAA,EACAD,YAAAA,IAEIjB,EAAgBN,EAAiBoB,EAAM1E,WACvC6D,EAAYL,EAAakB,EAAM1E,WAC/BqN,GAAmBxJ,EACnBG,EAAWP,EAAyBG,GACpC8H,EClEU,MDkEW1H,EClEL,IAAM,IDmEtBsB,EAAgBZ,EAAMiB,cAAcL,cACpC2G,EAAgBvH,EAAMO,MAAMtB,UAC5BqB,EAAaN,EAAMO,MAAMrF,OACzB0N,EACoB,mBAAjBF,EACHA,mBACK1I,EAAMO,OACTjF,UAAW0E,EAAM1E,aAEnBoN,EACAG,EACyB,iBAAtBD,EACH,CAAEtJ,SAAUsJ,EAAmB5B,QAAS4B,kBACtCtJ,SAAU,EAAG0H,QAAS,GAAM4B,GAC9BE,EAAsB9I,EAAMiB,cAAcC,OAC5ClB,EAAMiB,cAAcC,OAAOlB,EAAM1E,WACjC,KAEEyH,EAAO,CAAEtM,EAAG,EAAGG,EAAG,MAEnBgK,MAIDmG,EAAe,OACXgC,EAAwB,MAAbzJ,EAAmBzI,EAAMH,EACpCsS,EAAuB,MAAb1J,EAAmBtI,EAASD,EACtCwI,EAAmB,MAAbD,EAAmB,SAAW,QACpC4B,EAASN,EAActB,GAEvB3K,EAAMuM,EAASlJ,EAAS+Q,GACxBtU,EAAMyM,EAASlJ,EAASgR,GAExBC,EAAWR,GAAUnI,EAAWf,GAAO,EAAI,EAE3C2J,EAAS/J,IAAcpE,EAAQwM,EAAchI,GAAOe,EAAWf,GAC/D4J,EAAShK,IAAcpE,GAASuF,EAAWf,IAAQgI,EAAchI,GAIjE6J,EAAepJ,EAAMQ,SAAS4E,MAC9BiE,EACJZ,GAAUW,EACNrQ,EAAcqQ,GACd,CAAEhT,MAAO,EAAGE,OAAQ,GACpBgT,GAAqBtJ,EAAMiB,cAAc,oBAC3CjB,EAAMiB,cAAc,oBAAoBb,QhBhHvC,CACLvJ,IAAK,EACLE,MAAO,EACPC,OAAQ,EACRN,KAAM,GgB8GA6S,GAAkBD,GAAmBP,GACrCS,GAAkBF,GAAmBN,GAOrCS,GAAWnB,GAAO,EAAGf,EAAchI,GAAM8J,EAAU9J,IAEnDmK,GAAYf,EACdpB,EAAchI,GAAO,EACrB0J,EACAQ,GACAF,GACAV,EAA4BvJ,SAC5B4J,EACAO,GACAF,GACAV,EAA4BvJ,SAC1BqK,GAAYhB,GACbpB,EAAchI,GAAO,EACtB0J,EACAQ,GACAD,GACAX,EAA4BvJ,SAC5B6J,EACAM,GACAD,GACAX,EAA4BvJ,SAE1BsK,GACJ5J,EAAMQ,SAAS4E,OAASjL,EAAgB6F,EAAMQ,SAAS4E,OACnDyE,GAAeD,GACJ,MAAbtK,EACEsK,GAAkB9Q,WAAa,EAC/B8Q,GAAkB/Q,YAAc,EAClC,EAEEiR,kBAAsBhB,SAAAA,EAAsBxJ,MAAa,EAEzDyK,GAAY7I,EAASyI,GAAYG,GAEjCE,GAAkB1B,GACtBG,EAASD,EAAQ7T,EAJDuM,EAASwI,GAAYI,GAAsBD,IAIxBlV,EACnCuM,EACAuH,EAASF,EAAQ9T,EAAKsV,IAAatV,GAGrCmM,EAActB,GAAY0K,GAC1BjH,EAAKzD,GAAY0K,GAAkB9I,KAGjC+F,EAAc,QACV8B,GAAwB,MAAbzJ,EAAmBzI,EAAMH,EACpCsS,GAAuB,MAAb1J,EAAmBtI,EAASD,EACtCmK,GAASN,EAAcoG,GAEvBzH,GAAkB,MAAZyH,EAAkB,SAAW,QAEnCrS,GAAMuM,GAASlJ,EAAS+Q,IACxBtU,GAAMyM,GAASlJ,EAASgR,IAExBiB,IAAuD,IAAxC,CAACpT,EAAKH,GAAM4C,QAAQ4F,GAEnC4K,mBAAsBhB,SAAAA,EAAsB9B,OAAY,EACxDkD,GAAYD,GACdtV,GACAuM,GACAqG,EAAchI,IACde,EAAWf,IACXuK,GACAjB,EAA4B7B,QAC1B+C,GAAYE,GACd/I,GACAqG,EAAchI,IACde,EAAWf,IACXuK,GACAjB,EAA4B7B,QAC5BvS,GAEEuV,GACJvB,GAAUwB,GDjMT,SAAwBtV,EAAagL,EAAelL,OACnD0V,EAAI7B,GAAO3T,EAAKgL,EAAOlL,UACtB0V,EAAI1V,EAAMA,EAAM0V,ECgMfC,CAAeF,GAAWhJ,GAAQ6I,IAClCzB,GAAOG,EAASyB,GAAYvV,GAAKuM,GAAQuH,EAASsB,GAAYtV,IAEpEmM,EAAcoG,GAAWgD,GACzBjH,EAAKiE,GAAWgD,GAAkB9I,GAGpClB,EAAMiB,cAAc/E,GAAQ6G,IAU5B3G,iBAAkB,CAAC,kBE3GL,CACdF,KAAM,QACN+G,SAAS,EACTN,MAAO,OACPb,GA7EF,kBAAiB9B,IAAAA,MAAO9D,IAAAA,KAAM+D,IAAAA,QACtBmJ,EAAepJ,EAAMQ,SAAS4E,MAC9BxE,EAAgBZ,EAAMiB,cAAcL,cACpC1B,EAAgBN,EAAiBoB,EAAM1E,WACvC+F,EAAOtC,EAAyBG,GAEhCK,EADa,CAAC7I,EAAMK,GAAOuC,QAAQ4F,IAAkB,EAClC,SAAW,WAE/BkK,GAAiBxI,OAIhBnB,EAzBgB,SAACW,EAASJ,UAMzBR,EACc,iBANrBY,EACqB,mBAAZA,EACHA,mBAAaJ,EAAMO,OAAOjF,UAAW0E,EAAM1E,aAC3C8E,GAIAA,EACAV,EAAgBU,EAAStF,IAgBTuP,CAAgBpK,EAAQG,QAASJ,GACjDqJ,EAAYtQ,EAAcqQ,GAC1BkB,EAAmB,MAATjJ,EAAexK,EAAMH,EAC/B6T,EAAmB,MAATlJ,EAAerK,EAASD,EAElCyT,EACJxK,EAAMO,MAAMtB,UAAUM,GACtBS,EAAMO,MAAMtB,UAAUoC,GACtBT,EAAcS,GACdrB,EAAMO,MAAMrF,OAAOqE,GACfkL,EAAY7J,EAAcS,GAAQrB,EAAMO,MAAMtB,UAAUoC,GAExDuI,EAAoBzP,EAAgBiP,GACpCsB,EAAad,EACN,MAATvI,EACEuI,EAAkBnM,cAAgB,EAClCmM,EAAkBpM,aAAe,EACnC,EAEEmN,EAAoBH,EAAU,EAAIC,EAAY,EAI9C9V,EAAM8K,EAAc6K,GACpB7V,EAAMiW,EAAarB,EAAU9J,GAAOE,EAAc8K,GAClDK,EAASF,EAAa,EAAIrB,EAAU9J,GAAO,EAAIoL,EAC/CzJ,EAASoH,GAAO3T,EAAKiW,EAAQnW,GAG7BoW,EAAmBxJ,EACzBrB,EAAMiB,cAAc/E,WACjB2O,GAAW3J,IACZ4J,aAAc5J,EAAS0J,OAkCzB1H,OA9BF,gBAAkBlD,IAAAA,UAAOC,QACjBpK,QAASuT,aAAe,wBAEV,MAAhBA,IAKwB,iBAAjBA,IACTA,EAAepJ,EAAMQ,SAAStF,OAAO6P,cAAc3B,MAOhDxM,EAASoD,EAAMQ,SAAStF,OAAQkO,KAIrCpJ,EAAMQ,SAAS4E,MAAQgE,IAWvBjN,SAAU,CAAC,iBACXC,iBAAkB,CAAC,oBC3GrB,SAAS4O,GACPhT,EACAS,EACAwS,mBAAAA,IAAAA,EAA4B,CAAExU,EAAG,EAAGG,EAAG,IAEhC,CACLC,IAAKmB,EAASnB,IAAM4B,EAAKnC,OAAS2U,EAAiBrU,EACnDG,MAAOiB,EAASjB,MAAQ0B,EAAKrC,MAAQ6U,EAAiBxU,EACtDO,OAAQgB,EAAShB,OAASyB,EAAKnC,OAAS2U,EAAiBrU,EACzDF,KAAMsB,EAAStB,KAAO+B,EAAKrC,MAAQ6U,EAAiBxU,GAIxD,SAASyU,GAAsBlT,SACtB,CAACnB,EAAKE,EAAOC,EAAQN,GAAM+K,MAAK,SAAC0J,UAASnT,EAASmT,IAAS,YA4CrD,CACdjP,KAAM,OACN+G,SAAS,EACTN,MAAO,OACPvG,iBAAkB,CAAC,mBACnB0F,GA9CF,gBAAgB9B,IAAAA,MAAO9D,IAAAA,KACfqL,EAAgBvH,EAAMO,MAAMtB,UAC5BqB,EAAaN,EAAMO,MAAMrF,OACzB+P,EAAmBjL,EAAMiB,cAAcmK,gBAEvCC,EAAoBtL,EAAeC,EAAO,CAC9CE,eAAgB,cAEZoL,EAAoBvL,EAAeC,EAAO,CAC9CG,aAAa,IAGToL,EAA2BP,GAC/BK,EACA9D,GAEIiE,EAAsBR,GAC1BM,EACAhL,EACA2K,GAGIQ,EAAoBP,GAAsBK,GAC1CG,EAAmBR,GAAsBM,GAE/CxL,EAAMiB,cAAc/E,GAAQ,CAC1BqP,yBAAAA,EACAC,oBAAAA,EACAC,kBAAAA,EACAC,iBAAAA,GAGF1L,EAAMiC,WAAW/G,wBACZ8E,EAAMiC,WAAW/G,uCACYuQ,wBACTC,MC9CrBC,GAAejK,EAAgB,CAAEE,iBAPd,CACvBgK,GACAhL,GACAiL,GACAC,MCCIlK,GAAmB,CACvBgK,GACAhL,GACAiL,GACAC,GACA5K,GACA6K,GACAX,GACAhG,GACA4G,IAGIL,GAAejK,EAAgB,CAAEE,iBAAAA"}
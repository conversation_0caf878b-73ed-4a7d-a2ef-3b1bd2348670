{"version": 3, "file": "workbox-cacheable-response.dev.js", "sources": ["../_version.js", "../CacheableResponse.js", "../CacheableResponsePlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:cacheable-response:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport './_version.js';\n/**\n * This class allows you to set up rules determining what\n * status codes and/or headers need to be present in order for a\n * [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response)\n * to be considered cacheable.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponse {\n    /**\n     * To construct a new CacheableResponse instance you must provide at least\n     * one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.statuses || config.headers)) {\n                throw new WorkboxError('statuses-or-headers-required', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.statuses) {\n                assert.isArray(config.statuses, {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.statuses',\n                });\n            }\n            if (config.headers) {\n                assert.isType(config.headers, 'object', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.headers',\n                });\n            }\n        }\n        this._statuses = config.statuses;\n        this._headers = config.headers;\n    }\n    /**\n     * Checks a response to see whether it's cacheable or not, based on this\n     * object's configuration.\n     *\n     * @param {Response} response The response whose cacheability is being\n     * checked.\n     * @return {boolean} `true` if the `Response` is cacheable, and `false`\n     * otherwise.\n     */\n    isResponseCacheable(response) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(response, Response, {\n                moduleName: 'workbox-cacheable-response',\n                className: 'CacheableResponse',\n                funcName: 'isResponseCacheable',\n                paramName: 'response',\n            });\n        }\n        let cacheable = true;\n        if (this._statuses) {\n            cacheable = this._statuses.includes(response.status);\n        }\n        if (this._headers && cacheable) {\n            cacheable = Object.keys(this._headers).some((headerName) => {\n                return response.headers.get(headerName) === this._headers[headerName];\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (!cacheable) {\n                logger.groupCollapsed(`The request for ` +\n                    `'${getFriendlyURL(response.url)}' returned a response that does ` +\n                    `not meet the criteria for being cached.`);\n                logger.groupCollapsed(`View cacheability criteria here.`);\n                logger.log(`Cacheable statuses: ` + JSON.stringify(this._statuses));\n                logger.log(`Cacheable headers: ` + JSON.stringify(this._headers, null, 2));\n                logger.groupEnd();\n                const logFriendlyHeaders = {};\n                response.headers.forEach((value, key) => {\n                    logFriendlyHeaders[key] = value;\n                });\n                logger.groupCollapsed(`View response status and headers here.`);\n                logger.log(`Response status: ${response.status}`);\n                logger.log(`Response headers: ` + JSON.stringify(logFriendlyHeaders, null, 2));\n                logger.groupEnd();\n                logger.groupCollapsed(`View full response details here.`);\n                logger.log(response.headers);\n                logger.log(response);\n                logger.groupEnd();\n                logger.groupEnd();\n            }\n        }\n        return cacheable;\n    }\n}\nexport { CacheableResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { CacheableResponse, } from './CacheableResponse.js';\nimport './_version.js';\n/**\n * A class implementing the `cacheWillUpdate` lifecycle callback. This makes it\n * easier to add in cacheability checks to requests made via Workbox's built-in\n * strategies.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponsePlugin {\n    /**\n     * To construct a new CacheableResponsePlugin instance you must provide at\n     * least one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config) {\n        /**\n         * @param {Object} options\n         * @param {Response} options.response\n         * @return {Response|null}\n         * @private\n         */\n        this.cacheWillUpdate = async ({ response }) => {\n            if (this._cacheableResponse.isResponseCacheable(response)) {\n                return response;\n            }\n            return null;\n        };\n        this._cacheableResponse = new CacheableResponse(config);\n    }\n}\nexport { CacheableResponsePlugin };\n"], "names": ["self", "_", "e", "CacheableResponse", "constructor", "config", "statuses", "headers", "WorkboxError", "moduleName", "className", "funcName", "assert", "isArray", "paramName", "isType", "_statuses", "_headers", "isResponseCacheable", "response", "isInstance", "Response", "cacheable", "includes", "status", "Object", "keys", "some", "headerName", "get", "logger", "groupCollapsed", "getFriendlyURL", "url", "log", "JSON", "stringify", "groupEnd", "logFriendlyHeaders", "for<PERSON>ach", "value", "key", "CacheableResponsePlugin", "cacheWillUpdate", "_cacheableResponse"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,kCAAkC,CAAC,IAAIC,CAAC,EAAE,CAAA;IACnD,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,iBAAiB,CAAC;IACpB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAWA,CAACC,MAAM,GAAG,EAAE,EAAE;IACrB,IAA2C;UACvC,IAAI,EAAEA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACE,OAAO,CAAC,EAAE;IACtC,QAAA,MAAM,IAAIC,4BAAY,CAAC,8BAA8B,EAAE;IACnDC,UAAAA,UAAU,EAAE,4BAA4B;IACxCC,UAAAA,SAAS,EAAE,mBAAmB;IAC9BC,UAAAA,QAAQ,EAAE,aAAA;IACd,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAIN,MAAM,CAACC,QAAQ,EAAE;IACjBM,QAAAA,gBAAM,CAACC,OAAO,CAACR,MAAM,CAACC,QAAQ,EAAE;IAC5BG,UAAAA,UAAU,EAAE,4BAA4B;IACxCC,UAAAA,SAAS,EAAE,mBAAmB;IAC9BC,UAAAA,QAAQ,EAAE,aAAa;IACvBG,UAAAA,SAAS,EAAE,iBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAIT,MAAM,CAACE,OAAO,EAAE;YAChBK,gBAAM,CAACG,MAAM,CAACV,MAAM,CAACE,OAAO,EAAE,QAAQ,EAAE;IACpCE,UAAAA,UAAU,EAAE,4BAA4B;IACxCC,UAAAA,SAAS,EAAE,mBAAmB;IAC9BC,UAAAA,QAAQ,EAAE,aAAa;IACvBG,UAAAA,SAAS,EAAE,gBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;IACA,IAAA,IAAI,CAACE,SAAS,GAAGX,MAAM,CAACC,QAAQ,CAAA;IAChC,IAAA,IAAI,CAACW,QAAQ,GAAGZ,MAAM,CAACE,OAAO,CAAA;IAClC,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIW,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,IAA2C;IACvCP,MAAAA,gBAAM,CAACQ,UAAU,CAACD,QAAQ,EAAEE,QAAQ,EAAE;IAClCZ,QAAAA,UAAU,EAAE,4BAA4B;IACxCC,QAAAA,SAAS,EAAE,mBAAmB;IAC9BC,QAAAA,QAAQ,EAAE,qBAAqB;IAC/BG,QAAAA,SAAS,EAAE,UAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,IAAIQ,SAAS,GAAG,IAAI,CAAA;QACpB,IAAI,IAAI,CAACN,SAAS,EAAE;UAChBM,SAAS,GAAG,IAAI,CAACN,SAAS,CAACO,QAAQ,CAACJ,QAAQ,CAACK,MAAM,CAAC,CAAA;IACxD,KAAA;IACA,IAAA,IAAI,IAAI,CAACP,QAAQ,IAAIK,SAAS,EAAE;IAC5BA,MAAAA,SAAS,GAAGG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACT,QAAQ,CAAC,CAACU,IAAI,CAAEC,UAAU,IAAK;IACxD,QAAA,OAAOT,QAAQ,CAACZ,OAAO,CAACsB,GAAG,CAACD,UAAU,CAAC,KAAK,IAAI,CAACX,QAAQ,CAACW,UAAU,CAAC,CAAA;IACzE,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAA2C;UACvC,IAAI,CAACN,SAAS,EAAE;IACZQ,QAAAA,gBAAM,CAACC,cAAc,CAAE,CAAA,gBAAA,CAAiB,GACnC,CAAGC,CAAAA,EAAAA,gCAAc,CAACb,QAAQ,CAACc,GAAG,CAAE,CAAiC,gCAAA,CAAA,GACjE,yCAAwC,CAAC,CAAA;IAC9CH,QAAAA,gBAAM,CAACC,cAAc,CAAE,CAAA,gCAAA,CAAiC,CAAC,CAAA;IACzDD,QAAAA,gBAAM,CAACI,GAAG,CAAE,CAAA,oBAAA,CAAqB,GAAGC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAA;IACnEc,QAAAA,gBAAM,CAACI,GAAG,CAAE,CAAoB,mBAAA,CAAA,GAAGC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnB,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;YAC1Ea,gBAAM,CAACO,QAAQ,EAAE,CAAA;YACjB,MAAMC,kBAAkB,GAAG,EAAE,CAAA;YAC7BnB,QAAQ,CAACZ,OAAO,CAACgC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;IACrCH,UAAAA,kBAAkB,CAACG,GAAG,CAAC,GAAGD,KAAK,CAAA;IACnC,SAAC,CAAC,CAAA;IACFV,QAAAA,gBAAM,CAACC,cAAc,CAAE,CAAA,sCAAA,CAAuC,CAAC,CAAA;YAC/DD,gBAAM,CAACI,GAAG,CAAE,CAAA,iBAAA,EAAmBf,QAAQ,CAACK,MAAO,EAAC,CAAC,CAAA;IACjDM,QAAAA,gBAAM,CAACI,GAAG,CAAE,CAAA,kBAAA,CAAmB,GAAGC,IAAI,CAACC,SAAS,CAACE,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;YAC9ER,gBAAM,CAACO,QAAQ,EAAE,CAAA;IACjBP,QAAAA,gBAAM,CAACC,cAAc,CAAE,CAAA,gCAAA,CAAiC,CAAC,CAAA;IACzDD,QAAAA,gBAAM,CAACI,GAAG,CAACf,QAAQ,CAACZ,OAAO,CAAC,CAAA;IAC5BuB,QAAAA,gBAAM,CAACI,GAAG,CAACf,QAAQ,CAAC,CAAA;YACpBW,gBAAM,CAACO,QAAQ,EAAE,CAAA;YACjBP,gBAAM,CAACO,QAAQ,EAAE,CAAA;IACrB,OAAA;IACJ,KAAA;IACA,IAAA,OAAOf,SAAS,CAAA;IACpB,GAAA;IACJ;;ICrHA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMoB,uBAAuB,CAAC;IAC1B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACItC,WAAWA,CAACC,MAAM,EAAE;IAChB;IACR;IACA;IACA;IACA;IACA;QACQ,IAAI,CAACsC,eAAe,GAAG,OAAO;IAAExB,MAAAA,QAAAA;IAAS,KAAC,KAAK;UAC3C,IAAI,IAAI,CAACyB,kBAAkB,CAAC1B,mBAAmB,CAACC,QAAQ,CAAC,EAAE;IACvD,QAAA,OAAOA,QAAQ,CAAA;IACnB,OAAA;IACA,MAAA,OAAO,IAAI,CAAA;SACd,CAAA;IACD,IAAA,IAAI,CAACyB,kBAAkB,GAAG,IAAIzC,iBAAiB,CAACE,MAAM,CAAC,CAAA;IAC3D,GAAA;IACJ;;;;;;;;;;;"}
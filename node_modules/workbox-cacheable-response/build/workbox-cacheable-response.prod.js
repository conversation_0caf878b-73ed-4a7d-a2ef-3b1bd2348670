this.workbox=this.workbox||{},this.workbox.cacheableResponse=function(s){"use strict";try{self["workbox:cacheable-response:7.2.0"]&&_()}catch(s){}class t{constructor(s={}){this._=s.statuses,this.G=s.headers}isResponseCacheable(s){let t=!0;return this._&&(t=this._.includes(s.status)),this.G&&t&&(t=Object.keys(this.G).some((t=>s.headers.get(t)===this.G[t]))),t}}return s.CacheableResponse=t,s.CacheableResponsePlugin=class{constructor(s){this.cacheWillUpdate=async({response:s})=>this.H.isResponseCacheable(s)?s:null,this.H=new t(s)}},s}({});
//# sourceMappingURL=workbox-cacheable-response.prod.js.map

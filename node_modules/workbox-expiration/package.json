{"name": "workbox-expiration", "version": "7.3.0", "license": "MIT", "author": "Google's Web DevRel Team and Google's Aurora Team", "description": "A service worker helper library that expires cached responses based on age or maximum number of entries.", "repository": {"type": "git", "url": "git+https://github.com/googlechrome/workbox.git"}, "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "workbox-plugin"], "workbox": {"browserNamespace": "workbox.expiration", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"idb": "^7.0.1", "workbox-core": "7.3.0"}, "gitHead": "c77dceb54d4af1749db95316710d6430e82b0c48"}
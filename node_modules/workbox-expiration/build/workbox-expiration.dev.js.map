{"version": 3, "file": "workbox-expiration.dev.js", "sources": ["../node_modules/idb/build/wrap-idb-value.js", "../node_modules/idb/build/index.js", "../_version.js", "../models/CacheTimestampsModel.js", "../CacheExpiration.js", "../ExpirationPlugin.js"], "sourcesContent": ["const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction));\n        });\n    }\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking)\n            db.addEventListener('versionchange', () => blocking());\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked)\n        request.addEventListener('blocked', () => blocked());\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:expiration:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB, deleteDB } from 'idb';\nimport '../_version.js';\nconst DB_NAME = 'workbox-expiration';\nconst CACHE_OBJECT_STORE = 'cache-entries';\nconst normalizeURL = (unNormalizedUrl) => {\n    const url = new URL(unNormalizedUrl, location.href);\n    url.hash = '';\n    return url.href;\n};\n/**\n * Returns the timestamp model.\n *\n * @private\n */\nclass CacheTimestampsModel {\n    /**\n     *\n     * @param {string} cacheName\n     *\n     * @private\n     */\n    constructor(cacheName) {\n        this._db = null;\n        this._cacheName = cacheName;\n    }\n    /**\n     * Performs an upgrade of indexedDB.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDb(db) {\n        // TODO(philipwalton): EdgeHTML doesn't support arrays as a keyPath, so we\n        // have to use the `id` keyPath here and create our own values (a\n        // concatenation of `url + cacheName`) instead of simply using\n        // `keyPath: ['url', 'cacheName']`, which is supported in other browsers.\n        const objStore = db.createObjectStore(CACHE_OBJECT_STORE, { keyPath: 'id' });\n        // TODO(philipwalton): once we don't have to support EdgeHTML, we can\n        // create a single index with the keyPath `['cacheName', 'timestamp']`\n        // instead of doing both these indexes.\n        objStore.createIndex('cacheName', 'cacheName', { unique: false });\n        objStore.createIndex('timestamp', 'timestamp', { unique: false });\n    }\n    /**\n     * Performs an upgrade of indexedDB and deletes deprecated DBs.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDbAndDeleteOldDbs(db) {\n        this._upgradeDb(db);\n        if (this._cacheName) {\n            void deleteDB(this._cacheName);\n        }\n    }\n    /**\n     * @param {string} url\n     * @param {number} timestamp\n     *\n     * @private\n     */\n    async setTimestamp(url, timestamp) {\n        url = normalizeURL(url);\n        const entry = {\n            url,\n            timestamp,\n            cacheName: this._cacheName,\n            // Creating an ID from the URL and cache name won't be necessary once\n            // Edge switches to Chromium and all browsers we support work with\n            // array keyPaths.\n            id: this._getId(url),\n        };\n        const db = await this.getDb();\n        const tx = db.transaction(CACHE_OBJECT_STORE, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.put(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the timestamp stored for a given URL.\n     *\n     * @param {string} url\n     * @return {number | undefined}\n     *\n     * @private\n     */\n    async getTimestamp(url) {\n        const db = await this.getDb();\n        const entry = await db.get(CACHE_OBJECT_STORE, this._getId(url));\n        return entry === null || entry === void 0 ? void 0 : entry.timestamp;\n    }\n    /**\n     * Iterates through all the entries in the object store (from newest to\n     * oldest) and removes entries once either `maxCount` is reached or the\n     * entry's timestamp is less than `minTimestamp`.\n     *\n     * @param {number} minTimestamp\n     * @param {number} maxCount\n     * @return {Array<string>}\n     *\n     * @private\n     */\n    async expireEntries(minTimestamp, maxCount) {\n        const db = await this.getDb();\n        let cursor = await db\n            .transaction(CACHE_OBJECT_STORE)\n            .store.index('timestamp')\n            .openCursor(null, 'prev');\n        const entriesToDelete = [];\n        let entriesNotDeletedCount = 0;\n        while (cursor) {\n            const result = cursor.value;\n            // TODO(philipwalton): once we can use a multi-key index, we\n            // won't have to check `cacheName` here.\n            if (result.cacheName === this._cacheName) {\n                // Delete an entry if it's older than the max age or\n                // if we already have the max number allowed.\n                if ((minTimestamp && result.timestamp < minTimestamp) ||\n                    (maxCount && entriesNotDeletedCount >= maxCount)) {\n                    // TODO(philipwalton): we should be able to delete the\n                    // entry right here, but doing so causes an iteration\n                    // bug in Safari stable (fixed in TP). Instead we can\n                    // store the keys of the entries to delete, and then\n                    // delete the separate transactions.\n                    // https://github.com/GoogleChrome/workbox/issues/1978\n                    // cursor.delete();\n                    // We only need to return the URL, not the whole entry.\n                    entriesToDelete.push(cursor.value);\n                }\n                else {\n                    entriesNotDeletedCount++;\n                }\n            }\n            cursor = await cursor.continue();\n        }\n        // TODO(philipwalton): once the Safari bug in the following issue is fixed,\n        // we should be able to remove this loop and do the entry deletion in the\n        // cursor loop above:\n        // https://github.com/GoogleChrome/workbox/issues/1978\n        const urlsDeleted = [];\n        for (const entry of entriesToDelete) {\n            await db.delete(CACHE_OBJECT_STORE, entry.id);\n            urlsDeleted.push(entry.url);\n        }\n        return urlsDeleted;\n    }\n    /**\n     * Takes a URL and returns an ID that will be unique in the object store.\n     *\n     * @param {string} url\n     * @return {string}\n     *\n     * @private\n     */\n    _getId(url) {\n        // Creating an ID from the URL and cache name won't be necessary once\n        // Edge switches to Chromium and all browsers we support work with\n        // array keyPaths.\n        return this._cacheName + '|' + normalizeURL(url);\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, 1, {\n                upgrade: this._upgradeDbAndDeleteOldDbs.bind(this),\n            });\n        }\n        return this._db;\n    }\n}\nexport { CacheTimestampsModel };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheTimestampsModel } from './models/CacheTimestampsModel.js';\nimport './_version.js';\n/**\n * The `CacheExpiration` class allows you define an expiration and / or\n * limit on the number of responses stored in a\n * [`Cache`](https://developer.mozilla.org/en-US/docs/Web/API/Cache).\n *\n * @memberof workbox-expiration\n */\nclass CacheExpiration {\n    /**\n     * To construct a new CacheExpiration instance you must provide at least\n     * one of the `config` properties.\n     *\n     * @param {string} cacheName Name of the cache to apply restrictions to.\n     * @param {Object} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     */\n    constructor(cacheName, config = {}) {\n        this._isRunning = false;\n        this._rerunRequested = false;\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(cacheName, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'constructor',\n                paramName: 'cacheName',\n            });\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._maxEntries = config.maxEntries;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._matchOptions = config.matchOptions;\n        this._cacheName = cacheName;\n        this._timestampModel = new CacheTimestampsModel(cacheName);\n    }\n    /**\n     * Expires entries for the given cache and given criteria.\n     */\n    async expireEntries() {\n        if (this._isRunning) {\n            this._rerunRequested = true;\n            return;\n        }\n        this._isRunning = true;\n        const minTimestamp = this._maxAgeSeconds\n            ? Date.now() - this._maxAgeSeconds * 1000\n            : 0;\n        const urlsExpired = await this._timestampModel.expireEntries(minTimestamp, this._maxEntries);\n        // Delete URLs from the cache\n        const cache = await self.caches.open(this._cacheName);\n        for (const url of urlsExpired) {\n            await cache.delete(url, this._matchOptions);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (urlsExpired.length > 0) {\n                logger.groupCollapsed(`Expired ${urlsExpired.length} ` +\n                    `${urlsExpired.length === 1 ? 'entry' : 'entries'} and removed ` +\n                    `${urlsExpired.length === 1 ? 'it' : 'them'} from the ` +\n                    `'${this._cacheName}' cache.`);\n                logger.log(`Expired the following ${urlsExpired.length === 1 ? 'URL' : 'URLs'}:`);\n                urlsExpired.forEach((url) => logger.log(`    ${url}`));\n                logger.groupEnd();\n            }\n            else {\n                logger.debug(`Cache expiration ran and found no entries to remove.`);\n            }\n        }\n        this._isRunning = false;\n        if (this._rerunRequested) {\n            this._rerunRequested = false;\n            dontWaitFor(this.expireEntries());\n        }\n    }\n    /**\n     * Update the timestamp for the given URL. This ensures the when\n     * removing entries based on maximum entries, most recently used\n     * is accurate or when expiring, the timestamp is up-to-date.\n     *\n     * @param {string} url\n     */\n    async updateTimestamp(url) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(url, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'updateTimestamp',\n                paramName: 'url',\n            });\n        }\n        await this._timestampModel.setTimestamp(url, Date.now());\n    }\n    /**\n     * Can be used to check if a URL has expired or not before it's used.\n     *\n     * This requires a look up from IndexedDB, so can be slow.\n     *\n     * Note: This method will not remove the cached entry, call\n     * `expireEntries()` to remove indexedDB and Cache entries.\n     *\n     * @param {string} url\n     * @return {boolean}\n     */\n    async isURLExpired(url) {\n        if (!this._maxAgeSeconds) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new WorkboxError(`expired-test-without-max-age`, {\n                    methodName: 'isURLExpired',\n                    paramName: 'maxAgeSeconds',\n                });\n            }\n            return false;\n        }\n        else {\n            const timestamp = await this._timestampModel.getTimestamp(url);\n            const expireOlderThan = Date.now() - this._maxAgeSeconds * 1000;\n            return timestamp !== undefined ? timestamp < expireOlderThan : true;\n        }\n    }\n    /**\n     * Removes the IndexedDB object store used to keep track of cache expiration\n     * metadata.\n     */\n    async delete() {\n        // Make sure we don't attempt another rerun if we're called in the middle of\n        // a cache expiration.\n        this._rerunRequested = false;\n        await this._timestampModel.expireEntries(Infinity); // Expires all.\n    }\n}\nexport { CacheExpiration };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { registerQuotaErrorCallback } from 'workbox-core/registerQuotaErrorCallback.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheExpiration } from './CacheExpiration.js';\nimport './_version.js';\n/**\n * This plugin can be used in a `workbox-strategy` to regularly enforce a\n * limit on the age and / or the number of cached requests.\n *\n * It can only be used with `workbox-strategy` instances that have a\n * [custom `cacheName` property set](/web/tools/workbox/guides/configure-workbox#custom_cache_names_in_strategies).\n * In other words, it can't be used to expire entries in strategy that uses the\n * default runtime cache name.\n *\n * Whenever a cached response is used or updated, this plugin will look\n * at the associated cache and remove any old or extra responses.\n *\n * When using `maxAgeSeconds`, responses may be used *once* after expiring\n * because the expiration clean up will not have occurred until *after* the\n * cached response has been used. If the response has a \"Date\" header, then\n * a light weight expiration check is performed and the response will not be\n * used immediately.\n *\n * When using `maxEntries`, the entry least-recently requested will be removed\n * from the cache first.\n *\n * @memberof workbox-expiration\n */\nclass ExpirationPlugin {\n    /**\n     * @param {ExpirationPluginOptions} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     * @param {boolean} [config.purgeOnQuotaError] Whether to opt this cache in to\n     * automatic deletion if the available storage quota has been exceeded.\n     */\n    constructor(config = {}) {\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when a `Response` is about to be returned\n         * from a [Cache](https://developer.mozilla.org/en-US/docs/Web/API/Cache) to\n         * the handler. It allows the `Response` to be inspected for freshness and\n         * prevents it from being used if the `Response`'s `Date` header value is\n         * older than the configured `maxAgeSeconds`.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache the response is in.\n         * @param {Response} options.cachedResponse The `Response` object that's been\n         *     read from a cache and whose freshness should be checked.\n         * @return {Response} Either the `cachedResponse`, if it's\n         *     fresh, or `null` if the `Response` is older than `maxAgeSeconds`.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ event, request, cacheName, cachedResponse, }) => {\n            if (!cachedResponse) {\n                return null;\n            }\n            const isFresh = this._isResponseDateFresh(cachedResponse);\n            // Expire entries to ensure that even if the expiration date has\n            // expired, it'll only be used once.\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            dontWaitFor(cacheExpiration.expireEntries());\n            // Update the metadata for the request URL to the current timestamp,\n            // but don't `await` it as we don't want to block the response.\n            const updateTimestampDone = cacheExpiration.updateTimestamp(request.url);\n            if (event) {\n                try {\n                    event.waitUntil(updateTimestampDone);\n                }\n                catch (error) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // The event may not be a fetch event; only log the URL if it is.\n                        if ('request' in event) {\n                            logger.warn(`Unable to ensure service worker stays alive when ` +\n                                `updating cache entry for ` +\n                                `'${getFriendlyURL(event.request.url)}'.`);\n                        }\n                    }\n                }\n            }\n            return isFresh ? cachedResponse : null;\n        };\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when an entry is added to a cache.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache that was updated.\n         * @param {string} options.request The Request for the cached entry.\n         *\n         * @private\n         */\n        this.cacheDidUpdate = async ({ cacheName, request, }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                assert.isType(cacheName, 'string', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'cacheName',\n                });\n                assert.isInstance(request, Request, {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'request',\n                });\n            }\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            await cacheExpiration.updateTimestamp(request.url);\n            await cacheExpiration.expireEntries();\n        };\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._config = config;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._cacheExpirations = new Map();\n        if (config.purgeOnQuotaError) {\n            registerQuotaErrorCallback(() => this.deleteCacheAndMetadata());\n        }\n    }\n    /**\n     * A simple helper method to return a CacheExpiration instance for a given\n     * cache name.\n     *\n     * @param {string} cacheName\n     * @return {CacheExpiration}\n     *\n     * @private\n     */\n    _getCacheExpiration(cacheName) {\n        if (cacheName === cacheNames.getRuntimeName()) {\n            throw new WorkboxError('expire-custom-caches-only');\n        }\n        let cacheExpiration = this._cacheExpirations.get(cacheName);\n        if (!cacheExpiration) {\n            cacheExpiration = new CacheExpiration(cacheName, this._config);\n            this._cacheExpirations.set(cacheName, cacheExpiration);\n        }\n        return cacheExpiration;\n    }\n    /**\n     * @param {Response} cachedResponse\n     * @return {boolean}\n     *\n     * @private\n     */\n    _isResponseDateFresh(cachedResponse) {\n        if (!this._maxAgeSeconds) {\n            // We aren't expiring by age, so return true, it's fresh\n            return true;\n        }\n        // Check if the 'date' header will suffice a quick expiration check.\n        // See https://github.com/GoogleChromeLabs/sw-toolbox/issues/164 for\n        // discussion.\n        const dateHeaderTimestamp = this._getDateHeaderTimestamp(cachedResponse);\n        if (dateHeaderTimestamp === null) {\n            // Unable to parse date, so assume it's fresh.\n            return true;\n        }\n        // If we have a valid headerTime, then our response is fresh iff the\n        // headerTime plus maxAgeSeconds is greater than the current time.\n        const now = Date.now();\n        return dateHeaderTimestamp >= now - this._maxAgeSeconds * 1000;\n    }\n    /**\n     * This method will extract the data header and parse it into a useful\n     * value.\n     *\n     * @param {Response} cachedResponse\n     * @return {number|null}\n     *\n     * @private\n     */\n    _getDateHeaderTimestamp(cachedResponse) {\n        if (!cachedResponse.headers.has('date')) {\n            return null;\n        }\n        const dateHeader = cachedResponse.headers.get('date');\n        const parsedDate = new Date(dateHeader);\n        const headerTime = parsedDate.getTime();\n        // If the Date header was invalid for some reason, parsedDate.getTime()\n        // will return NaN.\n        if (isNaN(headerTime)) {\n            return null;\n        }\n        return headerTime;\n    }\n    /**\n     * This is a helper method that performs two operations:\n     *\n     * - Deletes *all* the underlying Cache instances associated with this plugin\n     * instance, by calling caches.delete() on your behalf.\n     * - Deletes the metadata from IndexedDB used to keep track of expiration\n     * details for each Cache instance.\n     *\n     * When using cache expiration, calling this method is preferable to calling\n     * `caches.delete()` directly, since this will ensure that the IndexedDB\n     * metadata is also cleanly removed and open IndexedDB instances are deleted.\n     *\n     * Note that if you're *not* using cache expiration for a given cache, calling\n     * `caches.delete()` and passing in the cache's name should be sufficient.\n     * There is no Workbox-specific method needed for cleanup in that case.\n     */\n    async deleteCacheAndMetadata() {\n        // Do this one at a time instead of all at once via `Promise.all()` to\n        // reduce the chance of inconsistency if a promise rejects.\n        for (const [cacheName, cacheExpiration] of this._cacheExpirations) {\n            await self.caches.delete(cacheName);\n            await cacheExpiration.delete();\n        }\n        // Reset this._cacheExpirations to its initial state.\n        this._cacheExpirations = new Map();\n    }\n}\nexport { ExpirationPlugin };\n"], "names": ["instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "getIdbProxyableTypes", "IDBDatabase", "IDBObjectStore", "IDBIndex", "IDBCursor", "IDBTransaction", "getCursorAdvanceMethods", "prototype", "advance", "continue", "continuePrimaryKey", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "promisifyRequest", "request", "promise", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "success", "error", "wrap", "result", "addEventListener", "then", "value", "set", "catch", "cacheDonePromiseForTransaction", "tx", "has", "done", "complete", "DOMException", "idbProxyTraps", "get", "target", "prop", "receiver", "objectStoreNames", "undefined", "objectStore", "replaceTraps", "callback", "wrapFunction", "func", "transaction", "storeNames", "args", "call", "unwrap", "sort", "includes", "apply", "transformCachableValue", "Proxy", "IDBRequest", "newValue", "openDB", "name", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "deleteDB", "deleteDatabase", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "replace", "useIndex", "isWrite", "method", "storeName", "store", "index", "shift", "all", "oldTraps", "_extends", "self", "_", "e", "DB_NAME", "CACHE_OBJECT_STORE", "normalizeURL", "unNormalizedUrl", "url", "URL", "location", "href", "hash", "CacheTimestampsModel", "constructor", "cacheName", "_db", "_cacheName", "_upgradeDb", "objStore", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "_upgradeDbAndDeleteOldDbs", "setTimestamp", "timestamp", "entry", "id", "_getId", "getDb", "durability", "put", "getTimestamp", "expireEntries", "minTimestamp", "maxCount", "cursor", "openCursor", "entriesToDelete", "entriesNotDeletedCount", "push", "urlsDeleted", "delete", "bind", "CacheExpiration", "config", "_isRunning", "_rerunRequested", "assert", "isType", "moduleName", "className", "funcName", "paramName", "maxEntries", "maxAgeSeconds", "WorkboxError", "_maxEntries", "_maxAgeSeconds", "_matchOptions", "matchOptions", "_timestampModel", "Date", "now", "urlsExpired", "cache", "caches", "length", "logger", "groupCollapsed", "log", "for<PERSON>ach", "groupEnd", "debug", "dontWait<PERSON>or", "updateTimestamp", "isURLExpired", "methodName", "expire<PERSON><PERSON><PERSON><PERSON>", "Infinity", "ExpirationPlugin", "cachedResponseWillBeUsed", "cachedResponse", "isFresh", "_isResponseDateFresh", "cacheExpiration", "_getCacheExpiration", "updateTimestampDone", "waitUntil", "warn", "getFriendlyURL", "cacheDidUpdate", "isInstance", "Request", "_config", "_cacheExpirations", "purgeOnQuotaError", "registerQuotaErrorCallback", "deleteCacheAndMetadata", "cacheNames", "getRuntimeName", "dateHeaderTimestamp", "_getDateHeaderTimestamp", "headers", "<PERSON><PERSON><PERSON><PERSON>", "parsedDate", "headerTime", "getTime", "isNaN"], "mappings": ";;;;;;;;;;;;;;;;;;;EAAA,MAAMA,aAAa,GAAGA,CAACC,MAAM,EAAEC,YAAY,KAAKA,YAAY,CAACC,IAAI,CAAEC,CAAC,IAAKH,MAAM,YAAYG,CAAC,CAAC,CAAA;EAE7F,IAAIC,iBAAiB,CAAA;EACrB,IAAIC,oBAAoB,CAAA;EACxB;EACA,SAASC,oBAAoBA,GAAG;EAC5B,EAAA,OAAQF,iBAAiB,KACpBA,iBAAiB,GAAG,CACjBG,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,cAAc,CACjB,CAAC,CAAA;EACV,CAAA;EACA;EACA,SAASC,uBAAuBA,GAAG;IAC/B,OAAQP,oBAAoB,KACvBA,oBAAoB,GAAG,CACpBK,SAAS,CAACG,SAAS,CAACC,OAAO,EAC3BJ,SAAS,CAACG,SAAS,CAACE,QAAQ,EAC5BL,SAAS,CAACG,SAAS,CAACG,kBAAkB,CACzC,CAAC,CAAA;EACV,CAAA;EACA,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,EAAE,CAAA;EACtC,MAAMC,kBAAkB,GAAG,IAAID,OAAO,EAAE,CAAA;EACxC,MAAME,wBAAwB,GAAG,IAAIF,OAAO,EAAE,CAAA;EAC9C,MAAMG,cAAc,GAAG,IAAIH,OAAO,EAAE,CAAA;EACpC,MAAMI,qBAAqB,GAAG,IAAIJ,OAAO,EAAE,CAAA;EAC3C,SAASK,gBAAgBA,CAACC,OAAO,EAAE;IAC/B,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC7C,MAAMC,QAAQ,GAAGA,MAAM;EACnBL,MAAAA,OAAO,CAACM,mBAAmB,CAAC,SAAS,EAAEC,OAAO,CAAC,CAAA;EAC/CP,MAAAA,OAAO,CAACM,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC,CAAA;OAC9C,CAAA;MACD,MAAMD,OAAO,GAAGA,MAAM;EAClBJ,MAAAA,OAAO,CAACM,IAAI,CAACT,OAAO,CAACU,MAAM,CAAC,CAAC,CAAA;EAC7BL,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;MACD,MAAMG,KAAK,GAAGA,MAAM;EAChBJ,MAAAA,MAAM,CAACJ,OAAO,CAACQ,KAAK,CAAC,CAAA;EACrBH,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;EACDL,IAAAA,OAAO,CAACW,gBAAgB,CAAC,SAAS,EAAEJ,OAAO,CAAC,CAAA;EAC5CP,IAAAA,OAAO,CAACW,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAA;EAC5C,GAAC,CAAC,CAAA;EACFP,EAAAA,OAAO,CACFW,IAAI,CAAEC,KAAK,IAAK;EACjB;EACA;MACA,IAAIA,KAAK,YAAY3B,SAAS,EAAE;EAC5BO,MAAAA,gBAAgB,CAACqB,GAAG,CAACD,KAAK,EAAEb,OAAO,CAAC,CAAA;EACxC,KAAA;EACA;EACJ,GAAC,CAAC,CACGe,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;EACrB;EACA;EACAjB,EAAAA,qBAAqB,CAACgB,GAAG,CAACb,OAAO,EAAED,OAAO,CAAC,CAAA;EAC3C,EAAA,OAAOC,OAAO,CAAA;EAClB,CAAA;EACA,SAASe,8BAA8BA,CAACC,EAAE,EAAE;EACxC;EACA,EAAA,IAAItB,kBAAkB,CAACuB,GAAG,CAACD,EAAE,CAAC,EAC1B,OAAA;IACJ,MAAME,IAAI,GAAG,IAAIjB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC1C,MAAMC,QAAQ,GAAGA,MAAM;EACnBY,MAAAA,EAAE,CAACX,mBAAmB,CAAC,UAAU,EAAEc,QAAQ,CAAC,CAAA;EAC5CH,MAAAA,EAAE,CAACX,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC,CAAA;EACtCS,MAAAA,EAAE,CAACX,mBAAmB,CAAC,OAAO,EAAEE,KAAK,CAAC,CAAA;OACzC,CAAA;MACD,MAAMY,QAAQ,GAAGA,MAAM;EACnBjB,MAAAA,OAAO,EAAE,CAAA;EACTE,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;MACD,MAAMG,KAAK,GAAGA,MAAM;EAChBJ,MAAAA,MAAM,CAACa,EAAE,CAACT,KAAK,IAAI,IAAIa,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;EAChEhB,MAAAA,QAAQ,EAAE,CAAA;OACb,CAAA;EACDY,IAAAA,EAAE,CAACN,gBAAgB,CAAC,UAAU,EAAES,QAAQ,CAAC,CAAA;EACzCH,IAAAA,EAAE,CAACN,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAA;EACnCS,IAAAA,EAAE,CAACN,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC,CAAA;EACvC,GAAC,CAAC,CAAA;EACF;EACAb,EAAAA,kBAAkB,CAACmB,GAAG,CAACG,EAAE,EAAEE,IAAI,CAAC,CAAA;EACpC,CAAA;EACA,IAAIG,aAAa,GAAG;EAChBC,EAAAA,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;MACxB,IAAIF,MAAM,YAAYrC,cAAc,EAAE;EAClC;QACA,IAAIsC,IAAI,KAAK,MAAM,EACf,OAAO9B,kBAAkB,CAAC4B,GAAG,CAACC,MAAM,CAAC,CAAA;EACzC;QACA,IAAIC,IAAI,KAAK,kBAAkB,EAAE;UAC7B,OAAOD,MAAM,CAACG,gBAAgB,IAAI/B,wBAAwB,CAAC2B,GAAG,CAACC,MAAM,CAAC,CAAA;EAC1E,OAAA;EACA;QACA,IAAIC,IAAI,KAAK,OAAO,EAAE;EAClB,QAAA,OAAOC,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,GAC7BC,SAAS,GACTF,QAAQ,CAACG,WAAW,CAACH,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAA;EAC5D,OAAA;EACJ,KAAA;EACA;EACA,IAAA,OAAOlB,IAAI,CAACe,MAAM,CAACC,IAAI,CAAC,CAAC,CAAA;KAC5B;EACDX,EAAAA,GAAGA,CAACU,MAAM,EAAEC,IAAI,EAAEZ,KAAK,EAAE;EACrBW,IAAAA,MAAM,CAACC,IAAI,CAAC,GAAGZ,KAAK,CAAA;EACpB,IAAA,OAAO,IAAI,CAAA;KACd;EACDK,EAAAA,GAAGA,CAACM,MAAM,EAAEC,IAAI,EAAE;EACd,IAAA,IAAID,MAAM,YAAYrC,cAAc,KAC/BsC,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,CAAC,EAAE;EACvC,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;MACA,OAAOA,IAAI,IAAID,MAAM,CAAA;EACzB,GAAA;EACJ,CAAC,CAAA;EACD,SAASM,YAAYA,CAACC,QAAQ,EAAE;EAC5BT,EAAAA,aAAa,GAAGS,QAAQ,CAACT,aAAa,CAAC,CAAA;EAC3C,CAAA;EACA,SAASU,YAAYA,CAACC,IAAI,EAAE;EACxB;EACA;EACA;EACA,EAAA,IAAIA,IAAI,KAAKlD,WAAW,CAACM,SAAS,CAAC6C,WAAW,IAC1C,EAAE,kBAAkB,IAAI/C,cAAc,CAACE,SAAS,CAAC,EAAE;EACnD,IAAA,OAAO,UAAU8C,UAAU,EAAE,GAAGC,IAAI,EAAE;EAClC,MAAA,MAAMnB,EAAE,GAAGgB,IAAI,CAACI,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,EAAEH,UAAU,EAAE,GAAGC,IAAI,CAAC,CAAA;EACvDxC,MAAAA,wBAAwB,CAACkB,GAAG,CAACG,EAAE,EAAEkB,UAAU,CAACI,IAAI,GAAGJ,UAAU,CAACI,IAAI,EAAE,GAAG,CAACJ,UAAU,CAAC,CAAC,CAAA;QACpF,OAAO1B,IAAI,CAACQ,EAAE,CAAC,CAAA;OAClB,CAAA;EACL,GAAA;EACA;EACA;EACA;EACA;EACA;IACA,IAAI7B,uBAAuB,EAAE,CAACoD,QAAQ,CAACP,IAAI,CAAC,EAAE;MAC1C,OAAO,UAAU,GAAGG,IAAI,EAAE;EACtB;EACA;QACAH,IAAI,CAACQ,KAAK,CAACH,MAAM,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC,CAAA;QAC9B,OAAO3B,IAAI,CAAChB,gBAAgB,CAAC8B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;OAC1C,CAAA;EACL,GAAA;IACA,OAAO,UAAU,GAAGa,IAAI,EAAE;EACtB;EACA;EACA,IAAA,OAAO3B,IAAI,CAACwB,IAAI,CAACQ,KAAK,CAACH,MAAM,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAA;KAC9C,CAAA;EACL,CAAA;EACA,SAASM,sBAAsBA,CAAC7B,KAAK,EAAE;IACnC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAC3B,OAAOmB,YAAY,CAACnB,KAAK,CAAC,CAAA;EAC9B;EACA;EACA,EAAA,IAAIA,KAAK,YAAY1B,cAAc,EAC/B6B,8BAA8B,CAACH,KAAK,CAAC,CAAA;EACzC,EAAA,IAAItC,aAAa,CAACsC,KAAK,EAAE/B,oBAAoB,EAAE,CAAC,EAC5C,OAAO,IAAI6D,KAAK,CAAC9B,KAAK,EAAES,aAAa,CAAC,CAAA;EAC1C;EACA,EAAA,OAAOT,KAAK,CAAA;EAChB,CAAA;EACA,SAASJ,IAAIA,CAACI,KAAK,EAAE;EACjB;EACA;IACA,IAAIA,KAAK,YAAY+B,UAAU,EAC3B,OAAO7C,gBAAgB,CAACc,KAAK,CAAC,CAAA;EAClC;EACA;EACA,EAAA,IAAIhB,cAAc,CAACqB,GAAG,CAACL,KAAK,CAAC,EACzB,OAAOhB,cAAc,CAAC0B,GAAG,CAACV,KAAK,CAAC,CAAA;EACpC,EAAA,MAAMgC,QAAQ,GAAGH,sBAAsB,CAAC7B,KAAK,CAAC,CAAA;EAC9C;EACA;IACA,IAAIgC,QAAQ,KAAKhC,KAAK,EAAE;EACpBhB,IAAAA,cAAc,CAACiB,GAAG,CAACD,KAAK,EAAEgC,QAAQ,CAAC,CAAA;EACnC/C,IAAAA,qBAAqB,CAACgB,GAAG,CAAC+B,QAAQ,EAAEhC,KAAK,CAAC,CAAA;EAC9C,GAAA;EACA,EAAA,OAAOgC,QAAQ,CAAA;EACnB,CAAA;EACA,MAAMP,MAAM,GAAIzB,KAAK,IAAKf,qBAAqB,CAACyB,GAAG,CAACV,KAAK,CAAC;;ECnL1D;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASiC,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAAEC,OAAO;IAAEC,OAAO;IAAEC,QAAQ;EAAEC,EAAAA,UAAAA;EAAW,CAAC,GAAG,EAAE,EAAE;IAC5E,MAAMpD,OAAO,GAAGqD,SAAS,CAACC,IAAI,CAACP,IAAI,EAAEC,OAAO,CAAC,CAAA;EAC7C,EAAA,MAAMO,WAAW,GAAG9C,IAAI,CAACT,OAAO,CAAC,CAAA;EACjC,EAAA,IAAIkD,OAAO,EAAE;EACTlD,IAAAA,OAAO,CAACW,gBAAgB,CAAC,eAAe,EAAG6C,KAAK,IAAK;QACjDN,OAAO,CAACzC,IAAI,CAACT,OAAO,CAACU,MAAM,CAAC,EAAE8C,KAAK,CAACC,UAAU,EAAED,KAAK,CAACE,UAAU,EAAEjD,IAAI,CAACT,OAAO,CAACkC,WAAW,CAAC,CAAC,CAAA;EAChG,KAAC,CAAC,CAAA;EACN,GAAA;EACA,EAAA,IAAIe,OAAO,EACPjD,OAAO,CAACW,gBAAgB,CAAC,SAAS,EAAE,MAAMsC,OAAO,EAAE,CAAC,CAAA;EACxDM,EAAAA,WAAW,CACN3C,IAAI,CAAE+C,EAAE,IAAK;EACd,IAAA,IAAIP,UAAU,EACVO,EAAE,CAAChD,gBAAgB,CAAC,OAAO,EAAE,MAAMyC,UAAU,EAAE,CAAC,CAAA;EACpD,IAAA,IAAID,QAAQ,EACRQ,EAAE,CAAChD,gBAAgB,CAAC,eAAe,EAAE,MAAMwC,QAAQ,EAAE,CAAC,CAAA;EAC9D,GAAC,CAAC,CACGpC,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;EACrB,EAAA,OAAOwC,WAAW,CAAA;EACtB,CAAA;EACA;EACA;EACA;EACA;EACA;EACA,SAASK,QAAQA,CAACb,IAAI,EAAE;EAAEE,EAAAA,OAAAA;EAAQ,CAAC,GAAG,EAAE,EAAE;EACtC,EAAA,MAAMjD,OAAO,GAAGqD,SAAS,CAACQ,cAAc,CAACd,IAAI,CAAC,CAAA;EAC9C,EAAA,IAAIE,OAAO,EACPjD,OAAO,CAACW,gBAAgB,CAAC,SAAS,EAAE,MAAMsC,OAAO,EAAE,CAAC,CAAA;IACxD,OAAOxC,IAAI,CAACT,OAAO,CAAC,CAACY,IAAI,CAAC,MAAMgB,SAAS,CAAC,CAAA;EAC9C,CAAA;EAEA,MAAMkC,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAA;EACtE,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;EACtD,MAAMC,aAAa,GAAG,IAAIC,GAAG,EAAE,CAAA;EAC/B,SAASC,SAASA,CAAC1C,MAAM,EAAEC,IAAI,EAAE;EAC7B,EAAA,IAAI,EAAED,MAAM,YAAYzC,WAAW,IAC/B,EAAE0C,IAAI,IAAID,MAAM,CAAC,IACjB,OAAOC,IAAI,KAAK,QAAQ,CAAC,EAAE;EAC3B,IAAA,OAAA;EACJ,GAAA;EACA,EAAA,IAAIuC,aAAa,CAACzC,GAAG,CAACE,IAAI,CAAC,EACvB,OAAOuC,aAAa,CAACzC,GAAG,CAACE,IAAI,CAAC,CAAA;IAClC,MAAM0C,cAAc,GAAG1C,IAAI,CAAC2C,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;EACrD,EAAA,MAAMC,QAAQ,GAAG5C,IAAI,KAAK0C,cAAc,CAAA;EACxC,EAAA,MAAMG,OAAO,GAAGP,YAAY,CAACvB,QAAQ,CAAC2B,cAAc,CAAC,CAAA;EACrD,EAAA;EACA;IACA,EAAEA,cAAc,IAAI,CAACE,QAAQ,GAAGpF,QAAQ,GAAGD,cAAc,EAAEK,SAAS,CAAC,IACjE,EAAEiF,OAAO,IAAIR,WAAW,CAACtB,QAAQ,CAAC2B,cAAc,CAAC,CAAC,EAAE;EACpD,IAAA,OAAA;EACJ,GAAA;IACA,MAAMI,MAAM,GAAG,gBAAgBC,SAAS,EAAE,GAAGpC,IAAI,EAAE;EAC/C;EACA,IAAA,MAAMnB,EAAE,GAAG,IAAI,CAACiB,WAAW,CAACsC,SAAS,EAAEF,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC,CAAA;EAC1E,IAAA,IAAI9C,MAAM,GAAGP,EAAE,CAACwD,KAAK,CAAA;EACrB,IAAA,IAAIJ,QAAQ,EACR7C,MAAM,GAAGA,MAAM,CAACkD,KAAK,CAACtC,IAAI,CAACuC,KAAK,EAAE,CAAC,CAAA;EACvC;EACA;EACA;EACA;EACA;MACA,OAAO,CAAC,MAAMzE,OAAO,CAAC0E,GAAG,CAAC,CACtBpD,MAAM,CAAC2C,cAAc,CAAC,CAAC,GAAG/B,IAAI,CAAC,EAC/BkC,OAAO,IAAIrD,EAAE,CAACE,IAAI,CACrB,CAAC,EAAE,CAAC,CAAC,CAAA;KACT,CAAA;EACD6C,EAAAA,aAAa,CAAClD,GAAG,CAACW,IAAI,EAAE8C,MAAM,CAAC,CAAA;EAC/B,EAAA,OAAOA,MAAM,CAAA;EACjB,CAAA;EACAzC,YAAY,CAAE+C,QAAQ,IAAAC,QAAA,KACfD,QAAQ,EAAA;IACXtD,GAAG,EAAEA,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,KAAKwC,SAAS,CAAC1C,MAAM,EAAEC,IAAI,CAAC,IAAIoD,QAAQ,CAACtD,GAAG,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;IAChGR,GAAG,EAAEA,CAACM,MAAM,EAAEC,IAAI,KAAK,CAAC,CAACyC,SAAS,CAAC1C,MAAM,EAAEC,IAAI,CAAC,IAAIoD,QAAQ,CAAC3D,GAAG,CAACM,MAAM,EAAEC,IAAI,CAAA;EAAC,CAAA,CAChF,CAAC;;ECpFH;EACA,IAAI;EACAsD,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;EAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ECLV;EACA;AACA;EACA;EACA;EACA;EACA;EAGA,MAAMC,OAAO,GAAG,oBAAoB,CAAA;EACpC,MAAMC,kBAAkB,GAAG,eAAe,CAAA;EAC1C,MAAMC,YAAY,GAAIC,eAAe,IAAK;IACtC,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACF,eAAe,EAAEG,QAAQ,CAACC,IAAI,CAAC,CAAA;IACnDH,GAAG,CAACI,IAAI,GAAG,EAAE,CAAA;IACb,OAAOJ,GAAG,CAACG,IAAI,CAAA;EACnB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACA,MAAME,oBAAoB,CAAC;EACvB;EACJ;EACA;EACA;EACA;EACA;IACIC,WAAWA,CAACC,SAAS,EAAE;MACnB,IAAI,CAACC,GAAG,GAAG,IAAI,CAAA;MACf,IAAI,CAACC,UAAU,GAAGF,SAAS,CAAA;EAC/B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IACIG,UAAUA,CAACrC,EAAE,EAAE;EACX;EACA;EACA;EACA;EACA,IAAA,MAAMsC,QAAQ,GAAGtC,EAAE,CAACuC,iBAAiB,CAACf,kBAAkB,EAAE;EAAEgB,MAAAA,OAAO,EAAE,IAAA;EAAK,KAAC,CAAC,CAAA;EAC5E;EACA;EACA;EACAF,IAAAA,QAAQ,CAACG,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE;EAAEC,MAAAA,MAAM,EAAE,KAAA;EAAM,KAAC,CAAC,CAAA;EACjEJ,IAAAA,QAAQ,CAACG,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE;EAAEC,MAAAA,MAAM,EAAE,KAAA;EAAM,KAAC,CAAC,CAAA;EACrE,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IACIC,yBAAyBA,CAAC3C,EAAE,EAAE;EAC1B,IAAA,IAAI,CAACqC,UAAU,CAACrC,EAAE,CAAC,CAAA;MACnB,IAAI,IAAI,CAACoC,UAAU,EAAE;EACjB,MAAA,KAAKnC,QAAQ,CAAC,IAAI,CAACmC,UAAU,CAAC,CAAA;EAClC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,MAAMQ,YAAYA,CAACjB,GAAG,EAAEkB,SAAS,EAAE;EAC/BlB,IAAAA,GAAG,GAAGF,YAAY,CAACE,GAAG,CAAC,CAAA;EACvB,IAAA,MAAMmB,KAAK,GAAG;QACVnB,GAAG;QACHkB,SAAS;QACTX,SAAS,EAAE,IAAI,CAACE,UAAU;EAC1B;EACA;EACA;EACAW,MAAAA,EAAE,EAAE,IAAI,CAACC,MAAM,CAACrB,GAAG,CAAA;OACtB,CAAA;EACD,IAAA,MAAM3B,EAAE,GAAG,MAAM,IAAI,CAACiD,KAAK,EAAE,CAAA;MAC7B,MAAM3F,EAAE,GAAG0C,EAAE,CAACzB,WAAW,CAACiD,kBAAkB,EAAE,WAAW,EAAE;EACvD0B,MAAAA,UAAU,EAAE,SAAA;EAChB,KAAC,CAAC,CAAA;EACF,IAAA,MAAM5F,EAAE,CAACwD,KAAK,CAACqC,GAAG,CAACL,KAAK,CAAC,CAAA;MACzB,MAAMxF,EAAE,CAACE,IAAI,CAAA;EACjB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAM4F,YAAYA,CAACzB,GAAG,EAAE;EACpB,IAAA,MAAM3B,EAAE,GAAG,MAAM,IAAI,CAACiD,KAAK,EAAE,CAAA;EAC7B,IAAA,MAAMH,KAAK,GAAG,MAAM9C,EAAE,CAACpC,GAAG,CAAC4D,kBAAkB,EAAE,IAAI,CAACwB,MAAM,CAACrB,GAAG,CAAC,CAAC,CAAA;EAChE,IAAA,OAAOmB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACD,SAAS,CAAA;EACxE,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI,EAAA,MAAMQ,aAAaA,CAACC,YAAY,EAAEC,QAAQ,EAAE;EACxC,IAAA,MAAMvD,EAAE,GAAG,MAAM,IAAI,CAACiD,KAAK,EAAE,CAAA;MAC7B,IAAIO,MAAM,GAAG,MAAMxD,EAAE,CAChBzB,WAAW,CAACiD,kBAAkB,CAAC,CAC/BV,KAAK,CAACC,KAAK,CAAC,WAAW,CAAC,CACxB0C,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;MAC7B,MAAMC,eAAe,GAAG,EAAE,CAAA;MAC1B,IAAIC,sBAAsB,GAAG,CAAC,CAAA;EAC9B,IAAA,OAAOH,MAAM,EAAE;EACX,MAAA,MAAMzG,MAAM,GAAGyG,MAAM,CAACtG,KAAK,CAAA;EAC3B;EACA;EACA,MAAA,IAAIH,MAAM,CAACmF,SAAS,KAAK,IAAI,CAACE,UAAU,EAAE;EACtC;EACA;EACA,QAAA,IAAKkB,YAAY,IAAIvG,MAAM,CAAC8F,SAAS,GAAGS,YAAY,IAC/CC,QAAQ,IAAII,sBAAsB,IAAIJ,QAAS,EAAE;EAClD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG,UAAAA,eAAe,CAACE,IAAI,CAACJ,MAAM,CAACtG,KAAK,CAAC,CAAA;EACtC,SAAC,MACI;EACDyG,UAAAA,sBAAsB,EAAE,CAAA;EAC5B,SAAA;EACJ,OAAA;EACAH,MAAAA,MAAM,GAAG,MAAMA,MAAM,CAAC5H,QAAQ,EAAE,CAAA;EACpC,KAAA;EACA;EACA;EACA;EACA;MACA,MAAMiI,WAAW,GAAG,EAAE,CAAA;EACtB,IAAA,KAAK,MAAMf,KAAK,IAAIY,eAAe,EAAE;QACjC,MAAM1D,EAAE,CAAC8D,MAAM,CAACtC,kBAAkB,EAAEsB,KAAK,CAACC,EAAE,CAAC,CAAA;EAC7Cc,MAAAA,WAAW,CAACD,IAAI,CAACd,KAAK,CAACnB,GAAG,CAAC,CAAA;EAC/B,KAAA;EACA,IAAA,OAAOkC,WAAW,CAAA;EACtB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;IACIb,MAAMA,CAACrB,GAAG,EAAE;EACR;EACA;EACA;MACA,OAAO,IAAI,CAACS,UAAU,GAAG,GAAG,GAAGX,YAAY,CAACE,GAAG,CAAC,CAAA;EACpD,GAAA;EACA;EACJ;EACA;EACA;EACA;IACI,MAAMsB,KAAKA,GAAG;EACV,IAAA,IAAI,CAAC,IAAI,CAACd,GAAG,EAAE;QACX,IAAI,CAACA,GAAG,GAAG,MAAMhD,MAAM,CAACoC,OAAO,EAAE,CAAC,EAAE;EAChChC,QAAAA,OAAO,EAAE,IAAI,CAACoD,yBAAyB,CAACoB,IAAI,CAAC,IAAI,CAAA;EACrD,OAAC,CAAC,CAAA;EACN,KAAA;MACA,OAAO,IAAI,CAAC5B,GAAG,CAAA;EACnB,GAAA;EACJ;;ECvLA;EACA;AACA;EACA;EACA;EACA;EACA;EAOA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM6B,eAAe,CAAC;EAClB;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACI/B,EAAAA,WAAWA,CAACC,SAAS,EAAE+B,MAAM,GAAG,EAAE,EAAE;MAChC,IAAI,CAACC,UAAU,GAAG,KAAK,CAAA;MACvB,IAAI,CAACC,eAAe,GAAG,KAAK,CAAA;EAC5B,IAA2C;EACvCC,MAAAA,gBAAM,CAACC,MAAM,CAACnC,SAAS,EAAE,QAAQ,EAAE;EAC/BoC,QAAAA,UAAU,EAAE,oBAAoB;EAChCC,QAAAA,SAAS,EAAE,iBAAiB;EAC5BC,QAAAA,QAAQ,EAAE,aAAa;EACvBC,QAAAA,SAAS,EAAE,WAAA;EACf,OAAC,CAAC,CAAA;QACF,IAAI,EAAER,MAAM,CAACS,UAAU,IAAIT,MAAM,CAACU,aAAa,CAAC,EAAE;EAC9C,QAAA,MAAM,IAAIC,4BAAY,CAAC,6BAA6B,EAAE;EAClDN,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,iBAAiB;EAC5BC,UAAAA,QAAQ,EAAE,aAAA;EACd,SAAC,CAAC,CAAA;EACN,OAAA;QACA,IAAIP,MAAM,CAACS,UAAU,EAAE;UACnBN,gBAAM,CAACC,MAAM,CAACJ,MAAM,CAACS,UAAU,EAAE,QAAQ,EAAE;EACvCJ,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,iBAAiB;EAC5BC,UAAAA,QAAQ,EAAE,aAAa;EACvBC,UAAAA,SAAS,EAAE,mBAAA;EACf,SAAC,CAAC,CAAA;EACN,OAAA;QACA,IAAIR,MAAM,CAACU,aAAa,EAAE;UACtBP,gBAAM,CAACC,MAAM,CAACJ,MAAM,CAACU,aAAa,EAAE,QAAQ,EAAE;EAC1CL,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,iBAAiB;EAC5BC,UAAAA,QAAQ,EAAE,aAAa;EACvBC,UAAAA,SAAS,EAAE,sBAAA;EACf,SAAC,CAAC,CAAA;EACN,OAAA;EACJ,KAAA;EACA,IAAA,IAAI,CAACI,WAAW,GAAGZ,MAAM,CAACS,UAAU,CAAA;EACpC,IAAA,IAAI,CAACI,cAAc,GAAGb,MAAM,CAACU,aAAa,CAAA;EAC1C,IAAA,IAAI,CAACI,aAAa,GAAGd,MAAM,CAACe,YAAY,CAAA;MACxC,IAAI,CAAC5C,UAAU,GAAGF,SAAS,CAAA;EAC3B,IAAA,IAAI,CAAC+C,eAAe,GAAG,IAAIjD,oBAAoB,CAACE,SAAS,CAAC,CAAA;EAC9D,GAAA;EACA;EACJ;EACA;IACI,MAAMmB,aAAaA,GAAG;MAClB,IAAI,IAAI,CAACa,UAAU,EAAE;QACjB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAA;EAC3B,MAAA,OAAA;EACJ,KAAA;MACA,IAAI,CAACD,UAAU,GAAG,IAAI,CAAA;EACtB,IAAA,MAAMZ,YAAY,GAAG,IAAI,CAACwB,cAAc,GAClCI,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,GACvC,CAAC,CAAA;EACP,IAAA,MAAMM,WAAW,GAAG,MAAM,IAAI,CAACH,eAAe,CAAC5B,aAAa,CAACC,YAAY,EAAE,IAAI,CAACuB,WAAW,CAAC,CAAA;EAC5F;EACA,IAAA,MAAMQ,KAAK,GAAG,MAAMjE,IAAI,CAACkE,MAAM,CAAC3F,IAAI,CAAC,IAAI,CAACyC,UAAU,CAAC,CAAA;EACrD,IAAA,KAAK,MAAMT,GAAG,IAAIyD,WAAW,EAAE;QAC3B,MAAMC,KAAK,CAACvB,MAAM,CAACnC,GAAG,EAAE,IAAI,CAACoD,aAAa,CAAC,CAAA;EAC/C,KAAA;EACA,IAA2C;EACvC,MAAA,IAAIK,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;EACxBC,QAAAA,gBAAM,CAACC,cAAc,CAAE,CAAUL,QAAAA,EAAAA,WAAW,CAACG,MAAO,CAAA,CAAA,CAAE,GACjD,CAAA,EAAEH,WAAW,CAACG,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAU,CAAc,aAAA,CAAA,GAC/D,GAAEH,WAAW,CAACG,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,MAAO,YAAW,GACtD,CAAA,CAAA,EAAG,IAAI,CAACnD,UAAW,UAAS,CAAC,CAAA;EAClCoD,QAAAA,gBAAM,CAACE,GAAG,CAAE,CAAA,sBAAA,EAAwBN,WAAW,CAACG,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,MAAO,GAAE,CAAC,CAAA;EACjFH,QAAAA,WAAW,CAACO,OAAO,CAAEhE,GAAG,IAAK6D,gBAAM,CAACE,GAAG,CAAE,CAAA,IAAA,EAAM/D,GAAI,CAAA,CAAC,CAAC,CAAC,CAAA;UACtD6D,gBAAM,CAACI,QAAQ,EAAE,CAAA;EACrB,OAAC,MACI;EACDJ,QAAAA,gBAAM,CAACK,KAAK,CAAE,CAAA,oDAAA,CAAqD,CAAC,CAAA;EACxE,OAAA;EACJ,KAAA;MACA,IAAI,CAAC3B,UAAU,GAAG,KAAK,CAAA;MACvB,IAAI,IAAI,CAACC,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,GAAG,KAAK,CAAA;EAC5B2B,MAAAA,0BAAW,CAAC,IAAI,CAACzC,aAAa,EAAE,CAAC,CAAA;EACrC,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;IACI,MAAM0C,eAAeA,CAACpE,GAAG,EAAE;EACvB,IAA2C;EACvCyC,MAAAA,gBAAM,CAACC,MAAM,CAAC1C,GAAG,EAAE,QAAQ,EAAE;EACzB2C,QAAAA,UAAU,EAAE,oBAAoB;EAChCC,QAAAA,SAAS,EAAE,iBAAiB;EAC5BC,QAAAA,QAAQ,EAAE,iBAAiB;EAC3BC,QAAAA,SAAS,EAAE,KAAA;EACf,OAAC,CAAC,CAAA;EACN,KAAA;EACA,IAAA,MAAM,IAAI,CAACQ,eAAe,CAACrC,YAAY,CAACjB,GAAG,EAAEuD,IAAI,CAACC,GAAG,EAAE,CAAC,CAAA;EAC5D,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAMa,YAAYA,CAACrE,GAAG,EAAE;EACpB,IAAA,IAAI,CAAC,IAAI,CAACmD,cAAc,EAAE;EACtB,MAA2C;EACvC,QAAA,MAAM,IAAIF,4BAAY,CAAE,CAAA,4BAAA,CAA6B,EAAE;EACnDqB,UAAAA,UAAU,EAAE,cAAc;EAC1BxB,UAAAA,SAAS,EAAE,eAAA;EACf,SAAC,CAAC,CAAA;EACN,OAAA;EAEJ,KAAC,MACI;QACD,MAAM5B,SAAS,GAAG,MAAM,IAAI,CAACoC,eAAe,CAAC7B,YAAY,CAACzB,GAAG,CAAC,CAAA;EAC9D,MAAA,MAAMuE,eAAe,GAAGhB,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,CAAA;QAC/D,OAAOjC,SAAS,KAAK5E,SAAS,GAAG4E,SAAS,GAAGqD,eAAe,GAAG,IAAI,CAAA;EACvE,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;IACI,MAAMpC,MAAMA,GAAG;EACX;EACA;MACA,IAAI,CAACK,eAAe,GAAG,KAAK,CAAA;MAC5B,MAAM,IAAI,CAACc,eAAe,CAAC5B,aAAa,CAAC8C,QAAQ,CAAC,CAAC;EACvD,GAAA;EACJ;;ECvKA;EACA;AACA;EACA;EACA;EACA;EACA;EAUA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,gBAAgB,CAAC;EACnB;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACInE,EAAAA,WAAWA,CAACgC,MAAM,GAAG,EAAE,EAAE;EACrB;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACQ,IAAI,CAACoC,wBAAwB,GAAG,OAAO;QAAExG,KAAK;QAAExD,OAAO;QAAE6F,SAAS;EAAEoE,MAAAA,cAAAA;EAAgB,KAAC,KAAK;QACtF,IAAI,CAACA,cAAc,EAAE;EACjB,QAAA,OAAO,IAAI,CAAA;EACf,OAAA;EACA,MAAA,MAAMC,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACF,cAAc,CAAC,CAAA;EACzD;EACA;EACA,MAAA,MAAMG,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACxE,SAAS,CAAC,CAAA;EAC3D4D,MAAAA,0BAAW,CAACW,eAAe,CAACpD,aAAa,EAAE,CAAC,CAAA;EAC5C;EACA;QACA,MAAMsD,mBAAmB,GAAGF,eAAe,CAACV,eAAe,CAAC1J,OAAO,CAACsF,GAAG,CAAC,CAAA;EACxE,MAAA,IAAI9B,KAAK,EAAE;UACP,IAAI;EACAA,UAAAA,KAAK,CAAC+G,SAAS,CAACD,mBAAmB,CAAC,CAAA;WACvC,CACD,OAAO9J,KAAK,EAAE;EACV,UAA2C;EACvC;cACA,IAAI,SAAS,IAAIgD,KAAK,EAAE;EACpB2F,cAAAA,gBAAM,CAACqB,IAAI,CAAE,CAAkD,iDAAA,CAAA,GAC1D,2BAA0B,GAC1B,CAAA,CAAA,EAAGC,gCAAc,CAACjH,KAAK,CAACxD,OAAO,CAACsF,GAAG,CAAE,IAAG,CAAC,CAAA;EAClD,aAAA;EACJ,WAAA;EACJ,SAAA;EACJ,OAAA;EACA,MAAA,OAAO4E,OAAO,GAAGD,cAAc,GAAG,IAAI,CAAA;OACzC,CAAA;EACD;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACQ,IAAI,CAACS,cAAc,GAAG,OAAO;QAAE7E,SAAS;EAAE7F,MAAAA,OAAAA;EAAS,KAAC,KAAK;EACrD,MAA2C;EACvC+H,QAAAA,gBAAM,CAACC,MAAM,CAACnC,SAAS,EAAE,QAAQ,EAAE;EAC/BoC,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,QAAQ;EACnBC,UAAAA,QAAQ,EAAE,gBAAgB;EAC1BC,UAAAA,SAAS,EAAE,WAAA;EACf,SAAC,CAAC,CAAA;EACFL,QAAAA,gBAAM,CAAC4C,UAAU,CAAC3K,OAAO,EAAE4K,OAAO,EAAE;EAChC3C,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,QAAQ;EACnBC,UAAAA,QAAQ,EAAE,gBAAgB;EAC1BC,UAAAA,SAAS,EAAE,SAAA;EACf,SAAC,CAAC,CAAA;EACN,OAAA;EACA,MAAA,MAAMgC,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACxE,SAAS,CAAC,CAAA;EAC3D,MAAA,MAAMuE,eAAe,CAACV,eAAe,CAAC1J,OAAO,CAACsF,GAAG,CAAC,CAAA;EAClD,MAAA,MAAM8E,eAAe,CAACpD,aAAa,EAAE,CAAA;OACxC,CAAA;EACD,IAA2C;QACvC,IAAI,EAAEY,MAAM,CAACS,UAAU,IAAIT,MAAM,CAACU,aAAa,CAAC,EAAE;EAC9C,QAAA,MAAM,IAAIC,4BAAY,CAAC,6BAA6B,EAAE;EAClDN,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,QAAQ;EACnBC,UAAAA,QAAQ,EAAE,aAAA;EACd,SAAC,CAAC,CAAA;EACN,OAAA;QACA,IAAIP,MAAM,CAACS,UAAU,EAAE;UACnBN,gBAAM,CAACC,MAAM,CAACJ,MAAM,CAACS,UAAU,EAAE,QAAQ,EAAE;EACvCJ,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,QAAQ;EACnBC,UAAAA,QAAQ,EAAE,aAAa;EACvBC,UAAAA,SAAS,EAAE,mBAAA;EACf,SAAC,CAAC,CAAA;EACN,OAAA;QACA,IAAIR,MAAM,CAACU,aAAa,EAAE;UACtBP,gBAAM,CAACC,MAAM,CAACJ,MAAM,CAACU,aAAa,EAAE,QAAQ,EAAE;EAC1CL,UAAAA,UAAU,EAAE,oBAAoB;EAChCC,UAAAA,SAAS,EAAE,QAAQ;EACnBC,UAAAA,QAAQ,EAAE,aAAa;EACvBC,UAAAA,SAAS,EAAE,sBAAA;EACf,SAAC,CAAC,CAAA;EACN,OAAA;EACJ,KAAA;MACA,IAAI,CAACyC,OAAO,GAAGjD,MAAM,CAAA;EACrB,IAAA,IAAI,CAACa,cAAc,GAAGb,MAAM,CAACU,aAAa,CAAA;EAC1C,IAAA,IAAI,CAACwC,iBAAiB,GAAG,IAAI7G,GAAG,EAAE,CAAA;MAClC,IAAI2D,MAAM,CAACmD,iBAAiB,EAAE;EAC1BC,MAAAA,wDAA0B,CAAC,MAAM,IAAI,CAACC,sBAAsB,EAAE,CAAC,CAAA;EACnE,KAAA;EACJ,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACIZ,mBAAmBA,CAACxE,SAAS,EAAE;EAC3B,IAAA,IAAIA,SAAS,KAAKqF,wBAAU,CAACC,cAAc,EAAE,EAAE;EAC3C,MAAA,MAAM,IAAI5C,4BAAY,CAAC,2BAA2B,CAAC,CAAA;EACvD,KAAA;MACA,IAAI6B,eAAe,GAAG,IAAI,CAACU,iBAAiB,CAACvJ,GAAG,CAACsE,SAAS,CAAC,CAAA;MAC3D,IAAI,CAACuE,eAAe,EAAE;QAClBA,eAAe,GAAG,IAAIzC,eAAe,CAAC9B,SAAS,EAAE,IAAI,CAACgF,OAAO,CAAC,CAAA;QAC9D,IAAI,CAACC,iBAAiB,CAAChK,GAAG,CAAC+E,SAAS,EAAEuE,eAAe,CAAC,CAAA;EAC1D,KAAA;EACA,IAAA,OAAOA,eAAe,CAAA;EAC1B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;IACID,oBAAoBA,CAACF,cAAc,EAAE;EACjC,IAAA,IAAI,CAAC,IAAI,CAACxB,cAAc,EAAE;EACtB;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACA;EACA;EACA,IAAA,MAAM2C,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAACpB,cAAc,CAAC,CAAA;MACxE,IAAImB,mBAAmB,KAAK,IAAI,EAAE;EAC9B;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACA;EACA,IAAA,MAAMtC,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE,CAAA;MACtB,OAAOsC,mBAAmB,IAAItC,GAAG,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,CAAA;EAClE,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI4C,uBAAuBA,CAACpB,cAAc,EAAE;MACpC,IAAI,CAACA,cAAc,CAACqB,OAAO,CAACpK,GAAG,CAAC,MAAM,CAAC,EAAE;EACrC,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;MACA,MAAMqK,UAAU,GAAGtB,cAAc,CAACqB,OAAO,CAAC/J,GAAG,CAAC,MAAM,CAAC,CAAA;EACrD,IAAA,MAAMiK,UAAU,GAAG,IAAI3C,IAAI,CAAC0C,UAAU,CAAC,CAAA;EACvC,IAAA,MAAME,UAAU,GAAGD,UAAU,CAACE,OAAO,EAAE,CAAA;EACvC;EACA;EACA,IAAA,IAAIC,KAAK,CAACF,UAAU,CAAC,EAAE;EACnB,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA,IAAA,OAAOA,UAAU,CAAA;EACrB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACI,MAAMR,sBAAsBA,GAAG;EAC3B;EACA;MACA,KAAK,MAAM,CAACpF,SAAS,EAAEuE,eAAe,CAAC,IAAI,IAAI,CAACU,iBAAiB,EAAE;EAC/D,MAAA,MAAM/F,IAAI,CAACkE,MAAM,CAACxB,MAAM,CAAC5B,SAAS,CAAC,CAAA;EACnC,MAAA,MAAMuE,eAAe,CAAC3C,MAAM,EAAE,CAAA;EAClC,KAAA;EACA;EACA,IAAA,IAAI,CAACqD,iBAAiB,GAAG,IAAI7G,GAAG,EAAE,CAAA;EACtC,GAAA;EACJ;;;;;;;;;;;"}
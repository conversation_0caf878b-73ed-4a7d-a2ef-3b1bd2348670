{"version": 3, "file": "workbox-streams.prod.js", "sources": ["../_version.js", "../concatenate.js", "../utils/createHeaders.js", "../concatenateToResponse.js", "../isSupported.js", "../strategy.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:streams:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Takes either a Response, a ReadableStream, or a\n * [BodyInit](https://fetch.spec.whatwg.org/#bodyinit) and returns the\n * ReadableStreamReader object associated with it.\n *\n * @param {workbox-streams.StreamSource} source\n * @return {ReadableStreamReader}\n * @private\n */\nfunction _getReaderFromSource(source) {\n    if (source instanceof Response) {\n        // See https://github.com/GoogleChrome/workbox/issues/2998\n        if (source.body) {\n            return source.body.getReader();\n        }\n        throw new WorkboxError('opaque-streams-source', { type: source.type });\n    }\n    if (source instanceof ReadableStream) {\n        return source.getReader();\n    }\n    return new Response(source).body.getReader();\n}\n/**\n * Takes multiple source Promises, each of which could resolve to a Response, a\n * ReadableStream, or a [BodyInit](https://fetch.spec.whatwg.org/#bodyinit).\n *\n * Returns an object exposing a ReadableStream with each individual stream's\n * data returned in sequence, along with a Promise which signals when the\n * stream is finished (useful for passing to a FetchEvent's waitUntil()).\n *\n * @param {Array<Promise<workbox-streams.StreamSource>>} sourcePromises\n * @return {Object<{done: Promise, stream: ReadableStream}>}\n *\n * @memberof workbox-streams\n */\nfunction concatenate(sourcePromises) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isArray(sourcePromises, {\n            moduleName: 'workbox-streams',\n            funcName: 'concatenate',\n            paramName: 'sourcePromises',\n        });\n    }\n    const readerPromises = sourcePromises.map((sourcePromise) => {\n        return Promise.resolve(sourcePromise).then((source) => {\n            return _getReaderFromSource(source);\n        });\n    });\n    const streamDeferred = new Deferred();\n    let i = 0;\n    const logMessages = [];\n    const stream = new ReadableStream({\n        pull(controller) {\n            return readerPromises[i]\n                .then((reader) => {\n                if (reader instanceof ReadableStreamDefaultReader) {\n                    return reader.read();\n                }\n                else {\n                    return;\n                }\n            })\n                .then((result) => {\n                if (result === null || result === void 0 ? void 0 : result.done) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logMessages.push([\n                            'Reached the end of source:',\n                            sourcePromises[i],\n                        ]);\n                    }\n                    i++;\n                    if (i >= readerPromises.length) {\n                        // Log all the messages in the group at once in a single group.\n                        if (process.env.NODE_ENV !== 'production') {\n                            logger.groupCollapsed(`Concatenating ${readerPromises.length} sources.`);\n                            for (const message of logMessages) {\n                                if (Array.isArray(message)) {\n                                    logger.log(...message);\n                                }\n                                else {\n                                    logger.log(message);\n                                }\n                            }\n                            logger.log('Finished reading all sources.');\n                            logger.groupEnd();\n                        }\n                        controller.close();\n                        streamDeferred.resolve();\n                        return;\n                    }\n                    // The `pull` method is defined because we're inside it.\n                    return this.pull(controller);\n                }\n                else {\n                    controller.enqueue(result === null || result === void 0 ? void 0 : result.value);\n                }\n            })\n                .catch((error) => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.error('An error occurred:', error);\n                }\n                streamDeferred.reject(error);\n                throw error;\n            });\n        },\n        cancel() {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn('The ReadableStream was cancelled.');\n            }\n            streamDeferred.resolve();\n        },\n    });\n    return { done: streamDeferred.promise, stream };\n}\nexport { concatenate };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * This is a utility method that determines whether the current browser supports\n * the features required to create streamed responses. Currently, it checks if\n * [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * is available.\n *\n * @private\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {boolean} `true`, if the current browser meets the requirements for\n * streaming responses, and `false` otherwise.\n *\n * @memberof workbox-streams\n */\nfunction createHeaders(headersInit = {}) {\n    // See https://github.com/GoogleChrome/workbox/issues/1461\n    const headers = new Headers(headersInit);\n    if (!headers.has('content-type')) {\n        headers.set('content-type', 'text/html');\n    }\n    return headers;\n}\nexport { createHeaders };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { createHeaders } from './utils/createHeaders.js';\nimport { concatenate } from './concatenate.js';\nimport './_version.js';\n/**\n * Takes multiple source Promises, each of which could resolve to a Response, a\n * ReadableStream, or a [BodyInit](https://fetch.spec.whatwg.org/#bodyinit),\n * along with a\n * [HeadersInit](https://fetch.spec.whatwg.org/#typedefdef-headersinit).\n *\n * Returns an object exposing a Response whose body consists of each individual\n * stream's data returned in sequence, along with a Promise which signals when\n * the stream is finished (useful for passing to a FetchEvent's waitUntil()).\n *\n * @param {Array<Promise<workbox-streams.StreamSource>>} sourcePromises\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {Object<{done: Promise, response: Response}>}\n *\n * @memberof workbox-streams\n */\nfunction concatenateToResponse(sourcePromises, headersInit) {\n    const { done, stream } = concatenate(sourcePromises);\n    const headers = createHeaders(headersInit);\n    const response = new Response(stream, { headers });\n    return { done, response };\n}\nexport { concatenateToResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructReadableStream } from 'workbox-core/_private/canConstructReadableStream.js';\nimport './_version.js';\n/**\n * This is a utility method that determines whether the current browser supports\n * the features required to create streamed responses. Currently, it checks if\n * [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * can be created.\n *\n * @return {boolean} `true`, if the current browser meets the requirements for\n * streaming responses, and `false` otherwise.\n *\n * @memberof workbox-streams\n */\nfunction isSupported() {\n    return canConstructReadableStream();\n}\nexport { isSupported };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { createHeaders } from './utils/createHeaders.js';\nimport { concatenateToResponse } from './concatenateToResponse.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * A shortcut to create a strategy that could be dropped-in to Workbox's router.\n *\n * On browsers that do not support constructing new `ReadableStream`s, this\n * strategy will automatically wait for all the `sourceFunctions` to complete,\n * and create a final response that concatenates their values together.\n *\n * @param {Array<function({event, request, url, params})>} sourceFunctions\n * An array of functions similar to {@link workbox-routing~handlerCallback}\n * but that instead return a {@link workbox-streams.StreamSource} (or a\n * Promise which resolves to one).\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {workbox-routing~handlerCallback}\n * @memberof workbox-streams\n */\nfunction strategy(sourceFunctions, headersInit) {\n    return async ({ event, request, url, params }) => {\n        const sourcePromises = sourceFunctions.map((fn) => {\n            // Ensure the return value of the function is always a promise.\n            return Promise.resolve(fn({ event, request, url, params }));\n        });\n        if (isSupported()) {\n            const { done, response } = concatenateToResponse(sourcePromises, headersInit);\n            if (event) {\n                event.waitUntil(done);\n            }\n            return response;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The current browser doesn't support creating response ` +\n                `streams. Falling back to non-streaming response instead.`);\n        }\n        // Fallback to waiting for everything to finish, and concatenating the\n        // responses.\n        const blobPartsPromises = sourcePromises.map(async (sourcePromise) => {\n            const source = await sourcePromise;\n            if (source instanceof Response) {\n                return source.blob();\n            }\n            else {\n                // Technically, a `StreamSource` object can include any valid\n                // `BodyInit` type, including `FormData` and `URLSearchParams`, which\n                // cannot be passed to the Blob constructor directly, so we have to\n                // convert them to actual Blobs first.\n                return new Response(source).blob();\n            }\n        });\n        const blobParts = await Promise.all(blobPartsPromises);\n        const headers = createHeaders(headersInit);\n        // Constructing a new Response from a Blob source is well-supported.\n        // So is constructing a new Blob from multiple source Blobs or strings.\n        return new Response(new Blob(blobParts), { headers });\n    };\n}\nexport { strategy };\n"], "names": ["self", "_", "e", "concatenate", "sourcePromises", "readerPromises", "map", "sourcePromise", "Promise", "resolve", "then", "source", "Response", "body", "<PERSON><PERSON><PERSON><PERSON>", "WorkboxError", "type", "ReadableStream", "_getReaderFromSource", "streamDef<PERSON>red", "Deferred", "i", "stream", "pull", "controller", "reader", "ReadableStreamDefaultReader", "read", "result", "done", "length", "close", "this", "enqueue", "value", "catch", "error", "reject", "cancel", "promise", "createHeaders", "headersInit", "headers", "Headers", "has", "set", "concatenateToResponse", "response", "isSupported", "canConstructReadableStream", "sourceFunctions", "async", "event", "request", "url", "params", "fn", "waitUntil", "blobPartsPromises", "blob", "blobParts", "all", "Blob"], "mappings": "kFAEA,IACIA,KAAK,0BAA4BC,GACrC,CACA,MAAOC,GAAG,CC0CV,SAASC,EAAYC,GAQjB,MAAMC,EAAiBD,EAAeE,KAAKC,GAChCC,QAAQC,QAAQF,GAAeG,MAAMC,GAnCpD,SAA8BA,GAC1B,GAAIA,aAAkBC,SAAU,CAE5B,GAAID,EAAOE,KACP,OAAOF,EAAOE,KAAKC,YAEvB,MAAM,IAAIC,EAAYA,aAAC,wBAAyB,CAAEC,KAAML,EAAOK,MACnE,CACA,OAAIL,aAAkBM,eACXN,EAAOG,YAEX,IAAIF,SAASD,GAAQE,KAAKC,WACrC,CAwBmBI,CAAqBP,OAG9BQ,EAAiB,IAAIC,EAAAA,SAC3B,IAAIC,EAAI,EAER,MAAMC,EAAS,IAAIL,eAAe,CAC9BM,KAAKC,GACD,OAAOnB,EAAegB,GACjBX,MAAMe,GACHA,aAAkBC,4BACXD,EAAOE,YAGd,IAGHjB,MAAMkB,IACP,GAAIA,aAAuC,EAASA,EAAOC,KAQvD,OADAR,IACIA,GAAKhB,EAAeyB,QAepBN,EAAWO,aACXZ,EAAeV,WAIZuB,KAAKT,KAAKC,GAGjBA,EAAWS,QAAQL,aAAuC,EAASA,EAAOM,MAC9E,IAECC,OAAOC,IAKR,MADAjB,EAAekB,OAAOD,GAChBA,CAAK,GAElB,EACDE,SAIInB,EAAeV,SACnB,IAEJ,MAAO,CAAEoB,KAAMV,EAAeoB,QAASjB,SAC3C,CCvGA,SAASkB,EAAcC,EAAc,IAEjC,MAAMC,EAAU,IAAIC,QAAQF,GAI5B,OAHKC,EAAQE,IAAI,iBACbF,EAAQG,IAAI,eAAgB,aAEzBH,CACX,CCFA,SAASI,EAAsB1C,EAAgBqC,GAC3C,MAAMZ,KAAEA,EAAIP,OAAEA,GAAWnB,EAAYC,GAC/BsC,EAAUF,EAAcC,GAE9B,MAAO,CAAEZ,OAAMkB,SADE,IAAInC,SAASU,EAAQ,CAAEoB,YAE5C,CCZA,SAASM,IACL,OAAOC,EAA0BA,4BACrC,6ECMA,SAAkBC,EAAiBT,GAC/B,OAAOU,OAASC,QAAOC,UAASC,MAAKC,aACjC,MAAMnD,EAAiB8C,EAAgB5C,KAAKkD,GAEjChD,QAAQC,QAAQ+C,EAAG,CAAEJ,QAAOC,UAASC,MAAKC,cAErD,GAAIP,IAAe,CACf,MAAMnB,KAAEA,EAAIkB,SAAEA,GAAaD,EAAsB1C,EAAgBqC,GAIjE,OAHIW,GACAA,EAAMK,UAAU5B,GAEbkB,CACX,CAOA,MAAMW,EAAoBtD,EAAeE,KAAI6C,UACzC,MAAMxC,QAAeJ,EACrB,OAAII,aAAkBC,SACXD,EAAOgD,OAOP,IAAI/C,SAASD,GAAQgD,MAChC,IAEEC,QAAkBpD,QAAQqD,IAAIH,GAC9BhB,EAAUF,EAAcC,GAG9B,OAAO,IAAI7B,SAAS,IAAIkD,KAAKF,GAAY,CAAElB,WAAU,CAE7D"}
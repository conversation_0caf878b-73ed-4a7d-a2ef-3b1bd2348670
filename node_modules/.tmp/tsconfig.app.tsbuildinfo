{"root": ["../../env.d.ts", "../../src/App.vue", "../../src/main.ts", "../../src/vite-env.d.ts", "../../src/components/HelloWorld.vue", "../../src/components/PWAInstallPrompt.vue", "../../src/components/TheWelcome.vue", "../../src/components/WelcomeItem.vue", "../../src/components/icons/IconCommunity.vue", "../../src/components/icons/IconDocumentation.vue", "../../src/components/icons/IconEcosystem.vue", "../../src/components/icons/IconSupport.vue", "../../src/components/icons/IconTooling.vue", "../../src/layouts/AdminLayout.vue", "../../src/layouts/NamoradaLayout.vue", "../../src/router/index.ts", "../../src/stores/auth.ts", "../../src/stores/counter.ts", "../../src/views/AboutView.vue", "../../src/views/AdminLoginView.vue", "../../src/views/HomeView.vue", "../../src/views/admin/ConfiguracoesView.vue", "../../src/views/admin/DashboardView.vue", "../../src/views/admin/EnigmasView.vue", "../../src/views/namorada/CronometroView.vue", "../../src/views/namorada/EnigmaView.vue", "../../src/views/namorada/InicioView.vue", "../../src/views/namorada/PedidoView.vue", "../../src/views/namorada/ProgressoView.vue"], "version": "5.8.3"}
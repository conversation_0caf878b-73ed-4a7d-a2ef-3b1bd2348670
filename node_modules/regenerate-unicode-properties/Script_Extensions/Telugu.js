const set = require('regenerate')(0xC5D, 0x1CDA, 0x1CF2);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0xC00, 0xC0C).addRange(0xC0E, 0xC10).addRange(0xC12, 0xC28).addRange(0xC2A, 0xC39).addRange(0xC3C, 0xC44).addRange(0xC46, 0xC48).addRange(0xC4A, 0xC4D).addRange(0xC55, 0xC56).addRange(0xC58, 0xC5A).addRange(0xC60, 0xC63).addRange(0xC66, 0xC6F).addRange(0xC77, 0xC7F);
exports.characters = set;

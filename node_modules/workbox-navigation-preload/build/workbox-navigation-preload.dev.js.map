{"version": 3, "file": "workbox-navigation-preload.dev.js", "sources": ["../_version.js", "../isSupported.js", "../disable.js", "../enable.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:navigation-preload:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * @return {boolean} Whether or not the current browser supports enabling\n * navigation preload.\n *\n * @memberof workbox-navigation-preload\n */\nfunction isSupported() {\n    return Boolean(self.registration && self.registration.navigationPreload);\n}\nexport { isSupported };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * If the browser supports Navigation Preload, then this will disable it.\n *\n * @memberof workbox-navigation-preload\n */\nfunction disable() {\n    if (isSupported()) {\n        self.addEventListener('activate', (event) => {\n            event.waitUntil(self.registration.navigationPreload.disable().then(() => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Navigation preload is disabled.`);\n                }\n            }));\n        });\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Navigation preload is not supported in this browser.`);\n        }\n    }\n}\nexport { disable };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * If the browser supports Navigation Preload, then this will enable it.\n *\n * @param {string} [headerValue] Optionally, allows developers to\n * [override](https://developers.google.com/web/updates/2017/02/navigation-preload#changing_the_header)\n * the value of the `Service-Worker-Navigation-Preload` header which will be\n * sent to the server when making the navigation request.\n *\n * @memberof workbox-navigation-preload\n */\nfunction enable(headerValue) {\n    if (isSupported()) {\n        self.addEventListener('activate', (event) => {\n            event.waitUntil(self.registration.navigationPreload.enable().then(() => {\n                // Defaults to Service-Worker-Navigation-Preload: true if not set.\n                if (headerValue) {\n                    void self.registration.navigationPreload.setHeaderValue(headerValue);\n                }\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Navigation preload is enabled.`);\n                }\n            }));\n        });\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Navigation preload is not supported in this browser.`);\n        }\n    }\n}\nexport { enable };\n"], "names": ["self", "_", "e", "isSupported", "Boolean", "registration", "navigationPreload", "disable", "addEventListener", "event", "waitUntil", "then", "logger", "log", "enable", "headerValue", "setHeaderValue"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,kCAAkC,CAAC,IAAIC,CAAC,EAAE,CAAA;IACnD,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,WAAWA,GAAG;MACnB,OAAOC,OAAO,CAACJ,IAAI,CAACK,YAAY,IAAIL,IAAI,CAACK,YAAY,CAACC,iBAAiB,CAAC,CAAA;IAC5E;;IChBA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA,SAASC,OAAOA,GAAG;MACf,IAAIJ,WAAW,EAAE,EAAE;IACfH,IAAAA,IAAI,CAACQ,gBAAgB,CAAC,UAAU,EAAGC,KAAK,IAAK;IACzCA,MAAAA,KAAK,CAACC,SAAS,CAACV,IAAI,CAACK,YAAY,CAACC,iBAAiB,CAACC,OAAO,EAAE,CAACI,IAAI,CAAC,MAAM;IACrE,QAA2C;IACvCC,UAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,+BAAA,CAAgC,CAAC,CAAA;IACjD,SAAA;IACJ,OAAC,CAAC,CAAC,CAAA;IACP,KAAC,CAAC,CAAA;IACN,GAAC,MACI;IACD,IAA2C;IACvCD,MAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,oDAAA,CAAqD,CAAC,CAAA;IACtE,KAAA;IACJ,GAAA;IACJ;;IC9BA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,MAAMA,CAACC,WAAW,EAAE;MACzB,IAAIZ,WAAW,EAAE,EAAE;IACfH,IAAAA,IAAI,CAACQ,gBAAgB,CAAC,UAAU,EAAGC,KAAK,IAAK;IACzCA,MAAAA,KAAK,CAACC,SAAS,CAACV,IAAI,CAACK,YAAY,CAACC,iBAAiB,CAACQ,MAAM,EAAE,CAACH,IAAI,CAAC,MAAM;IACpE;IACA,QAAA,IAAII,WAAW,EAAE;cACb,KAAKf,IAAI,CAACK,YAAY,CAACC,iBAAiB,CAACU,cAAc,CAACD,WAAW,CAAC,CAAA;IACxE,SAAA;IACA,QAA2C;IACvCH,UAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,8BAAA,CAA+B,CAAC,CAAA;IAChD,SAAA;IACJ,OAAC,CAAC,CAAC,CAAA;IACP,KAAC,CAAC,CAAA;IACN,GAAC,MACI;IACD,IAA2C;IACvCD,MAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,oDAAA,CAAqD,CAAC,CAAA;IACtE,KAAA;IACJ,GAAA;IACJ;;;;;;;;;;;;"}
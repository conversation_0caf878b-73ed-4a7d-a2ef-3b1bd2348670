{"version": 3, "file": "workbox-precaching.dev.js", "sources": ["../_version.js", "../utils/createCacheKey.js", "../utils/PrecacheInstallReportPlugin.js", "../utils/PrecacheCacheKeyPlugin.js", "../utils/printCleanupDetails.js", "../utils/printInstallDetails.js", "../PrecacheStrategy.js", "../PrecacheController.js", "../utils/getOrCreatePrecacheController.js", "../addPlugins.js", "../utils/removeIgnoredSearchParams.js", "../utils/generateURLVariations.js", "../PrecacheRoute.js", "../addRoute.js", "../utils/deleteOutdatedCaches.js", "../cleanupOutdatedCaches.js", "../createHandlerBoundToURL.js", "../getCacheKeyForURL.js", "../matchPrecache.js", "../precache.js", "../precacheAndRoute.js", "../PrecacheFallbackPlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to determine the\n * of assets that were updated (or not updated) during the install event.\n *\n * @private\n */\nclass PrecacheInstallReportPlugin {\n    constructor() {\n        this.updatedURLs = [];\n        this.notUpdatedURLs = [];\n        this.handlerWillStart = async ({ request, state, }) => {\n            // TODO: `state` should never be undefined...\n            if (state) {\n                state.originalRequest = request;\n            }\n        };\n        this.cachedResponseWillBeUsed = async ({ event, state, cachedResponse, }) => {\n            if (event.type === 'install') {\n                if (state &&\n                    state.originalRequest &&\n                    state.originalRequest instanceof Request) {\n                    // TODO: `state` should never be undefined...\n                    const url = state.originalRequest.url;\n                    if (cachedResponse) {\n                        this.notUpdatedURLs.push(url);\n                    }\n                    else {\n                        this.updatedURLs.push(url);\n                    }\n                }\n            }\n            return cachedResponse;\n        };\n    }\n}\nexport { PrecacheInstallReportPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to translate URLs into\n * the corresponding cache key, based on the current revision info.\n *\n * @private\n */\nclass PrecacheCacheKeyPlugin {\n    constructor({ precacheController }) {\n        this.cacheKeyWillBeUsed = async ({ request, params, }) => {\n            // Params is type any, can't change right now.\n            /* eslint-disable */\n            const cacheKey = (params === null || params === void 0 ? void 0 : params.cacheKey) ||\n                this._precacheController.getCacheKeyForURL(request.url);\n            /* eslint-enable */\n            return cacheKey\n                ? new Request(cacheKey, { headers: request.headers })\n                : request;\n        };\n        this._precacheController = precacheController;\n    }\n}\nexport { PrecacheCacheKeyPlugin };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} deletedURLs\n *\n * @private\n */\nconst logGroup = (groupTitle, deletedURLs) => {\n    logger.groupCollapsed(groupTitle);\n    for (const url of deletedURLs) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n};\n/**\n * @param {Array<string>} deletedURLs\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printCleanupDetails(deletedURLs) {\n    const deletionCount = deletedURLs.length;\n    if (deletionCount > 0) {\n        logger.groupCollapsed(`During precaching cleanup, ` +\n            `${deletionCount} cached ` +\n            `request${deletionCount === 1 ? ' was' : 's were'} deleted.`);\n        logGroup('Deleted Cache Requests', deletedURLs);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} urls\n *\n * @private\n */\nfunction _nestedGroup(groupTitle, urls) {\n    if (urls.length === 0) {\n        return;\n    }\n    logger.groupCollapsed(groupTitle);\n    for (const url of urls) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n}\n/**\n * @param {Array<string>} urlsToPrecache\n * @param {Array<string>} urlsAlreadyPrecached\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printInstallDetails(urlsToPrecache, urlsAlreadyPrecached) {\n    const precachedCount = urlsToPrecache.length;\n    const alreadyPrecachedCount = urlsAlreadyPrecached.length;\n    if (precachedCount || alreadyPrecachedCount) {\n        let message = `Precaching ${precachedCount} file${precachedCount === 1 ? '' : 's'}.`;\n        if (alreadyPrecachedCount > 0) {\n            message +=\n                ` ${alreadyPrecachedCount} ` +\n                    `file${alreadyPrecachedCount === 1 ? ' is' : 's are'} already cached.`;\n        }\n        logger.groupCollapsed(message);\n        _nestedGroup(`View newly precached URLs.`, urlsToPrecache);\n        _nestedGroup(`View previously precached URLs.`, urlsAlreadyPrecached);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from 'workbox-strategies/Strategy.js';\nimport './_version.js';\n/**\n * A {@link workbox-strategies.Strategy} implementation\n * specifically designed to work with\n * {@link workbox-precaching.PrecacheController}\n * to both cache and fetch precached assets.\n *\n * Note: an instance of this class is created automatically when creating a\n * `PrecacheController`; it's generally not necessary to create this yourself.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-precaching\n */\nclass PrecacheStrategy extends Strategy {\n    /**\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] {@link https://developers.google.com/web/tools/workbox/guides/using-plugins|Plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters|init}\n     * of all fetch() requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * {@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions|CacheQueryOptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor(options = {}) {\n        options.cacheName = cacheNames.getPrecacheName(options.cacheName);\n        super(options);\n        this._fallbackToNetwork =\n            options.fallbackToNetwork === false ? false : true;\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        this.plugins.push(PrecacheStrategy.copyRedirectedCacheableResponsesPlugin);\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const response = await handler.cacheMatch(request);\n        if (response) {\n            return response;\n        }\n        // If this is an `install` event for an entry that isn't already cached,\n        // then populate the cache.\n        if (handler.event && handler.event.type === 'install') {\n            return await this._handleInstall(request, handler);\n        }\n        // Getting here means something went wrong. An entry that should have been\n        // precached wasn't found in the cache.\n        return await this._handleFetch(request, handler);\n    }\n    async _handleFetch(request, handler) {\n        let response;\n        const params = (handler.params || {});\n        // Fall back to the network if we're configured to do so.\n        if (this._fallbackToNetwork) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(request.url)} in ${this.cacheName} was not ` +\n                    `found. Falling back to the network.`);\n            }\n            const integrityInManifest = params.integrity;\n            const integrityInRequest = request.integrity;\n            const noIntegrityConflict = !integrityInRequest || integrityInRequest === integrityInManifest;\n            // Do not add integrity if the original request is no-cors\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            response = await handler.fetch(new Request(request, {\n                integrity: request.mode !== 'no-cors'\n                    ? integrityInRequest || integrityInManifest\n                    : undefined,\n            }));\n            // It's only \"safe\" to repair the cache if we're using SRI to guarantee\n            // that the response matches the precache manifest's expectations,\n            // and there's either a) no integrity property in the incoming request\n            // or b) there is an integrity, and it matches the precache manifest.\n            // See https://github.com/GoogleChrome/workbox/issues/2858\n            // Also if the original request users no-cors we don't use integrity.\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            if (integrityInManifest &&\n                noIntegrityConflict &&\n                request.mode !== 'no-cors') {\n                this._useDefaultCacheabilityPluginIfNeeded();\n                const wasCached = await handler.cachePut(request, response.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    if (wasCached) {\n                        logger.log(`A response for ${getFriendlyURL(request.url)} ` +\n                            `was used to \"repair\" the precache.`);\n                    }\n                }\n            }\n        }\n        else {\n            // This shouldn't normally happen, but there are edge cases:\n            // https://github.com/GoogleChrome/workbox/issues/1441\n            throw new WorkboxError('missing-precache-entry', {\n                cacheName: this.cacheName,\n                url: request.url,\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            const cacheKey = params.cacheKey || (await handler.getCacheKey(request, 'read'));\n            // Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Precaching is responding to: ` + getFriendlyURL(request.url));\n            logger.log(`Serving the precached url: ${getFriendlyURL(cacheKey instanceof Request ? cacheKey.url : cacheKey)}`);\n            logger.groupCollapsed(`View request details here.`);\n            logger.log(request);\n            logger.groupEnd();\n            logger.groupCollapsed(`View response details here.`);\n            logger.log(response);\n            logger.groupEnd();\n            logger.groupEnd();\n        }\n        return response;\n    }\n    async _handleInstall(request, handler) {\n        this._useDefaultCacheabilityPluginIfNeeded();\n        const response = await handler.fetch(request);\n        // Make sure we defer cachePut() until after we know the response\n        // should be cached; see https://github.com/GoogleChrome/workbox/issues/2737\n        const wasCached = await handler.cachePut(request, response.clone());\n        if (!wasCached) {\n            // Throwing here will lead to the `install` handler failing, which\n            // we want to do if *any* of the responses aren't safe to cache.\n            throw new WorkboxError('bad-precaching-response', {\n                url: request.url,\n                status: response.status,\n            });\n        }\n        return response;\n    }\n    /**\n     * This method is complex, as there a number of things to account for:\n     *\n     * The `plugins` array can be set at construction, and/or it might be added to\n     * to at any time before the strategy is used.\n     *\n     * At the time the strategy is used (i.e. during an `install` event), there\n     * needs to be at least one plugin that implements `cacheWillUpdate` in the\n     * array, other than `copyRedirectedCacheableResponsesPlugin`.\n     *\n     * - If this method is called and there are no suitable `cacheWillUpdate`\n     * plugins, we need to add `defaultPrecacheCacheabilityPlugin`.\n     *\n     * - If this method is called and there is exactly one `cacheWillUpdate`, then\n     * we don't have to do anything (this might be a previously added\n     * `defaultPrecacheCacheabilityPlugin`, or it might be a custom plugin).\n     *\n     * - If this method is called and there is more than one `cacheWillUpdate`,\n     * then we need to check if one is `defaultPrecacheCacheabilityPlugin`. If so,\n     * we need to remove it. (This situation is unlikely, but it could happen if\n     * the strategy is used multiple times, the first without a `cacheWillUpdate`,\n     * and then later on after manually adding a custom `cacheWillUpdate`.)\n     *\n     * See https://github.com/GoogleChrome/workbox/issues/2737 for more context.\n     *\n     * @private\n     */\n    _useDefaultCacheabilityPluginIfNeeded() {\n        let defaultPluginIndex = null;\n        let cacheWillUpdatePluginCount = 0;\n        for (const [index, plugin] of this.plugins.entries()) {\n            // Ignore the copy redirected plugin when determining what to do.\n            if (plugin === PrecacheStrategy.copyRedirectedCacheableResponsesPlugin) {\n                continue;\n            }\n            // Save the default plugin's index, in case it needs to be removed.\n            if (plugin === PrecacheStrategy.defaultPrecacheCacheabilityPlugin) {\n                defaultPluginIndex = index;\n            }\n            if (plugin.cacheWillUpdate) {\n                cacheWillUpdatePluginCount++;\n            }\n        }\n        if (cacheWillUpdatePluginCount === 0) {\n            this.plugins.push(PrecacheStrategy.defaultPrecacheCacheabilityPlugin);\n        }\n        else if (cacheWillUpdatePluginCount > 1 && defaultPluginIndex !== null) {\n            // Only remove the default plugin; multiple custom plugins are allowed.\n            this.plugins.splice(defaultPluginIndex, 1);\n        }\n        // Nothing needs to be done if cacheWillUpdatePluginCount is 1\n    }\n}\nPrecacheStrategy.defaultPrecacheCacheabilityPlugin = {\n    async cacheWillUpdate({ response }) {\n        if (!response || response.status >= 400) {\n            return null;\n        }\n        return response;\n    },\n};\nPrecacheStrategy.copyRedirectedCacheableResponsesPlugin = {\n    async cacheWillUpdate({ response }) {\n        return response.redirected ? await copyResponse(response) : response;\n    },\n};\nexport { PrecacheStrategy };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { waitUntil } from 'workbox-core/_private/waitUntil.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { PrecacheInstallReportPlugin } from './utils/PrecacheInstallReportPlugin.js';\nimport { PrecacheCacheKeyPlugin } from './utils/PrecacheCacheKeyPlugin.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport { PrecacheStrategy } from './PrecacheStrategy.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] The cache to use for precaching.\n     * @param {string} [options.plugins] Plugins to use when precaching as well\n     * as responding to fetch events for precached assets.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor({ cacheName, plugins = [], fallbackToNetwork = true, } = {}) {\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n        this._strategy = new PrecacheStrategy({\n            cacheName: cacheNames.getPrecacheName(cacheName),\n            plugins: [\n                ...plugins,\n                new PrecacheCacheKeyPlugin({ precacheController: this }),\n            ],\n            fallbackToNetwork,\n        });\n        // Bind the install and activate methods to the instance.\n        this.install = this.install.bind(this);\n        this.activate = this.activate.bind(this);\n    }\n    /**\n     * @type {workbox-precaching.PrecacheStrategy} The strategy created by this controller and\n     * used to cache assets and respond to fetch events.\n     */\n    get strategy() {\n        return this._strategy;\n    }\n    /**\n     * Adds items to the precache list, removing any duplicates and\n     * stores the files in the\n     * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n     * worker installs.\n     *\n     * This method can be called multiple times.\n     *\n     * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n     */\n    precache(entries) {\n        this.addToCacheList(entries);\n        if (!this._installAndActiveListenersAdded) {\n            self.addEventListener('install', this.install);\n            self.addEventListener('activate', this.activate);\n            this._installAndActiveListenersAdded = true;\n        }\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {Array<workbox-precaching.PrecacheController.PrecacheEntry|string>} entries\n     *     Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = typeof entry !== 'string' && entry.revision ? 'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.InstallResult>}\n     */\n    install(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const installReportPlugin = new PrecacheInstallReportPlugin();\n            this.strategy.plugins.push(installReportPlugin);\n            // Cache entries one at a time.\n            // See https://github.com/GoogleChrome/workbox/issues/2528\n            for (const [url, cacheKey] of this._urlsToCacheKeys) {\n                const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n                const cacheMode = this._urlsToCacheModes.get(url);\n                const request = new Request(url, {\n                    integrity,\n                    cache: cacheMode,\n                    credentials: 'same-origin',\n                });\n                await Promise.all(this.strategy.handleAll({\n                    params: { cacheKey },\n                    request,\n                    event,\n                }));\n            }\n            const { updatedURLs, notUpdatedURLs } = installReportPlugin;\n            if (process.env.NODE_ENV !== 'production') {\n                printInstallDetails(updatedURLs, notUpdatedURLs);\n            }\n            return { updatedURLs, notUpdatedURLs };\n        });\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.CleanupResult>}\n     */\n    activate(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            const currentlyCachedRequests = await cache.keys();\n            const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n            const deletedURLs = [];\n            for (const request of currentlyCachedRequests) {\n                if (!expectedCacheKeys.has(request.url)) {\n                    await cache.delete(request);\n                    deletedURLs.push(request.url);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                printCleanupDetails(deletedURLs);\n            }\n            return { deletedURLs };\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * @param {string} url A cache key whose SRI you want to look up.\n     * @return {string} The subresource integrity associated with the cache key,\n     * or undefined if it's not set.\n     */\n    getIntegrityForCacheKey(cacheKey) {\n        return this._cacheKeysToIntegrities.get(cacheKey);\n    }\n    /**\n     * This acts as a drop-in replacement for\n     * [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @return {workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        return (options) => {\n            options.request = new Request(url);\n            options.params = Object.assign({ cacheKey }, options.params);\n            return this.strategy.handle(options);\n        };\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds plugins to the precaching strategy.\n *\n * @param {Array<Object>} plugins\n *\n * @memberof workbox-precaching\n */\nfunction addPlugins(plugins) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.strategy.plugins.push(...plugins);\n}\nexport { addPlugins };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching = [/^utm_/, /^fbclid$/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { generateURLVariations } from './utils/generateURLVariations.js';\nimport './_version.js';\n/**\n * A subclass of {@link workbox-routing.Route} that takes a\n * {@link workbox-precaching.PrecacheController}\n * instance and uses it to match incoming requests and handle fetching\n * responses from the precache.\n *\n * @memberof workbox-precaching\n * @extends workbox-routing.Route\n */\nclass PrecacheRoute extends Route {\n    /**\n     * @param {PrecacheController} precacheController A `PrecacheController`\n     * instance used to both match requests and respond to fetch events.\n     * @param {Object} [options] Options to control how requests are matched\n     * against the list of precached URLs.\n     * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n     * check cache entries for a URLs ending with '/' to see if there is a hit when\n     * appending the `directoryIndex` value.\n     * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/, /^fbclid$/]] An\n     * array of regex's to remove search params when looking for a cache match.\n     * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n     * check the cache for the URL with a `.html` added to the end of the end.\n     * @param {workbox-precaching~urlManipulation} [options.urlManipulation]\n     * This is a function that should take a URL and return an array of\n     * alternative URLs that should be checked for precache matches.\n     */\n    constructor(precacheController, options) {\n        const match = ({ request, }) => {\n            const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n            for (const possibleURL of generateURLVariations(request.url, options)) {\n                const cacheKey = urlsToCacheKeys.get(possibleURL);\n                if (cacheKey) {\n                    const integrity = precacheController.getIntegrityForCacheKey(cacheKey);\n                    return { cacheKey, integrity };\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` + getFriendlyURL(request.url));\n            }\n            return;\n        };\n        super(match, precacheController.strategy);\n    }\n}\nexport { PrecacheRoute };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { PrecacheRoute } from './PrecacheRoute.js';\nimport './_version.js';\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options] See the {@link workbox-precaching.PrecacheRoute}\n * options.\n *\n * @memberof workbox-precaching\n */\nfunction addRoute(options) {\n    const precacheController = getOrCreatePrecacheController();\n    const precacheRoute = new PrecacheRoute(precacheController, options);\n    registerRoute(precacheRoute);\n}\nexport { addRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst SUBSTRING_TO_FIND = '-precache-';\n/**\n * Cleans up incompatible precaches that were created by older versions of\n * Workbox, by a service worker registered under the current scope.\n *\n * This is meant to be called as part of the `activate` event.\n *\n * This should be safe to use as long as you don't include `substringToFind`\n * (defaulting to `-precache-`) in your non-precache cache names.\n *\n * @param {string} currentPrecacheName The cache name currently in use for\n * precaching. This cache won't be deleted.\n * @param {string} [substringToFind='-precache-'] Cache names which include this\n * substring will be deleted (excluding `currentPrecacheName`).\n * @return {Array<string>} A list of all the cache names that were deleted.\n *\n * @private\n * @memberof workbox-precaching\n */\nconst deleteOutdatedCaches = async (currentPrecacheName, substringToFind = SUBSTRING_TO_FIND) => {\n    const cacheNames = await self.caches.keys();\n    const cacheNamesToDelete = cacheNames.filter((cacheName) => {\n        return (cacheName.includes(substringToFind) &&\n            cacheName.includes(self.registration.scope) &&\n            cacheName !== currentPrecacheName);\n    });\n    await Promise.all(cacheNamesToDelete.map((cacheName) => self.caches.delete(cacheName)));\n    return cacheNamesToDelete;\n};\nexport { deleteOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { deleteOutdatedCaches } from './utils/deleteOutdatedCaches.js';\nimport './_version.js';\n/**\n * Adds an `activate` event listener which will clean up incompatible\n * precaches that were created by older versions of Workbox.\n *\n * @memberof workbox-precaching\n */\nfunction cleanupOutdatedCaches() {\n    // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-436484705\n    self.addEventListener('activate', ((event) => {\n        const cacheName = cacheNames.getPrecacheName();\n        event.waitUntil(deleteOutdatedCaches(cacheName).then((cachesDeleted) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (cachesDeleted.length > 0) {\n                    logger.log(`The following out-of-date precaches were cleaned up ` +\n                        `automatically:`, cachesDeleted);\n                }\n            }\n        }));\n    }));\n}\nexport { cleanupOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#createHandlerBoundToURL} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call the\n * {@link PrecacheController#createHandlerBoundToURL} on that instance,\n * instead of using this function.\n *\n * @param {string} url The precached URL which will be used to lookup the\n * `Response`.\n * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n * response from the network if there's a precache miss.\n * @return {workbox-routing~handlerCallback}\n *\n * @memberof workbox-precaching\n */\nfunction createHandlerBoundToURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.createHandlerBoundToURL(url);\n}\nexport { createHandlerBoundToURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Takes in a URL, and returns the corresponding URL that could be used to\n * lookup the entry in the precache.\n *\n * If a relative URL is provided, the location of the service worker file will\n * be used as the base.\n *\n * For precached entries without revision information, the cache key will be the\n * same as the original URL.\n *\n * For precached entries with revision information, the cache key will be the\n * original URL with the addition of a query parameter used for keeping track of\n * the revision info.\n *\n * @param {string} url The URL whose cache key to look up.\n * @return {string} The cache key that corresponds to that URL.\n *\n * @memberof workbox-precaching\n */\nfunction getCacheKeyForURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.getCacheKeyForURL(url);\n}\nexport { getCacheKeyForURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#matchPrecache} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call\n * {@link PrecacheController#matchPrecache} on that instance,\n * instead of using this function.\n *\n * @param {string|Request} request The key (without revisioning parameters)\n * to look up in the precache.\n * @return {Promise<Response|undefined>}\n *\n * @memberof workbox-precaching\n */\nfunction matchPrecache(request) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.matchPrecache(request);\n}\nexport { matchPrecache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * {@link workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * {@link workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.precache(entries);\n}\nexport { precache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * {@link workbox-precaching.precache} and\n * {@link workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See the\n * {@link workbox-precaching.PrecacheRoute} options.\n *\n * @memberof workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * `PrecacheFallbackPlugin` allows you to specify an \"offline fallback\"\n * response to be used when a given strategy is unable to generate a response.\n *\n * It does this by intercepting the `handlerDidError` plugin callback\n * and returning a precached response, taking the expected revision parameter\n * into account automatically.\n *\n * Unless you explicitly pass in a `PrecacheController` instance to the\n * constructor, the default instance will be used. Generally speaking, most\n * developers will end up using the default.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheFallbackPlugin {\n    /**\n     * Constructs a new PrecacheFallbackPlugin with the associated fallbackURL.\n     *\n     * @param {Object} config\n     * @param {string} config.fallbackURL A precached URL to use as the fallback\n     *     if the associated strategy can't generate a response.\n     * @param {PrecacheController} [config.precacheController] An optional\n     *     PrecacheController instance. If not provided, the default\n     *     PrecacheController will be used.\n     */\n    constructor({ fallbackURL, precacheController, }) {\n        /**\n         * @return {Promise<Response>} The precache response for the fallback URL.\n         *\n         * @private\n         */\n        this.handlerDidError = () => this._precacheController.matchPrecache(this._fallbackURL);\n        this._fallbackURL = fallbackURL;\n        this._precacheController =\n            precacheController || getOrCreatePrecacheController();\n    }\n}\nexport { PrecacheFallbackPlugin };\n"], "names": ["self", "_", "e", "REVISION_SEARCH_PARAM", "createCacheKey", "entry", "WorkboxError", "urlObject", "URL", "location", "href", "cache<PERSON>ey", "url", "revision", "cacheKeyURL", "originalURL", "searchParams", "set", "PrecacheInstallReportPlugin", "constructor", "updatedURLs", "notUpdatedURLs", "handlerWillStart", "request", "state", "originalRequest", "cachedResponseWillBeUsed", "event", "cachedResponse", "type", "Request", "push", "PrecacheCacheKeyPlugin", "precacheController", "cacheKeyWillBeUsed", "params", "_precacheController", "getCacheKeyForURL", "headers", "logGroup", "groupTitle", "deletedURLs", "logger", "groupCollapsed", "log", "groupEnd", "printCleanupDetails", "deletionCount", "length", "_nestedGroup", "urls", "printInstallDetails", "urlsToPrecache", "urlsAlreadyPrecached", "precachedCount", "alreadyPrecachedCount", "message", "PrecacheStrategy", "Strategy", "options", "cacheName", "cacheNames", "getPrecacheName", "_fallbackToNetwork", "fallbackToNetwork", "plugins", "copyRedirectedCacheableResponsesPlugin", "_handle", "handler", "response", "cacheMatch", "_handleInstall", "_handleFetch", "warn", "getFriendlyURL", "integrityInManifest", "integrity", "integrityInRequest", "noIntegrityConflict", "fetch", "mode", "undefined", "_useDefaultCacheabilityPluginIfNeeded", "was<PERSON>ached", "cachePut", "clone", "get<PERSON><PERSON><PERSON><PERSON>", "status", "defaultPluginIndex", "cacheWillUpdatePluginCount", "index", "plugin", "entries", "defaultPrecacheCacheabilityPlugin", "cacheWillUpdate", "splice", "redirected", "copyResponse", "PrecacheController", "_urlsTo<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "_urlsToCacheModes", "_cacheKeysToIntegrities", "_strategy", "install", "bind", "activate", "strategy", "precache", "addToCacheList", "_installAndActiveListenersAdded", "addEventListener", "assert", "isArray", "moduleName", "className", "funcName", "paramName", "urlsToWarnAbout", "cacheMode", "has", "get", "firstEntry", "secondEntry", "warningMessage", "join", "waitUntil", "installReportPlugin", "cache", "credentials", "Promise", "all", "handleAll", "caches", "open", "currentlyCachedRequests", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "values", "delete", "getURLsToCacheKeys", "getCachedURLs", "getIntegrityForCacheKey", "matchPrecache", "match", "createHandlerBoundToURL", "Object", "assign", "handle", "getOrCreatePrecacheController", "addPlugins", "removeIgnoredSearchParams", "ignoreURLParametersMatching", "some", "regExp", "test", "generateURLVariations", "directoryIndex", "cleanURLs", "urlManipulation", "hash", "urlWithoutIgnoredParams", "pathname", "endsWith", "directoryURL", "cleanURL", "additionalURLs", "urlToAttempt", "PrecacheRoute", "Route", "urlsTo<PERSON>ache<PERSON><PERSON>s", "possibleURL", "debug", "addRoute", "precacheRoute", "registerRoute", "SUBSTRING_TO_FIND", "deleteOutdatedCaches", "currentPrecacheName", "substringToFind", "cacheNamesToDelete", "filter", "includes", "registration", "scope", "map", "cleanupOutdatedCaches", "then", "cachesDeleted", "precacheAndRoute", "PrecacheFallbackPlugin", "fallbackURL", "handlerDidError", "_fallbackURL"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;IAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA,MAAMC,qBAAqB,GAAG,iBAAiB,CAAA;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASC,cAAcA,CAACC,KAAK,EAAE;MAClC,IAAI,CAACA,KAAK,EAAE;IACR,IAAA,MAAM,IAAIC,4BAAY,CAAC,mCAAmC,EAAE;IAAED,MAAAA,KAAAA;IAAM,KAAC,CAAC,CAAA;IAC1E,GAAA;IACA;IACA;IACA,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,MAAME,SAAS,GAAG,IAAIC,GAAG,CAACH,KAAK,EAAEI,QAAQ,CAACC,IAAI,CAAC,CAAA;QAC/C,OAAO;UACHC,QAAQ,EAAEJ,SAAS,CAACG,IAAI;UACxBE,GAAG,EAAEL,SAAS,CAACG,IAAAA;SAClB,CAAA;IACL,GAAA;MACA,MAAM;QAAEG,QAAQ;IAAED,IAAAA,GAAAA;IAAI,GAAC,GAAGP,KAAK,CAAA;MAC/B,IAAI,CAACO,GAAG,EAAE;IACN,IAAA,MAAM,IAAIN,4BAAY,CAAC,mCAAmC,EAAE;IAAED,MAAAA,KAAAA;IAAM,KAAC,CAAC,CAAA;IAC1E,GAAA;IACA;IACA;MACA,IAAI,CAACQ,QAAQ,EAAE;QACX,MAAMN,SAAS,GAAG,IAAIC,GAAG,CAACI,GAAG,EAAEH,QAAQ,CAACC,IAAI,CAAC,CAAA;QAC7C,OAAO;UACHC,QAAQ,EAAEJ,SAAS,CAACG,IAAI;UACxBE,GAAG,EAAEL,SAAS,CAACG,IAAAA;SAClB,CAAA;IACL,GAAA;IACA;IACA;MACA,MAAMI,WAAW,GAAG,IAAIN,GAAG,CAACI,GAAG,EAAEH,QAAQ,CAACC,IAAI,CAAC,CAAA;MAC/C,MAAMK,WAAW,GAAG,IAAIP,GAAG,CAACI,GAAG,EAAEH,QAAQ,CAACC,IAAI,CAAC,CAAA;MAC/CI,WAAW,CAACE,YAAY,CAACC,GAAG,CAACd,qBAAqB,EAAEU,QAAQ,CAAC,CAAA;MAC7D,OAAO;QACHF,QAAQ,EAAEG,WAAW,CAACJ,IAAI;QAC1BE,GAAG,EAAEG,WAAW,CAACL,IAAAA;OACpB,CAAA;IACL;;ICvDA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMQ,2BAA2B,CAAC;IAC9BC,EAAAA,WAAWA,GAAG;QACV,IAAI,CAACC,WAAW,GAAG,EAAE,CAAA;QACrB,IAAI,CAACC,cAAc,GAAG,EAAE,CAAA;QACxB,IAAI,CAACC,gBAAgB,GAAG,OAAO;UAAEC,OAAO;IAAEC,MAAAA,KAAAA;IAAO,KAAC,KAAK;IACnD;IACA,MAAA,IAAIA,KAAK,EAAE;YACPA,KAAK,CAACC,eAAe,GAAGF,OAAO,CAAA;IACnC,OAAA;SACH,CAAA;QACD,IAAI,CAACG,wBAAwB,GAAG,OAAO;UAAEC,KAAK;UAAEH,KAAK;IAAEI,MAAAA,cAAAA;IAAgB,KAAC,KAAK;IACzE,MAAA,IAAID,KAAK,CAACE,IAAI,KAAK,SAAS,EAAE;YAC1B,IAAIL,KAAK,IACLA,KAAK,CAACC,eAAe,IACrBD,KAAK,CAACC,eAAe,YAAYK,OAAO,EAAE;IAC1C;IACA,UAAA,MAAMlB,GAAG,GAAGY,KAAK,CAACC,eAAe,CAACb,GAAG,CAAA;IACrC,UAAA,IAAIgB,cAAc,EAAE;IAChB,YAAA,IAAI,CAACP,cAAc,CAACU,IAAI,CAACnB,GAAG,CAAC,CAAA;IACjC,WAAC,MACI;IACD,YAAA,IAAI,CAACQ,WAAW,CAACW,IAAI,CAACnB,GAAG,CAAC,CAAA;IAC9B,WAAA;IACJ,SAAA;IACJ,OAAA;IACA,MAAA,OAAOgB,cAAc,CAAA;SACxB,CAAA;IACL,GAAA;IACJ;;IC1CA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMI,sBAAsB,CAAC;IACzBb,EAAAA,WAAWA,CAAC;IAAEc,IAAAA,kBAAAA;IAAmB,GAAC,EAAE;QAChC,IAAI,CAACC,kBAAkB,GAAG,OAAO;UAAEX,OAAO;IAAEY,MAAAA,MAAAA;IAAQ,KAAC,KAAK;IACtD;IACA;IACA,MAAA,MAAMxB,QAAQ,GAAG,CAACwB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACxB,QAAQ,KAC7E,IAAI,CAACyB,mBAAmB,CAACC,iBAAiB,CAACd,OAAO,CAACX,GAAG,CAAC,CAAA;IAC3D;IACA,MAAA,OAAOD,QAAQ,GACT,IAAImB,OAAO,CAACnB,QAAQ,EAAE;YAAE2B,OAAO,EAAEf,OAAO,CAACe,OAAAA;WAAS,CAAC,GACnDf,OAAO,CAAA;SAChB,CAAA;QACD,IAAI,CAACa,mBAAmB,GAAGH,kBAAkB,CAAA;IACjD,GAAA;IACJ;;IC5BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMM,QAAQ,GAAGA,CAACC,UAAU,EAAEC,WAAW,KAAK;IAC1CC,EAAAA,gBAAM,CAACC,cAAc,CAACH,UAAU,CAAC,CAAA;IACjC,EAAA,KAAK,MAAM5B,GAAG,IAAI6B,WAAW,EAAE;IAC3BC,IAAAA,gBAAM,CAACE,GAAG,CAAChC,GAAG,CAAC,CAAA;IACnB,GAAA;MACA8B,gBAAM,CAACG,QAAQ,EAAE,CAAA;IACrB,CAAC,CAAA;IACD;IACA;IACA;IACA;IACA;IACA;IACO,SAASC,mBAAmBA,CAACL,WAAW,EAAE;IAC7C,EAAA,MAAMM,aAAa,GAAGN,WAAW,CAACO,MAAM,CAAA;MACxC,IAAID,aAAa,GAAG,CAAC,EAAE;IACnBL,IAAAA,gBAAM,CAACC,cAAc,CAAE,6BAA4B,GAC9C,CAAA,EAAEI,aAAc,CAAS,QAAA,CAAA,GACzB,CAASA,OAAAA,EAAAA,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG,QAAS,WAAU,CAAC,CAAA;IACjER,IAAAA,QAAQ,CAAC,wBAAwB,EAAEE,WAAW,CAAC,CAAA;QAC/CC,gBAAM,CAACG,QAAQ,EAAE,CAAA;IACrB,GAAA;IACJ;;ICrCA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA,SAASI,YAAYA,CAACT,UAAU,EAAEU,IAAI,EAAE;IACpC,EAAA,IAAIA,IAAI,CAACF,MAAM,KAAK,CAAC,EAAE;IACnB,IAAA,OAAA;IACJ,GAAA;IACAN,EAAAA,gBAAM,CAACC,cAAc,CAACH,UAAU,CAAC,CAAA;IACjC,EAAA,KAAK,MAAM5B,GAAG,IAAIsC,IAAI,EAAE;IACpBR,IAAAA,gBAAM,CAACE,GAAG,CAAChC,GAAG,CAAC,CAAA;IACnB,GAAA;MACA8B,gBAAM,CAACG,QAAQ,EAAE,CAAA;IACrB,CAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASM,mBAAmBA,CAACC,cAAc,EAAEC,oBAAoB,EAAE;IACtE,EAAA,MAAMC,cAAc,GAAGF,cAAc,CAACJ,MAAM,CAAA;IAC5C,EAAA,MAAMO,qBAAqB,GAAGF,oBAAoB,CAACL,MAAM,CAAA;MACzD,IAAIM,cAAc,IAAIC,qBAAqB,EAAE;IACzC,IAAA,IAAIC,OAAO,GAAI,CAAaF,WAAAA,EAAAA,cAAe,CAAOA,KAAAA,EAAAA,cAAc,KAAK,CAAC,GAAG,EAAE,GAAG,GAAI,CAAE,CAAA,CAAA,CAAA;QACpF,IAAIC,qBAAqB,GAAG,CAAC,EAAE;IAC3BC,MAAAA,OAAO,IACF,CAAA,CAAA,EAAGD,qBAAsB,CAAA,CAAA,CAAE,GACvB,CAAA,IAAA,EAAMA,qBAAqB,KAAK,CAAC,GAAG,KAAK,GAAG,OAAQ,CAAiB,gBAAA,CAAA,CAAA;IAClF,KAAA;IACAb,IAAAA,gBAAM,CAACC,cAAc,CAACa,OAAO,CAAC,CAAA;IAC9BP,IAAAA,YAAY,CAAE,CAAA,0BAAA,CAA2B,EAAEG,cAAc,CAAC,CAAA;IAC1DH,IAAAA,YAAY,CAAE,CAAA,+BAAA,CAAgC,EAAEI,oBAAoB,CAAC,CAAA;QACrEX,gBAAM,CAACG,QAAQ,EAAE,CAAA;IACrB,GAAA;IACJ;;IC/CA;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMY,gBAAgB,SAASC,oBAAQ,CAAC;IACpC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIvC,EAAAA,WAAWA,CAACwC,OAAO,GAAG,EAAE,EAAE;QACtBA,OAAO,CAACC,SAAS,GAAGC,wBAAU,CAACC,eAAe,CAACH,OAAO,CAACC,SAAS,CAAC,CAAA;QACjE,KAAK,CAACD,OAAO,CAAC,CAAA;QACd,IAAI,CAACI,kBAAkB,GACnBJ,OAAO,CAACK,iBAAiB,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAA;IACtD;IACA;IACA;IACA;QACA,IAAI,CAACC,OAAO,CAAClC,IAAI,CAAC0B,gBAAgB,CAACS,sCAAsC,CAAC,CAAA;IAC9E,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMC,OAAOA,CAAC5C,OAAO,EAAE6C,OAAO,EAAE;QAC5B,MAAMC,QAAQ,GAAG,MAAMD,OAAO,CAACE,UAAU,CAAC/C,OAAO,CAAC,CAAA;IAClD,IAAA,IAAI8C,QAAQ,EAAE;IACV,MAAA,OAAOA,QAAQ,CAAA;IACnB,KAAA;IACA;IACA;QACA,IAAID,OAAO,CAACzC,KAAK,IAAIyC,OAAO,CAACzC,KAAK,CAACE,IAAI,KAAK,SAAS,EAAE;UACnD,OAAO,MAAM,IAAI,CAAC0C,cAAc,CAAChD,OAAO,EAAE6C,OAAO,CAAC,CAAA;IACtD,KAAA;IACA;IACA;QACA,OAAO,MAAM,IAAI,CAACI,YAAY,CAACjD,OAAO,EAAE6C,OAAO,CAAC,CAAA;IACpD,GAAA;IACA,EAAA,MAAMI,YAAYA,CAACjD,OAAO,EAAE6C,OAAO,EAAE;IACjC,IAAA,IAAIC,QAAQ,CAAA;IACZ,IAAA,MAAMlC,MAAM,GAAIiC,OAAO,CAACjC,MAAM,IAAI,EAAG,CAAA;IACrC;QACA,IAAI,IAAI,CAAC4B,kBAAkB,EAAE;IACzB,MAA2C;IACvCrB,QAAAA,gBAAM,CAAC+B,IAAI,CAAE,6BAA4B,GACpC,CAAA,EAAEC,gCAAc,CAACnD,OAAO,CAACX,GAAG,CAAE,OAAM,IAAI,CAACgD,SAAU,CAAU,SAAA,CAAA,GAC7D,qCAAoC,CAAC,CAAA;IAC9C,OAAA;IACA,MAAA,MAAMe,mBAAmB,GAAGxC,MAAM,CAACyC,SAAS,CAAA;IAC5C,MAAA,MAAMC,kBAAkB,GAAGtD,OAAO,CAACqD,SAAS,CAAA;IAC5C,MAAA,MAAME,mBAAmB,GAAG,CAACD,kBAAkB,IAAIA,kBAAkB,KAAKF,mBAAmB,CAAA;IAC7F;IACA;UACAN,QAAQ,GAAG,MAAMD,OAAO,CAACW,KAAK,CAAC,IAAIjD,OAAO,CAACP,OAAO,EAAE;YAChDqD,SAAS,EAAErD,OAAO,CAACyD,IAAI,KAAK,SAAS,GAC/BH,kBAAkB,IAAIF,mBAAmB,GACzCM,SAAAA;IACV,OAAC,CAAC,CAAC,CAAA;IACH;IACA;IACA;IACA;IACA;IACA;IACA;UACA,IAAIN,mBAAmB,IACnBG,mBAAmB,IACnBvD,OAAO,CAACyD,IAAI,KAAK,SAAS,EAAE;YAC5B,IAAI,CAACE,qCAAqC,EAAE,CAAA;IAC5C,QAAA,MAAMC,SAAS,GAAG,MAAMf,OAAO,CAACgB,QAAQ,CAAC7D,OAAO,EAAE8C,QAAQ,CAACgB,KAAK,EAAE,CAAC,CAAA;IACnE,QAA2C;IACvC,UAAA,IAAIF,SAAS,EAAE;IACXzC,YAAAA,gBAAM,CAACE,GAAG,CAAE,CAAA,eAAA,EAAiB8B,gCAAc,CAACnD,OAAO,CAACX,GAAG,CAAE,CAAE,CAAA,CAAA,GACtD,oCAAmC,CAAC,CAAA;IAC7C,WAAA;IACJ,SAAA;IACJ,OAAA;IACJ,KAAC,MACI;IACD;IACA;IACA,MAAA,MAAM,IAAIN,4BAAY,CAAC,wBAAwB,EAAE;YAC7CsD,SAAS,EAAE,IAAI,CAACA,SAAS;YACzBhD,GAAG,EAAEW,OAAO,CAACX,GAAAA;IACjB,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAA2C;IACvC,MAAA,MAAMD,QAAQ,GAAGwB,MAAM,CAACxB,QAAQ,KAAK,MAAMyD,OAAO,CAACkB,WAAW,CAAC/D,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;IAChF;IACA;UACAmB,gBAAM,CAACC,cAAc,CAAE,CAA8B,6BAAA,CAAA,GAAG+B,gCAAc,CAACnD,OAAO,CAACX,GAAG,CAAC,CAAC,CAAA;IACpF8B,MAAAA,gBAAM,CAACE,GAAG,CAAE,CAA6B8B,2BAAAA,EAAAA,gCAAc,CAAC/D,QAAQ,YAAYmB,OAAO,GAAGnB,QAAQ,CAACC,GAAG,GAAGD,QAAQ,CAAE,EAAC,CAAC,CAAA;IACjH+B,MAAAA,gBAAM,CAACC,cAAc,CAAE,CAAA,0BAAA,CAA2B,CAAC,CAAA;IACnDD,MAAAA,gBAAM,CAACE,GAAG,CAACrB,OAAO,CAAC,CAAA;UACnBmB,gBAAM,CAACG,QAAQ,EAAE,CAAA;IACjBH,MAAAA,gBAAM,CAACC,cAAc,CAAE,CAAA,2BAAA,CAA4B,CAAC,CAAA;IACpDD,MAAAA,gBAAM,CAACE,GAAG,CAACyB,QAAQ,CAAC,CAAA;UACpB3B,gBAAM,CAACG,QAAQ,EAAE,CAAA;UACjBH,gBAAM,CAACG,QAAQ,EAAE,CAAA;IACrB,KAAA;IACA,IAAA,OAAOwB,QAAQ,CAAA;IACnB,GAAA;IACA,EAAA,MAAME,cAAcA,CAAChD,OAAO,EAAE6C,OAAO,EAAE;QACnC,IAAI,CAACc,qCAAqC,EAAE,CAAA;QAC5C,MAAMb,QAAQ,GAAG,MAAMD,OAAO,CAACW,KAAK,CAACxD,OAAO,CAAC,CAAA;IAC7C;IACA;IACA,IAAA,MAAM4D,SAAS,GAAG,MAAMf,OAAO,CAACgB,QAAQ,CAAC7D,OAAO,EAAE8C,QAAQ,CAACgB,KAAK,EAAE,CAAC,CAAA;QACnE,IAAI,CAACF,SAAS,EAAE;IACZ;IACA;IACA,MAAA,MAAM,IAAI7E,4BAAY,CAAC,yBAAyB,EAAE;YAC9CM,GAAG,EAAEW,OAAO,CAACX,GAAG;YAChB2E,MAAM,EAAElB,QAAQ,CAACkB,MAAAA;IACrB,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAOlB,QAAQ,CAAA;IACnB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIa,EAAAA,qCAAqCA,GAAG;QACpC,IAAIM,kBAAkB,GAAG,IAAI,CAAA;QAC7B,IAAIC,0BAA0B,GAAG,CAAC,CAAA;IAClC,IAAA,KAAK,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,IAAI,IAAI,CAAC1B,OAAO,CAAC2B,OAAO,EAAE,EAAE;IAClD;IACA,MAAA,IAAID,MAAM,KAAKlC,gBAAgB,CAACS,sCAAsC,EAAE;IACpE,QAAA,SAAA;IACJ,OAAA;IACA;IACA,MAAA,IAAIyB,MAAM,KAAKlC,gBAAgB,CAACoC,iCAAiC,EAAE;IAC/DL,QAAAA,kBAAkB,GAAGE,KAAK,CAAA;IAC9B,OAAA;UACA,IAAIC,MAAM,CAACG,eAAe,EAAE;IACxBL,QAAAA,0BAA0B,EAAE,CAAA;IAChC,OAAA;IACJ,KAAA;QACA,IAAIA,0BAA0B,KAAK,CAAC,EAAE;UAClC,IAAI,CAACxB,OAAO,CAAClC,IAAI,CAAC0B,gBAAgB,CAACoC,iCAAiC,CAAC,CAAA;SACxE,MACI,IAAIJ,0BAA0B,GAAG,CAAC,IAAID,kBAAkB,KAAK,IAAI,EAAE;IACpE;UACA,IAAI,CAACvB,OAAO,CAAC8B,MAAM,CAACP,kBAAkB,EAAE,CAAC,CAAC,CAAA;IAC9C,KAAA;IACA;IACJ,GAAA;IACJ,CAAA;IACA/B,gBAAgB,CAACoC,iCAAiC,GAAG;IACjD,EAAA,MAAMC,eAAeA,CAAC;IAAEzB,IAAAA,QAAAA;IAAS,GAAC,EAAE;QAChC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACkB,MAAM,IAAI,GAAG,EAAE;IACrC,MAAA,OAAO,IAAI,CAAA;IACf,KAAA;IACA,IAAA,OAAOlB,QAAQ,CAAA;IACnB,GAAA;IACJ,CAAC,CAAA;IACDZ,gBAAgB,CAACS,sCAAsC,GAAG;IACtD,EAAA,MAAM4B,eAAeA,CAAC;IAAEzB,IAAAA,QAAAA;IAAS,GAAC,EAAE;QAChC,OAAOA,QAAQ,CAAC2B,UAAU,GAAG,MAAMC,4BAAY,CAAC5B,QAAQ,CAAC,GAAGA,QAAQ,CAAA;IACxE,GAAA;IACJ,CAAC;;IC7ND;IACA;AACA;IACA;IACA;IACA;IACA;IAaA;IACA;IACA;IACA;IACA;IACA,MAAM6B,kBAAkB,CAAC;IACrB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI/E,EAAAA,WAAWA,CAAC;QAAEyC,SAAS;IAAEK,IAAAA,OAAO,GAAG,EAAE;IAAED,IAAAA,iBAAiB,GAAG,IAAA;OAAO,GAAG,EAAE,EAAE;IACrE,IAAA,IAAI,CAACmC,gBAAgB,GAAG,IAAIC,GAAG,EAAE,CAAA;IACjC,IAAA,IAAI,CAACC,iBAAiB,GAAG,IAAID,GAAG,EAAE,CAAA;IAClC,IAAA,IAAI,CAACE,uBAAuB,GAAG,IAAIF,GAAG,EAAE,CAAA;IACxC,IAAA,IAAI,CAACG,SAAS,GAAG,IAAI9C,gBAAgB,CAAC;IAClCG,MAAAA,SAAS,EAAEC,wBAAU,CAACC,eAAe,CAACF,SAAS,CAAC;IAChDK,MAAAA,OAAO,EAAE,CACL,GAAGA,OAAO,EACV,IAAIjC,sBAAsB,CAAC;IAAEC,QAAAA,kBAAkB,EAAE,IAAA;IAAK,OAAC,CAAC,CAC3D;IACD+B,MAAAA,iBAAAA;IACJ,KAAC,CAAC,CAAA;IACF;QACA,IAAI,CAACwC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC,CAAA;IAC5C,GAAA;IACA;IACJ;IACA;IACA;MACI,IAAIE,QAAQA,GAAG;QACX,OAAO,IAAI,CAACJ,SAAS,CAAA;IACzB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIK,QAAQA,CAAChB,OAAO,EAAE;IACd,IAAA,IAAI,CAACiB,cAAc,CAACjB,OAAO,CAAC,CAAA;IAC5B,IAAA,IAAI,CAAC,IAAI,CAACkB,+BAA+B,EAAE;UACvC9G,IAAI,CAAC+G,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACP,OAAO,CAAC,CAAA;UAC9CxG,IAAI,CAAC+G,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACL,QAAQ,CAAC,CAAA;UAChD,IAAI,CAACI,+BAA+B,GAAG,IAAI,CAAA;IAC/C,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACID,cAAcA,CAACjB,OAAO,EAAE;IACpB,IAA2C;IACvCoB,MAAAA,gBAAM,CAACC,OAAO,CAACrB,OAAO,EAAE;IACpBsB,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,oBAAoB;IAC/BC,QAAAA,QAAQ,EAAE,gBAAgB;IAC1BC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAMC,eAAe,GAAG,EAAE,CAAA;IAC1B,IAAA,KAAK,MAAMjH,KAAK,IAAIuF,OAAO,EAAE;IACzB;IACA,MAAA,IAAI,OAAOvF,KAAK,KAAK,QAAQ,EAAE;IAC3BiH,QAAAA,eAAe,CAACvF,IAAI,CAAC1B,KAAK,CAAC,CAAA;WAC9B,MACI,IAAIA,KAAK,IAAIA,KAAK,CAACQ,QAAQ,KAAKoE,SAAS,EAAE;IAC5CqC,QAAAA,eAAe,CAACvF,IAAI,CAAC1B,KAAK,CAACO,GAAG,CAAC,CAAA;IACnC,OAAA;UACA,MAAM;YAAED,QAAQ;IAAEC,QAAAA,GAAAA;IAAI,OAAC,GAAGR,cAAc,CAACC,KAAK,CAAC,CAAA;IAC/C,MAAA,MAAMkH,SAAS,GAAG,OAAOlH,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACQ,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;IACpF,MAAA,IAAI,IAAI,CAACsF,gBAAgB,CAACqB,GAAG,CAAC5G,GAAG,CAAC,IAC9B,IAAI,CAACuF,gBAAgB,CAACsB,GAAG,CAAC7G,GAAG,CAAC,KAAKD,QAAQ,EAAE;IAC7C,QAAA,MAAM,IAAIL,4BAAY,CAAC,uCAAuC,EAAE;cAC5DoH,UAAU,EAAE,IAAI,CAACvB,gBAAgB,CAACsB,GAAG,CAAC7G,GAAG,CAAC;IAC1C+G,UAAAA,WAAW,EAAEhH,QAAAA;IACjB,SAAC,CAAC,CAAA;IACN,OAAA;UACA,IAAI,OAAON,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACuE,SAAS,EAAE;YAC9C,IAAI,IAAI,CAAC0B,uBAAuB,CAACkB,GAAG,CAAC7G,QAAQ,CAAC,IAC1C,IAAI,CAAC2F,uBAAuB,CAACmB,GAAG,CAAC9G,QAAQ,CAAC,KAAKN,KAAK,CAACuE,SAAS,EAAE;IAChE,UAAA,MAAM,IAAItE,4BAAY,CAAC,2CAA2C,EAAE;IAChEM,YAAAA,GAAAA;IACJ,WAAC,CAAC,CAAA;IACN,SAAA;YACA,IAAI,CAAC0F,uBAAuB,CAACrF,GAAG,CAACN,QAAQ,EAAEN,KAAK,CAACuE,SAAS,CAAC,CAAA;IAC/D,OAAA;UACA,IAAI,CAACuB,gBAAgB,CAAClF,GAAG,CAACL,GAAG,EAAED,QAAQ,CAAC,CAAA;UACxC,IAAI,CAAC0F,iBAAiB,CAACpF,GAAG,CAACL,GAAG,EAAE2G,SAAS,CAAC,CAAA;IAC1C,MAAA,IAAID,eAAe,CAACtE,MAAM,GAAG,CAAC,EAAE;IAC5B,QAAA,MAAM4E,cAAc,GAAI,CAA6C,4CAAA,CAAA,GAChE,CAAQN,MAAAA,EAAAA,eAAe,CAACO,IAAI,CAAC,IAAI,CAAE,CAAA,8BAAA,CAA+B,GAClE,CAAyC,wCAAA,CAAA,CAAA;IAC9C,QAKK;IACDnF,UAAAA,gBAAM,CAAC+B,IAAI,CAACmD,cAAc,CAAC,CAAA;IAC/B,SAAA;IACJ,OAAA;IACJ,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIpB,OAAOA,CAAC7E,KAAK,EAAE;IACX;IACA;IACA,IAAA,OAAOmG,sBAAS,CAACnG,KAAK,EAAE,YAAY;IAChC,MAAA,MAAMoG,mBAAmB,GAAG,IAAI7G,2BAA2B,EAAE,CAAA;UAC7D,IAAI,CAACyF,QAAQ,CAAC1C,OAAO,CAAClC,IAAI,CAACgG,mBAAmB,CAAC,CAAA;IAC/C;IACA;UACA,KAAK,MAAM,CAACnH,GAAG,EAAED,QAAQ,CAAC,IAAI,IAAI,CAACwF,gBAAgB,EAAE;YACjD,MAAMvB,SAAS,GAAG,IAAI,CAAC0B,uBAAuB,CAACmB,GAAG,CAAC9G,QAAQ,CAAC,CAAA;YAC5D,MAAM4G,SAAS,GAAG,IAAI,CAAClB,iBAAiB,CAACoB,GAAG,CAAC7G,GAAG,CAAC,CAAA;IACjD,QAAA,MAAMW,OAAO,GAAG,IAAIO,OAAO,CAAClB,GAAG,EAAE;cAC7BgE,SAAS;IACToD,UAAAA,KAAK,EAAET,SAAS;IAChBU,UAAAA,WAAW,EAAE,aAAA;IACjB,SAAC,CAAC,CAAA;YACF,MAAMC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxB,QAAQ,CAACyB,SAAS,CAAC;IACtCjG,UAAAA,MAAM,EAAE;IAAExB,YAAAA,QAAAA;eAAU;cACpBY,OAAO;IACPI,UAAAA,KAAAA;IACJ,SAAC,CAAC,CAAC,CAAA;IACP,OAAA;UACA,MAAM;YAAEP,WAAW;IAAEC,QAAAA,cAAAA;IAAe,OAAC,GAAG0G,mBAAmB,CAAA;IAC3D,MAA2C;IACvC5E,QAAAA,mBAAmB,CAAC/B,WAAW,EAAEC,cAAc,CAAC,CAAA;IACpD,OAAA;UACA,OAAO;YAAED,WAAW;IAAEC,QAAAA,cAAAA;WAAgB,CAAA;IAC1C,KAAC,CAAC,CAAA;IACN,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIqF,QAAQA,CAAC/E,KAAK,EAAE;IACZ;IACA;IACA,IAAA,OAAOmG,sBAAS,CAACnG,KAAK,EAAE,YAAY;IAChC,MAAA,MAAMqG,KAAK,GAAG,MAAMhI,IAAI,CAACqI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC/C,SAAS,CAAC,CAAA;IAC7D,MAAA,MAAM2E,uBAAuB,GAAG,MAAMP,KAAK,CAACQ,IAAI,EAAE,CAAA;IAClD,MAAA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACvC,gBAAgB,CAACwC,MAAM,EAAE,CAAC,CAAA;UACjE,MAAMlG,WAAW,GAAG,EAAE,CAAA;IACtB,MAAA,KAAK,MAAMlB,OAAO,IAAIgH,uBAAuB,EAAE;YAC3C,IAAI,CAACE,iBAAiB,CAACjB,GAAG,CAACjG,OAAO,CAACX,GAAG,CAAC,EAAE;IACrC,UAAA,MAAMoH,KAAK,CAACY,MAAM,CAACrH,OAAO,CAAC,CAAA;IAC3BkB,UAAAA,WAAW,CAACV,IAAI,CAACR,OAAO,CAACX,GAAG,CAAC,CAAA;IACjC,SAAA;IACJ,OAAA;IACA,MAA2C;YACvCkC,mBAAmB,CAACL,WAAW,CAAC,CAAA;IACpC,OAAA;UACA,OAAO;IAAEA,QAAAA,WAAAA;WAAa,CAAA;IAC1B,KAAC,CAAC,CAAA;IACN,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACIoG,EAAAA,kBAAkBA,GAAG;QACjB,OAAO,IAAI,CAAC1C,gBAAgB,CAAA;IAChC,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACI2C,EAAAA,aAAaA,GAAG;QACZ,OAAO,CAAC,GAAG,IAAI,CAAC3C,gBAAgB,CAACqC,IAAI,EAAE,CAAC,CAAA;IAC5C,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACInG,iBAAiBA,CAACzB,GAAG,EAAE;QACnB,MAAML,SAAS,GAAG,IAAIC,GAAG,CAACI,GAAG,EAAEH,QAAQ,CAACC,IAAI,CAAC,CAAA;QAC7C,OAAO,IAAI,CAACyF,gBAAgB,CAACsB,GAAG,CAAClH,SAAS,CAACG,IAAI,CAAC,CAAA;IACpD,GAAA;IACA;IACJ;IACA;IACA;IACA;MACIqI,uBAAuBA,CAACpI,QAAQ,EAAE;IAC9B,IAAA,OAAO,IAAI,CAAC2F,uBAAuB,CAACmB,GAAG,CAAC9G,QAAQ,CAAC,CAAA;IACrD,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMqI,aAAaA,CAACzH,OAAO,EAAE;QACzB,MAAMX,GAAG,GAAGW,OAAO,YAAYO,OAAO,GAAGP,OAAO,CAACX,GAAG,GAAGW,OAAO,CAAA;IAC9D,IAAA,MAAMZ,QAAQ,GAAG,IAAI,CAAC0B,iBAAiB,CAACzB,GAAG,CAAC,CAAA;IAC5C,IAAA,IAAID,QAAQ,EAAE;IACV,MAAA,MAAMqH,KAAK,GAAG,MAAMhI,IAAI,CAACqI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC/C,SAAS,CAAC,CAAA;IAC7D,MAAA,OAAOoE,KAAK,CAACiB,KAAK,CAACtI,QAAQ,CAAC,CAAA;IAChC,KAAA;IACA,IAAA,OAAOsE,SAAS,CAAA;IACpB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;MACIiE,uBAAuBA,CAACtI,GAAG,EAAE;IACzB,IAAA,MAAMD,QAAQ,GAAG,IAAI,CAAC0B,iBAAiB,CAACzB,GAAG,CAAC,CAAA;QAC5C,IAAI,CAACD,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIL,4BAAY,CAAC,mBAAmB,EAAE;IAAEM,QAAAA,GAAAA;IAAI,OAAC,CAAC,CAAA;IACxD,KAAA;IACA,IAAA,OAAQ+C,OAAO,IAAK;IAChBA,MAAAA,OAAO,CAACpC,OAAO,GAAG,IAAIO,OAAO,CAAClB,GAAG,CAAC,CAAA;IAClC+C,MAAAA,OAAO,CAACxB,MAAM,GAAGgH,MAAM,CAACC,MAAM,CAAC;IAAEzI,QAAAA,QAAAA;IAAS,OAAC,EAAEgD,OAAO,CAACxB,MAAM,CAAC,CAAA;IAC5D,MAAA,OAAO,IAAI,CAACwE,QAAQ,CAAC0C,MAAM,CAAC1F,OAAO,CAAC,CAAA;SACvC,CAAA;IACL,GAAA;IACJ;;IClSA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA,IAAI1B,kBAAkB,CAAA;IACtB;IACA;IACA;IACA;IACO,MAAMqH,6BAA6B,GAAGA,MAAM;MAC/C,IAAI,CAACrH,kBAAkB,EAAE;IACrBA,IAAAA,kBAAkB,GAAG,IAAIiE,kBAAkB,EAAE,CAAA;IACjD,GAAA;IACA,EAAA,OAAOjE,kBAAkB,CAAA;IAC7B,CAAC;;ICnBD;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASsH,UAAUA,CAACtF,OAAO,EAAE;IACzB,EAAA,MAAMhC,kBAAkB,GAAGqH,6BAA6B,EAAE,CAAA;MAC1DrH,kBAAkB,CAAC0E,QAAQ,CAAC1C,OAAO,CAAClC,IAAI,CAAC,GAAGkC,OAAO,CAAC,CAAA;IACxD;;ICnBA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASuF,yBAAyBA,CAACjJ,SAAS,EAAEkJ,2BAA2B,GAAG,EAAE,EAAE;IACnF;IACA;IACA,EAAA,KAAK,MAAMpC,SAAS,IAAI,CAAC,GAAG9G,SAAS,CAACS,YAAY,CAACwH,IAAI,EAAE,CAAC,EAAE;IACxD,IAAA,IAAIiB,2BAA2B,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,IAAI,CAACvC,SAAS,CAAC,CAAC,EAAE;IACtE9G,MAAAA,SAAS,CAACS,YAAY,CAAC4H,MAAM,CAACvB,SAAS,CAAC,CAAA;IAC5C,KAAA;IACJ,GAAA;IACA,EAAA,OAAO9G,SAAS,CAAA;IACpB;;IC7BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,UAAUsJ,qBAAqBA,CAACjJ,GAAG,EAAE;IAAE6I,EAAAA,2BAA2B,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;IAAEK,EAAAA,cAAc,GAAG,YAAY;IAAEC,EAAAA,SAAS,GAAG,IAAI;IAAEC,EAAAA,eAAAA;IAAiB,CAAC,GAAG,EAAE,EAAE;MACzK,MAAMzJ,SAAS,GAAG,IAAIC,GAAG,CAACI,GAAG,EAAEH,QAAQ,CAACC,IAAI,CAAC,CAAA;MAC7CH,SAAS,CAAC0J,IAAI,GAAG,EAAE,CAAA;MACnB,MAAM1J,SAAS,CAACG,IAAI,CAAA;IACpB,EAAA,MAAMwJ,uBAAuB,GAAGV,yBAAyB,CAACjJ,SAAS,EAAEkJ,2BAA2B,CAAC,CAAA;MACjG,MAAMS,uBAAuB,CAACxJ,IAAI,CAAA;MAClC,IAAIoJ,cAAc,IAAII,uBAAuB,CAACC,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClE,MAAMC,YAAY,GAAG,IAAI7J,GAAG,CAAC0J,uBAAuB,CAACxJ,IAAI,CAAC,CAAA;QAC1D2J,YAAY,CAACF,QAAQ,IAAIL,cAAc,CAAA;QACvC,MAAMO,YAAY,CAAC3J,IAAI,CAAA;IAC3B,GAAA;IACA,EAAA,IAAIqJ,SAAS,EAAE;QACX,MAAMO,QAAQ,GAAG,IAAI9J,GAAG,CAAC0J,uBAAuB,CAACxJ,IAAI,CAAC,CAAA;QACtD4J,QAAQ,CAACH,QAAQ,IAAI,OAAO,CAAA;QAC5B,MAAMG,QAAQ,CAAC5J,IAAI,CAAA;IACvB,GAAA;IACA,EAAA,IAAIsJ,eAAe,EAAE;QACjB,MAAMO,cAAc,GAAGP,eAAe,CAAC;IAAEpJ,MAAAA,GAAG,EAAEL,SAAAA;IAAU,KAAC,CAAC,CAAA;IAC1D,IAAA,KAAK,MAAMiK,YAAY,IAAID,cAAc,EAAE;UACvC,MAAMC,YAAY,CAAC9J,IAAI,CAAA;IAC3B,KAAA;IACJ,GAAA;IACJ;;ICzCA;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM+J,aAAa,SAASC,cAAK,CAAC;IAC9B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIvJ,EAAAA,WAAWA,CAACc,kBAAkB,EAAE0B,OAAO,EAAE;QACrC,MAAMsF,KAAK,GAAGA,CAAC;IAAE1H,MAAAA,OAAAA;IAAS,KAAC,KAAK;IAC5B,MAAA,MAAMoJ,eAAe,GAAG1I,kBAAkB,CAAC4G,kBAAkB,EAAE,CAAA;UAC/D,KAAK,MAAM+B,WAAW,IAAIf,qBAAqB,CAACtI,OAAO,CAACX,GAAG,EAAE+C,OAAO,CAAC,EAAE;IACnE,QAAA,MAAMhD,QAAQ,GAAGgK,eAAe,CAAClD,GAAG,CAACmD,WAAW,CAAC,CAAA;IACjD,QAAA,IAAIjK,QAAQ,EAAE;IACV,UAAA,MAAMiE,SAAS,GAAG3C,kBAAkB,CAAC8G,uBAAuB,CAACpI,QAAQ,CAAC,CAAA;cACtE,OAAO;gBAAEA,QAAQ;IAAEiE,YAAAA,SAAAA;eAAW,CAAA;IAClC,SAAA;IACJ,OAAA;IACA,MAA2C;YACvClC,gBAAM,CAACmI,KAAK,CAAE,CAAqC,oCAAA,CAAA,GAAGnG,gCAAc,CAACnD,OAAO,CAACX,GAAG,CAAC,CAAC,CAAA;IACtF,OAAA;IACA,MAAA,OAAA;SACH,CAAA;IACD,IAAA,KAAK,CAACqI,KAAK,EAAEhH,kBAAkB,CAAC0E,QAAQ,CAAC,CAAA;IAC7C,GAAA;IACJ;;ICvDA;IACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASmE,QAAQA,CAACnH,OAAO,EAAE;IACvB,EAAA,MAAM1B,kBAAkB,GAAGqH,6BAA6B,EAAE,CAAA;MAC1D,MAAMyB,aAAa,GAAG,IAAIN,aAAa,CAACxI,kBAAkB,EAAE0B,OAAO,CAAC,CAAA;MACpEqH,8BAAa,CAACD,aAAa,CAAC,CAAA;IAChC;;IC7BA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA,MAAME,iBAAiB,GAAG,YAAY,CAAA;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,oBAAoB,GAAG,OAAOC,mBAAmB,EAAEC,eAAe,GAAGH,iBAAiB,KAAK;MAC7F,MAAMpH,UAAU,GAAG,MAAM7D,IAAI,CAACqI,MAAM,CAACG,IAAI,EAAE,CAAA;IAC3C,EAAA,MAAM6C,kBAAkB,GAAGxH,UAAU,CAACyH,MAAM,CAAE1H,SAAS,IAAK;QACxD,OAAQA,SAAS,CAAC2H,QAAQ,CAACH,eAAe,CAAC,IACvCxH,SAAS,CAAC2H,QAAQ,CAACvL,IAAI,CAACwL,YAAY,CAACC,KAAK,CAAC,IAC3C7H,SAAS,KAAKuH,mBAAmB,CAAA;IACzC,GAAC,CAAC,CAAA;IACF,EAAA,MAAMjD,OAAO,CAACC,GAAG,CAACkD,kBAAkB,CAACK,GAAG,CAAE9H,SAAS,IAAK5D,IAAI,CAACqI,MAAM,CAACO,MAAM,CAAChF,SAAS,CAAC,CAAC,CAAC,CAAA;IACvF,EAAA,OAAOyH,kBAAkB,CAAA;IAC7B,CAAC;;ICpCD;IACA;AACA;IACA;IACA;IACA;IACA;IAKA;IACA;IACA;IACA;IACA;IACA;IACA,SAASM,qBAAqBA,GAAG;IAC7B;IACA3L,EAAAA,IAAI,CAAC+G,gBAAgB,CAAC,UAAU,EAAIpF,KAAK,IAAK;IAC1C,IAAA,MAAMiC,SAAS,GAAGC,wBAAU,CAACC,eAAe,EAAE,CAAA;QAC9CnC,KAAK,CAACmG,SAAS,CAACoD,oBAAoB,CAACtH,SAAS,CAAC,CAACgI,IAAI,CAAEC,aAAa,IAAK;IACpE,MAA2C;IACvC,QAAA,IAAIA,aAAa,CAAC7I,MAAM,GAAG,CAAC,EAAE;cAC1BN,gBAAM,CAACE,GAAG,CAAE,CAAA,oDAAA,CAAqD,GAC5D,CAAe,cAAA,CAAA,EAAEiJ,aAAa,CAAC,CAAA;IACxC,SAAA;IACJ,OAAA;IACJ,KAAC,CAAC,CAAC,CAAA;IACP,GAAE,CAAC,CAAA;IACP;;IC9BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS3C,uBAAuBA,CAACtI,GAAG,EAAE;IAClC,EAAA,MAAMqB,kBAAkB,GAAGqH,6BAA6B,EAAE,CAAA;IAC1D,EAAA,OAAOrH,kBAAkB,CAACiH,uBAAuB,CAACtI,GAAG,CAAC,CAAA;IAC1D;;IC7BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASyB,iBAAiBA,CAACzB,GAAG,EAAE;IAC5B,EAAA,MAAMqB,kBAAkB,GAAGqH,6BAA6B,EAAE,CAAA;IAC1D,EAAA,OAAOrH,kBAAkB,CAACI,iBAAiB,CAACzB,GAAG,CAAC,CAAA;IACpD;;IC/BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASoI,aAAaA,CAACzH,OAAO,EAAE;IAC5B,EAAA,MAAMU,kBAAkB,GAAGqH,6BAA6B,EAAE,CAAA;IAC1D,EAAA,OAAOrH,kBAAkB,CAAC+G,aAAa,CAACzH,OAAO,CAAC,CAAA;IACpD;;IC3BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASqF,QAAQA,CAAChB,OAAO,EAAE;IACvB,EAAA,MAAM3D,kBAAkB,GAAGqH,6BAA6B,EAAE,CAAA;IAC1DrH,EAAAA,kBAAkB,CAAC2E,QAAQ,CAAChB,OAAO,CAAC,CAAA;IACxC;;IC/BA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASkG,gBAAgBA,CAAClG,OAAO,EAAEjC,OAAO,EAAE;MACxCiD,QAAQ,CAAChB,OAAO,CAAC,CAAA;MACjBkF,QAAQ,CAACnH,OAAO,CAAC,CAAA;IACrB;;IC3BA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMoI,sBAAsB,CAAC;IACzB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI5K,EAAAA,WAAWA,CAAC;QAAE6K,WAAW;IAAE/J,IAAAA,kBAAAA;IAAoB,GAAC,EAAE;IAC9C;IACR;IACA;IACA;IACA;IACQ,IAAA,IAAI,CAACgK,eAAe,GAAG,MAAM,IAAI,CAAC7J,mBAAmB,CAAC4G,aAAa,CAAC,IAAI,CAACkD,YAAY,CAAC,CAAA;QACtF,IAAI,CAACA,YAAY,GAAGF,WAAW,CAAA;IAC/B,IAAA,IAAI,CAAC5J,mBAAmB,GACpBH,kBAAkB,IAAIqH,6BAA6B,EAAE,CAAA;IAC7D,GAAA;IACJ;;;;;;;;;;;;;;;;;;;;;"}
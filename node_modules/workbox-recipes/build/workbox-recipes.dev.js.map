{"version": 3, "file": "workbox-recipes.dev.js", "sources": ["../_version.js", "../googleFontsCache.js", "../warmStrategyCache.js", "../imageCache.js", "../staticResourceCache.js", "../pageCache.js", "../offlineFallback.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:recipes:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { StaleWhileRevalidate } from 'workbox-strategies/StaleWhileRevalidate.js';\nimport { CacheFirst } from 'workbox-strategies/CacheFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport { ExpirationPlugin } from 'workbox-expiration/ExpirationPlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [Google fonts]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#google_fonts} caching recipe\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cachePrefix] Cache prefix for caching stylesheets and webfonts. Defaults to google-fonts\n * @param {number} [options.maxAgeSeconds] Maximum age, in seconds, that font entries will be cached for. Defaults to 1 year\n * @param {number} [options.maxEntries] Maximum number of fonts that will be cached. Defaults to 30\n */\nfunction googleFontsCache(options = {}) {\n    const sheetCacheName = `${options.cachePrefix || 'google-fonts'}-stylesheets`;\n    const fontCacheName = `${options.cachePrefix || 'google-fonts'}-webfonts`;\n    const maxAgeSeconds = options.maxAgeSeconds || 60 * 60 * 24 * 365;\n    const maxEntries = options.maxEntries || 30;\n    // Cache the Google Fonts stylesheets with a stale-while-revalidate strategy.\n    registerRoute(({ url }) => url.origin === 'https://fonts.googleapis.com', new StaleWhileRevalidate({\n        cacheName: sheetCacheName,\n    }));\n    // Cache the underlying font files with a cache-first strategy for 1 year.\n    registerRoute(({ url }) => url.origin === 'https://fonts.gstatic.com', new CacheFirst({\n        cacheName: fontCacheName,\n        plugins: [\n            new CacheableResponsePlugin({\n                statuses: [0, 200],\n            }),\n            new ExpirationPlugin({\n                maxAgeSeconds,\n                maxEntries,\n            }),\n        ],\n    }));\n}\nexport { googleFontsCache };\n", "import './_version.js';\n/**\n * @memberof workbox-recipes\n \n * @param {Object} options\n * @param {string[]} options.urls Paths to warm the strategy's cache with\n * @param {Strategy} options.strategy Strategy to use\n */\nfunction warmStrategyCache(options) {\n    self.addEventListener('install', (event) => {\n        const done = options.urls.map((path) => options.strategy.handleAll({\n            event,\n            request: new Request(path),\n        })[1]);\n        event.waitUntil(Promise.all(done));\n    });\n}\nexport { warmStrategyCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { CacheFirst } from 'workbox-strategies/CacheFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport { ExpirationPlugin } from 'workbox-expiration/ExpirationPlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [image caching recipe]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#caching_images}\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to images\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.destination === 'image';\n * @param {number} [options.maxAgeSeconds] Maximum age, in seconds, that font entries will be cached for. Defaults to 30 days\n * @param {number} [options.maxEntries] Maximum number of images that will be cached. Defaults to 60\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction imageCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.destination === 'image';\n    const cacheName = options.cacheName || 'images';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const maxAgeSeconds = options.maxAgeSeconds || 30 * 24 * 60 * 60;\n    const maxEntries = options.maxEntries || 60;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    plugins.push(new ExpirationPlugin({\n        maxEntries,\n        maxAgeSeconds,\n    }));\n    const strategy = new CacheFirst({\n        cacheName,\n        plugins,\n    });\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { imageCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { StaleWhileRevalidate } from 'workbox-strategies/StaleWhileRevalidate.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [CSS and JavaScript files recipe]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#cache_css_and_javascript_files}\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to static-resources\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.destination === 'style' || request.destination === 'script' || request.destination === 'worker';\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction staticResourceCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.destination === 'style' ||\n        request.destination === 'script' ||\n        request.destination === 'worker';\n    const cacheName = options.cacheName || 'static-resources';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    const strategy = new StaleWhileRevalidate({\n        cacheName,\n        plugins,\n    });\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { staticResourceCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { NetworkFirst } from 'workbox-strategies/NetworkFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport './_version.js';\n/**\n * An implementation of a page caching recipe with a network timeout\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to pages\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.mode === 'navigate';\n * @param {number} [options.networkTimoutSeconds] Maximum amount of time, in seconds, to wait on the network before falling back to cache. Defaults to 3\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction pageCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.mode === 'navigate';\n    const cacheName = options.cacheName || 'pages';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const networkTimeoutSeconds = options.networkTimeoutSeconds || 3;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    const strategy = new NetworkFirst({\n        networkTimeoutSeconds,\n        cacheName,\n        plugins,\n    });\n    // Registers the route\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { pageCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { setCatchHandler } from 'workbox-routing/setCatchHandler.js';\nimport { matchPrecache } from 'workbox-precaching/matchPrecache.js';\nimport './_version.js';\n/**\n * An implementation of the [comprehensive fallbacks recipe]{@link https://developers.google.com/web/tools/workbox/guides/advanced-recipes#comprehensive_fallbacks}. Be sure to include the fallbacks in your precache injection\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.pageFallback] Precache name to match for pag fallbacks. Defaults to offline.html\n * @param {string} [options.imageFallback] Precache name to match for image fallbacks.\n * @param {string} [options.fontFallback] Precache name to match for font fallbacks.\n */\nfunction offlineFallback(options = {}) {\n    const pageFallback = options.pageFallback || 'offline.html';\n    const imageFallback = options.imageFallback || false;\n    const fontFallback = options.fontFallback || false;\n    self.addEventListener('install', (event) => {\n        const files = [pageFallback];\n        if (imageFallback) {\n            files.push(imageFallback);\n        }\n        if (fontFallback) {\n            files.push(fontFallback);\n        }\n        event.waitUntil(self.caches\n            .open('workbox-offline-fallbacks')\n            .then((cache) => cache.addAll(files)));\n    });\n    const handler = async (options) => {\n        const dest = options.request.destination;\n        const cache = await self.caches.open('workbox-offline-fallbacks');\n        if (dest === 'document') {\n            const match = (await matchPrecache(pageFallback)) ||\n                (await cache.match(pageFallback));\n            return match || Response.error();\n        }\n        if (dest === 'image' && imageFallback !== false) {\n            const match = (await matchPrecache(imageFallback)) ||\n                (await cache.match(imageFallback));\n            return match || Response.error();\n        }\n        if (dest === 'font' && fontFallback !== false) {\n            const match = (await matchPrecache(fontFallback)) ||\n                (await cache.match(fontFallback));\n            return match || Response.error();\n        }\n        return Response.error();\n    };\n    setCatchHandler(handler);\n}\nexport { offlineFallback };\n"], "names": ["self", "_", "e", "googleFontsCache", "options", "sheetCacheName", "cachePrefix", "fontCacheName", "maxAgeSeconds", "maxEntries", "registerRoute", "url", "origin", "StaleWhileRevalidate", "cacheName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plugins", "CacheableResponsePlugin", "statuses", "ExpirationPlugin", "warmStrategyCache", "addEventListener", "event", "done", "urls", "map", "path", "strategy", "handleAll", "request", "Request", "waitUntil", "Promise", "all", "imageCache", "defaultMatchCallback", "destination", "matchCallback", "push", "warmCache", "staticResourceCache", "pageCache", "mode", "networkTimeoutSeconds", "NetworkFirst", "offlineFallback", "pageFallback", "imageFallback", "fontFallback", "files", "caches", "open", "then", "cache", "addAll", "handler", "dest", "match", "matchPrecache", "Response", "error", "setCatchHandler"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,uBAAuB,CAAC,IAAIC,CAAC,EAAE,CAAA;IACxC,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,gBAAgBA,CAACC,OAAO,GAAG,EAAE,EAAE;MACpC,MAAMC,cAAc,GAAI,CAAED,EAAAA,OAAO,CAACE,WAAW,IAAI,cAAe,CAAa,YAAA,CAAA,CAAA;MAC7E,MAAMC,aAAa,GAAI,CAAEH,EAAAA,OAAO,CAACE,WAAW,IAAI,cAAe,CAAU,SAAA,CAAA,CAAA;IACzE,EAAA,MAAME,aAAa,GAAGJ,OAAO,CAACI,aAAa,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;IACjE,EAAA,MAAMC,UAAU,GAAGL,OAAO,CAACK,UAAU,IAAI,EAAE,CAAA;IAC3C;IACAC,EAAAA,8BAAa,CAAC,CAAC;IAAEC,IAAAA,GAAAA;OAAK,KAAKA,GAAG,CAACC,MAAM,KAAK,8BAA8B,EAAE,IAAIC,4CAAoB,CAAC;IAC/FC,IAAAA,SAAS,EAAET,cAAAA;IACf,GAAC,CAAC,CAAC,CAAA;IACH;IACAK,EAAAA,8BAAa,CAAC,CAAC;IAAEC,IAAAA,GAAAA;OAAK,KAAKA,GAAG,CAACC,MAAM,KAAK,2BAA2B,EAAE,IAAIG,wBAAU,CAAC;IAClFD,IAAAA,SAAS,EAAEP,aAAa;IACxBS,IAAAA,OAAO,EAAE,CACL,IAAIC,kDAAuB,CAAC;IACxBC,MAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAA;IACrB,KAAC,CAAC,EACF,IAAIC,oCAAgB,CAAC;UACjBX,aAAa;IACbC,MAAAA,UAAAA;IACJ,KAAC,CAAC,CAAA;IAEV,GAAC,CAAC,CAAC,CAAA;IACP;;IC5CA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASW,iBAAiBA,CAAChB,OAAO,EAAE;IAChCJ,EAAAA,IAAI,CAACqB,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAK;IACxC,IAAA,MAAMC,IAAI,GAAGnB,OAAO,CAACoB,IAAI,CAACC,GAAG,CAAEC,IAAI,IAAKtB,OAAO,CAACuB,QAAQ,CAACC,SAAS,CAAC;UAC/DN,KAAK;IACLO,MAAAA,OAAO,EAAE,IAAIC,OAAO,CAACJ,IAAI,CAAA;IAC7B,KAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACNJ,KAAK,CAACS,SAAS,CAACC,OAAO,CAACC,GAAG,CAACV,IAAI,CAAC,CAAC,CAAA;IACtC,GAAC,CAAC,CAAA;IACN;;IChBA;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASW,UAAUA,CAAC9B,OAAO,GAAG,EAAE,EAAE;MAC9B,MAAM+B,oBAAoB,GAAGA,CAAC;IAAEN,IAAAA,OAAAA;IAAQ,GAAC,KAAKA,OAAO,CAACO,WAAW,KAAK,OAAO,CAAA;IAC7E,EAAA,MAAMtB,SAAS,GAAGV,OAAO,CAACU,SAAS,IAAI,QAAQ,CAAA;IAC/C,EAAA,MAAMuB,aAAa,GAAGjC,OAAO,CAACiC,aAAa,IAAIF,oBAAoB,CAAA;IACnE,EAAA,MAAM3B,aAAa,GAAGJ,OAAO,CAACI,aAAa,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAA;IAChE,EAAA,MAAMC,UAAU,GAAGL,OAAO,CAACK,UAAU,IAAI,EAAE,CAAA;IAC3C,EAAA,MAAMO,OAAO,GAAGZ,OAAO,CAACY,OAAO,IAAI,EAAE,CAAA;IACrCA,EAAAA,OAAO,CAACsB,IAAI,CAAC,IAAIrB,kDAAuB,CAAC;IACrCC,IAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAA;IACrB,GAAC,CAAC,CAAC,CAAA;IACHF,EAAAA,OAAO,CAACsB,IAAI,CAAC,IAAInB,oCAAgB,CAAC;QAC9BV,UAAU;IACVD,IAAAA,aAAAA;IACJ,GAAC,CAAC,CAAC,CAAA;IACH,EAAA,MAAMmB,QAAQ,GAAG,IAAIZ,wBAAU,CAAC;QAC5BD,SAAS;IACTE,IAAAA,OAAAA;IACJ,GAAC,CAAC,CAAA;IACFN,EAAAA,8BAAa,CAAC2B,aAAa,EAAEV,QAAQ,CAAC,CAAA;IACtC;MACA,IAAIvB,OAAO,CAACmC,SAAS,EAAE;IACnBnB,IAAAA,iBAAiB,CAAC;UAAEI,IAAI,EAAEpB,OAAO,CAACmC,SAAS;IAAEZ,MAAAA,QAAAA;IAAS,KAAC,CAAC,CAAA;IAC5D,GAAA;IACJ;;ICjDA;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASa,mBAAmBA,CAACpC,OAAO,GAAG,EAAE,EAAE;MACvC,MAAM+B,oBAAoB,GAAGA,CAAC;IAAEN,IAAAA,OAAAA;IAAQ,GAAC,KAAKA,OAAO,CAACO,WAAW,KAAK,OAAO,IACzEP,OAAO,CAACO,WAAW,KAAK,QAAQ,IAChCP,OAAO,CAACO,WAAW,KAAK,QAAQ,CAAA;IACpC,EAAA,MAAMtB,SAAS,GAAGV,OAAO,CAACU,SAAS,IAAI,kBAAkB,CAAA;IACzD,EAAA,MAAMuB,aAAa,GAAGjC,OAAO,CAACiC,aAAa,IAAIF,oBAAoB,CAAA;IACnE,EAAA,MAAMnB,OAAO,GAAGZ,OAAO,CAACY,OAAO,IAAI,EAAE,CAAA;IACrCA,EAAAA,OAAO,CAACsB,IAAI,CAAC,IAAIrB,kDAAuB,CAAC;IACrCC,IAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAA;IACrB,GAAC,CAAC,CAAC,CAAA;IACH,EAAA,MAAMS,QAAQ,GAAG,IAAId,4CAAoB,CAAC;QACtCC,SAAS;IACTE,IAAAA,OAAAA;IACJ,GAAC,CAAC,CAAA;IACFN,EAAAA,8BAAa,CAAC2B,aAAa,EAAEV,QAAQ,CAAC,CAAA;IACtC;MACA,IAAIvB,OAAO,CAACmC,SAAS,EAAE;IACnBnB,IAAAA,iBAAiB,CAAC;UAAEI,IAAI,EAAEpB,OAAO,CAACmC,SAAS;IAAEZ,MAAAA,QAAAA;IAAS,KAAC,CAAC,CAAA;IAC5D,GAAA;IACJ;;IC1CA;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASc,SAASA,CAACrC,OAAO,GAAG,EAAE,EAAE;MAC7B,MAAM+B,oBAAoB,GAAGA,CAAC;IAAEN,IAAAA,OAAAA;IAAQ,GAAC,KAAKA,OAAO,CAACa,IAAI,KAAK,UAAU,CAAA;IACzE,EAAA,MAAM5B,SAAS,GAAGV,OAAO,CAACU,SAAS,IAAI,OAAO,CAAA;IAC9C,EAAA,MAAMuB,aAAa,GAAGjC,OAAO,CAACiC,aAAa,IAAIF,oBAAoB,CAAA;IACnE,EAAA,MAAMQ,qBAAqB,GAAGvC,OAAO,CAACuC,qBAAqB,IAAI,CAAC,CAAA;IAChE,EAAA,MAAM3B,OAAO,GAAGZ,OAAO,CAACY,OAAO,IAAI,EAAE,CAAA;IACrCA,EAAAA,OAAO,CAACsB,IAAI,CAAC,IAAIrB,kDAAuB,CAAC;IACrCC,IAAAA,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,CAAA;IACrB,GAAC,CAAC,CAAC,CAAA;IACH,EAAA,MAAMS,QAAQ,GAAG,IAAIiB,4BAAY,CAAC;QAC9BD,qBAAqB;QACrB7B,SAAS;IACTE,IAAAA,OAAAA;IACJ,GAAC,CAAC,CAAA;IACF;IACAN,EAAAA,8BAAa,CAAC2B,aAAa,EAAEV,QAAQ,CAAC,CAAA;IACtC;MACA,IAAIvB,OAAO,CAACmC,SAAS,EAAE;IACnBnB,IAAAA,iBAAiB,CAAC;UAAEI,IAAI,EAAEpB,OAAO,CAACmC,SAAS;IAAEZ,MAAAA,QAAAA;IAAS,KAAC,CAAC,CAAA;IAC5D,GAAA;IACJ;;IC5CA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASkB,eAAeA,CAACzC,OAAO,GAAG,EAAE,EAAE;IACnC,EAAA,MAAM0C,YAAY,GAAG1C,OAAO,CAAC0C,YAAY,IAAI,cAAc,CAAA;IAC3D,EAAA,MAAMC,aAAa,GAAG3C,OAAO,CAAC2C,aAAa,IAAI,KAAK,CAAA;IACpD,EAAA,MAAMC,YAAY,GAAG5C,OAAO,CAAC4C,YAAY,IAAI,KAAK,CAAA;IAClDhD,EAAAA,IAAI,CAACqB,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAK;IACxC,IAAA,MAAM2B,KAAK,GAAG,CAACH,YAAY,CAAC,CAAA;IAC5B,IAAA,IAAIC,aAAa,EAAE;IACfE,MAAAA,KAAK,CAACX,IAAI,CAACS,aAAa,CAAC,CAAA;IAC7B,KAAA;IACA,IAAA,IAAIC,YAAY,EAAE;IACdC,MAAAA,KAAK,CAACX,IAAI,CAACU,YAAY,CAAC,CAAA;IAC5B,KAAA;QACA1B,KAAK,CAACS,SAAS,CAAC/B,IAAI,CAACkD,MAAM,CACtBC,IAAI,CAAC,2BAA2B,CAAC,CACjCC,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACC,MAAM,CAACL,KAAK,CAAC,CAAC,CAAC,CAAA;IAC9C,GAAC,CAAC,CAAA;IACF,EAAA,MAAMM,OAAO,GAAG,MAAOnD,OAAO,IAAK;IAC/B,IAAA,MAAMoD,IAAI,GAAGpD,OAAO,CAACyB,OAAO,CAACO,WAAW,CAAA;QACxC,MAAMiB,KAAK,GAAG,MAAMrD,IAAI,CAACkD,MAAM,CAACC,IAAI,CAAC,2BAA2B,CAAC,CAAA;QACjE,IAAIK,IAAI,KAAK,UAAU,EAAE;IACrB,MAAA,MAAMC,KAAK,GAAG,CAAC,MAAMC,8BAAa,CAACZ,YAAY,CAAC,MAC3C,MAAMO,KAAK,CAACI,KAAK,CAACX,YAAY,CAAC,CAAC,CAAA;IACrC,MAAA,OAAOW,KAAK,IAAIE,QAAQ,CAACC,KAAK,EAAE,CAAA;IACpC,KAAA;IACA,IAAA,IAAIJ,IAAI,KAAK,OAAO,IAAIT,aAAa,KAAK,KAAK,EAAE;IAC7C,MAAA,MAAMU,KAAK,GAAG,CAAC,MAAMC,8BAAa,CAACX,aAAa,CAAC,MAC5C,MAAMM,KAAK,CAACI,KAAK,CAACV,aAAa,CAAC,CAAC,CAAA;IACtC,MAAA,OAAOU,KAAK,IAAIE,QAAQ,CAACC,KAAK,EAAE,CAAA;IACpC,KAAA;IACA,IAAA,IAAIJ,IAAI,KAAK,MAAM,IAAIR,YAAY,KAAK,KAAK,EAAE;IAC3C,MAAA,MAAMS,KAAK,GAAG,CAAC,MAAMC,8BAAa,CAACV,YAAY,CAAC,MAC3C,MAAMK,KAAK,CAACI,KAAK,CAACT,YAAY,CAAC,CAAC,CAAA;IACrC,MAAA,OAAOS,KAAK,IAAIE,QAAQ,CAACC,KAAK,EAAE,CAAA;IACpC,KAAA;IACA,IAAA,OAAOD,QAAQ,CAACC,KAAK,EAAE,CAAA;OAC1B,CAAA;MACDC,kCAAe,CAACN,OAAO,CAAC,CAAA;IAC5B;;;;;;;;;;;;;;;"}
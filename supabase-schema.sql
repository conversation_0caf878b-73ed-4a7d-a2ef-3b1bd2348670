-- Tabela para armazenar os enigmas
CREATE TABLE enigmas (
  id BIGSERIAL PRIMARY KEY,
  titulo TEXT NOT NULL,
  pergunta TEXT NOT NULL,
  resposta TEXT NOT NULL,
  dicas JSONB NOT NULL DEFAULT '[]',
  resolvido BOOLEAN NOT NULL DEFAULT false,
  ordem INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela para armazenar o progresso do jogo
CREATE TABLE progresso_jogo (
  id BIGSERIAL PRIMARY KEY,
  enigmas_resolvidos INTEGER[] NOT NULL DEFAULT '{}',
  chaves_coletadas INTEGER NOT NULL DEFAULT 0,
  jogo_completo BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para melhor performance
CREATE INDEX idx_enigmas_ordem ON enigmas(ordem);
CREATE INDEX idx_enigmas_resolvido ON enigmas(resolvido);

-- RLS (Row Level Security) - Opcional, para controle de acesso
ALTER TABLE enigmas ENABLE ROW LEVEL SECURITY;
ALTER TABLE progresso_jogo ENABLE ROW LEVEL SECURITY;

-- Políticas de acesso (permite leitura pública, escrita apenas autenticada)
-- Para um app simples, você pode permitir acesso total:
CREATE POLICY "Permitir acesso total aos enigmas" ON enigmas
  FOR ALL USING (true);

CREATE POLICY "Permitir acesso total ao progresso" ON progresso_jogo
  FOR ALL USING (true);

-- Inserir enigmas iniciais
INSERT INTO enigmas (titulo, pergunta, resposta, dicas, ordem) VALUES
(
  'Nosso Primeiro Encontro',
  'Em que lugar nos conhecemos pela primeira vez?',
  'faculdade',
  '["Foi em um lugar onde estudamos...", "Tem muitos jovens por lá...", "É um lugar de aprendizado...", "Rima com \"cidade\"..."]',
  1
),
(
  'Nossa Primeira Conversa',
  'Qual foi o primeiro assunto que conversamos?',
  'musica',
  '["Foi sobre algo que amamos ouvir...", "Tem ritmo e melodia...", "Pode ser rock, pop, sertanejo...", "Rima com \"harmônica\"..."]',
  2
),
(
  'Nosso Primeiro Momento Especial',
  'Onde foi nosso primeiro encontro romântico?',
  'cinema',
  '["Foi um lugar escuro...", "Assistimos algo juntos...", "Tem pipoca e refrigerante...", "Rima com \"tema\"..."]',
  3
),
(
  'O Que Mais Amo em Você',
  'Qual é a qualidade que mais amo em você?',
  'sorriso',
  '["É algo que você faz com o rosto...", "Ilumina meu dia...", "Mostra seus dentes brancos...", "Rima com \"brilho\"..."]',
  4
),
(
  'Nossa Música',
  'Qual é a música que sempre me faz lembrar de você?',
  'perfect',
  '["É de um cantor inglês famoso...", "Fala sobre ser perfeito...", "Ed Sheeran canta...", "Rima com \"effect\"..."]',
  5
),
(
  'Nossos Sonhos Juntos',
  'Qual é o nosso maior sonho como casal?',
  'viajar',
  '["Envolve sair de casa...", "Conhecer lugares novos...", "Fazer as malas...", "Rima com \"chegar\"..."]',
  6
),
(
  'Nosso Lugar Especial',
  'Qual é o lugar onde nossos corações se conectaram?',
  'praia',
  '["Tem areia...", "Tem ondas do mar...", "É perfeito para o pôr do sol...", "Rima com \"faia\"..."]',
  7
);

-- Função para atualizar o timestamp automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at automaticamente
CREATE TRIGGER update_enigmas_updated_at BEFORE UPDATE ON enigmas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_progresso_updated_at BEFORE UPDATE ON progresso_jogo
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

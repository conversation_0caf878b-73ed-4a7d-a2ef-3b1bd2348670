/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'romantic': ['Pacifico', 'cursive'],
        'sans': ['Quicksand', 'sans-serif'],
      },
      colors: {
        'love-pink': '#ec4899',
        'love-purple': '#a855f7',
        'love-light': '#fdf2f8',
        'love-accent': '#f472b6',
      },
      animation: {
        'float-up': 'float-up 15s infinite linear',
        'sparkle': 'sparkle 2s infinite',
      },
      keyframes: {
        'float-up': {
          '0%': {
            transform: 'translateY(100vh) rotate(0deg)',
            opacity: '0',
          },
          '10%': {
            opacity: '0.3',
          },
          '90%': {
            opacity: '0.3',
          },
          '100%': {
            transform: 'translateY(-100px) rotate(360deg)',
            opacity: '0',
          },
        },
        'sparkle': {
          '0%, 100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
          '50%': {
            opacity: '0.5',
            transform: 'scale(1.1)',
          },
        },
      },
    },
  },
  plugins: [
    require('daisyui'),
  ],
  daisyui: {
    themes: [
      {
        love: {
          "primary": "#ec4899",
          "secondary": "#a855f7",
          "accent": "#f472b6",
          "neutral": "#374151",
          "base-100": "#ffffff",
          "base-200": "#f9fafb",
          "base-300": "#f3f4f6",
          "info": "#3b82f6",
          "success": "#10b981",
          "warning": "#f59e0b",
          "error": "#ef4444",
        },
      },
      "light",
    ],
    base: true,
    styled: true,
    utils: true,
  },
}

{"name": "package.json", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "daisyui": "^5.0.43", "npm-run-all2": "^7.0.2", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-pwa": "^1.0.0", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}
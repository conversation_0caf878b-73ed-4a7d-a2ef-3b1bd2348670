# 🚀 Configuração Supabase - Choose Me App

## 📋 **Passo a Passo para Configurar o Backend Serverless**

### 1. **Criar Conta no Supabase**
1. Acesse [https://supabase.com](https://supabase.com)
2. Clique em "Start your project"
3. Faça login com GitHub ou crie uma conta
4. Clique em "New Project"

### 2. **Configurar Projeto**
1. **Nome do Projeto:** `choose-me-app`
2. **Senha do Banco:** Crie uma senha forte (anote!)
3. **Região:** Escolha a mais próxima (ex: South America)
4. **Plano:** Free (suficiente para o app)
5. Clique em "Create new project"

### 3. **Obter Credenciais**
1. Aguarde o projeto ser criado (1-2 minutos)
2. Vá em **Settings** → **API**
3. Copie:
   - **Project URL** (ex: `https://abc123.supabase.co`)
   - **anon public key** (chave longa que começa com `eyJ...`)

### 4. **Configurar Variáveis de Ambiente**
1. Copie o arquivo `.env.example` para `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edite o arquivo `.env` com suas credenciais:
   ```env
   VITE_SUPABASE_URL=https://seu-projeto-id.supabase.co
   VITE_SUPABASE_ANON_KEY=sua-chave-anon-aqui
   ```

### 5. **Criar Tabelas no Banco**
1. No Supabase, vá em **SQL Editor**
2. Clique em "New query"
3. Copie e cole todo o conteúdo do arquivo `supabase-schema.sql`
4. Clique em "Run" para executar

### 6. **Verificar Tabelas**
1. Vá em **Table Editor**
2. Você deve ver as tabelas:
   - `enigmas` (com 7 enigmas pré-cadastrados)
   - `progresso_jogo` (vazia inicialmente)

### 7. **Testar Conexão**
1. Reinicie o servidor de desenvolvimento:
   ```bash
   npm run dev
   ```
2. Acesse `/admin` no navegador
3. Você deve ver os enigmas carregados do Supabase!

## 🔧 **Funcionalidades Serverless Implementadas**

### **Gerenciamento de Enigmas:**
- ✅ **CRUD Completo** (Create, Read, Update, Delete)
- ✅ **Persistência** no PostgreSQL
- ✅ **Ordenação** automática por ordem
- ✅ **Validação** de dados

### **Sistema de Progresso:**
- ✅ **Tracking** de enigmas resolvidos
- ✅ **Contagem** de chaves coletadas
- ✅ **Status** do jogo (completo/incompleto)
- ✅ **Persistência** entre sessões

### **API Automática:**
- ✅ **REST API** gerada automaticamente
- ✅ **Real-time** (opcional)
- ✅ **Autenticação** (configurável)
- ✅ **Row Level Security** (RLS)

## 📊 **Estrutura do Banco**

### **Tabela: enigmas**
```sql
- id (BIGSERIAL PRIMARY KEY)
- titulo (TEXT) - Título do enigma
- pergunta (TEXT) - Pergunta a ser respondida
- resposta (TEXT) - Resposta correta
- dicas (JSONB) - Array de dicas
- resolvido (BOOLEAN) - Status de resolução
- ordem (INTEGER) - Ordem de apresentação
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

### **Tabela: progresso_jogo**
```sql
- id (BIGSERIAL PRIMARY KEY)
- enigmas_resolvidos (INTEGER[]) - IDs dos enigmas resolvidos
- chaves_coletadas (INTEGER) - Número de chaves
- jogo_completo (BOOLEAN) - Status do jogo
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

## 🌟 **Vantagens da Solução Serverless**

### **Custo Zero:**
- ✅ **Supabase Free Tier:** 500MB storage, 2GB bandwidth
- ✅ **Sem servidor** para manter
- ✅ **Escalabilidade** automática

### **Desenvolvimento Rápido:**
- ✅ **API REST** automática
- ✅ **TypeScript** types gerados
- ✅ **Real-time** subscriptions
- ✅ **Dashboard** visual

### **Produção Ready:**
- ✅ **Backups** automáticos
- ✅ **SSL** incluído
- ✅ **CDN** global
- ✅ **Monitoramento** integrado

## 🚀 **Deploy para Produção**

### **Opções de Hosting:**
1. **Vercel** (Recomendado)
2. **Netlify**
3. **GitHub Pages**
4. **Firebase Hosting**

### **Configuração de Deploy:**
1. Faça push do código para GitHub
2. Conecte com Vercel/Netlify
3. Configure as variáveis de ambiente
4. Deploy automático a cada commit!

## 🔒 **Segurança**

### **Row Level Security (RLS):**
- ✅ Configurado para acesso público (app simples)
- ✅ Pode ser restringido por usuário se necessário
- ✅ Políticas customizáveis

### **Variáveis de Ambiente:**
- ✅ Chaves sensíveis não expostas no código
- ✅ Diferentes ambientes (dev/prod)
- ✅ Rotação de chaves facilitada

## 📱 **Compatibilidade PWA**

### **Offline First:**
- ✅ Cache automático via service worker
- ✅ Dados persistem offline
- ✅ Sincronização quando online

### **Performance:**
- ✅ Queries otimizadas
- ✅ Índices no banco
- ✅ Cache de assets

---

**🎉 Pronto! Seu app Choose Me agora é 100% serverless e está pronto para produção!**

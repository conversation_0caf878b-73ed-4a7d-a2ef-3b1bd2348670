import sharp from 'sharp';
import fs from 'fs';
import path from 'path';

// Ícone SVG base
const iconSvg = `<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="512" height="512" rx="80" fill="url(#backgroundGradient)"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fce7f3;stop-opacity:1" />
      <stop offset="20%" style="stop-color:#f9a8d4;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="heartShadow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#be185d;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#831843;stop-opacity:0.8" />
    </linearGradient>
    
    <linearGradient id="heartHighlight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- Heart shadow -->
  <path 
    d="M256 420c-6-5-50-40-100-90-40-40-70-80-70-130 0-50 40-90 90-90 30 0 56 16 80 40 24-24 50-40 80-40 50 0 90 40 90 90 0 50-30 90-70 130-50 50-94 85-100 90z" 
    fill="url(#heartShadow)"
    transform="translate(6, 6)"
  />
  
  <!-- Main heart -->
  <path 
    d="M256 410c-6-5-50-40-100-90-40-40-70-80-70-130 0-50 40-90 90-90 30 0 56 16 80 40 24-24 50-40 80-40 50 0 90 40 90 90 0 50-30 90-70 130-50 50-94 85-100 90z" 
    fill="url(#heartGradient)"
  />
  
  <!-- Heart highlight -->
  <path 
    d="M256 410c-6-5-50-40-100-90-40-40-70-80-70-130 0-50 40-90 90-90 30 0 56 16 80 40 24-24 50-40 80-40 50 0 90 40 90 90 0 50-30 90-70 130-50 50-94 85-100 90z" 
    fill="url(#heartHighlight)"
    opacity="0.3"
  />
  
  <!-- Top highlight -->
  <ellipse 
    cx="210" 
    cy="150" 
    rx="25" 
    ry="12" 
    fill="white" 
    opacity="0.8"
    transform="rotate(-20 210 150)"
  />
</svg>`;

// Tamanhos necessários para iOS e Android
const sizes = [
  { name: 'apple-touch-icon', size: 180 },
  { name: 'apple-touch-icon-152', size: 152 },
  { name: 'apple-touch-icon-144', size: 144 },
  { name: 'apple-touch-icon-120', size: 120 },
  { name: 'apple-touch-icon-114', size: 114 },
  { name: 'apple-touch-icon-76', size: 76 },
  { name: 'apple-touch-icon-72', size: 72 },
  { name: 'apple-touch-icon-60', size: 60 },
  { name: 'apple-touch-icon-57', size: 57 },
  { name: 'icon-192', size: 192 },
  { name: 'icon-512', size: 512 },
  { name: 'favicon-32', size: 32 },
  { name: 'favicon-16', size: 16 }
];

async function generateIcons() {
  const publicDir = path.join(process.cwd(), 'public');
  
  for (const { name, size } of sizes) {
    try {
      await sharp(Buffer.from(iconSvg))
        .resize(size, size)
        .png()
        .toFile(path.join(publicDir, `${name}.png`));
      
      console.log(`✅ Generated ${name}.png (${size}x${size})`);
    } catch (error) {
      console.error(`❌ Error generating ${name}.png:`, error);
    }
  }
  
  console.log('🎉 All icons generated successfully!');
}

generateIcons();

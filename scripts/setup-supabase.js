#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.join(__dirname, '..')

console.log('🚀 Choose Me - Setup Supabase\n')

// Verificar se o .env existe
const envPath = path.join(projectRoot, '.env')
const envExamplePath = path.join(projectRoot, '.env.example')

if (!fs.existsSync(envPath)) {
  console.log('📋 Criando arquivo .env...')
  
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath)
    console.log('✅ Arquivo .env criado a partir do .env.example')
  } else {
    const defaultEnv = `# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Para configurar:
# 1. Acesse https://supabase.com
# 2. Crie um novo projeto
# 3. Vá em Settings > API
# 4. Substitua os valores acima
`
    fs.writeFileSync(envPath, defaultEnv)
    console.log('✅ Arquivo .env criado com valores padrão')
  }
} else {
  console.log('📋 Arquivo .env já existe')
}

console.log('\n📖 Próximos passos:')
console.log('1. 🌐 Acesse https://supabase.com e crie um projeto')
console.log('2. 📝 Edite o arquivo .env com suas credenciais')
console.log('3. 🗄️  Execute o SQL do arquivo supabase-schema.sql no Supabase')
console.log('4. 🚀 Execute: npm run dev')

console.log('\n📚 Documentação completa: SUPABASE_SETUP.md')

// Verificar se as dependências estão instaladas
const packageJsonPath = path.join(projectRoot, 'package.json')
const nodeModulesPath = path.join(projectRoot, 'node_modules')

if (!fs.existsSync(nodeModulesPath)) {
  console.log('\n⚠️  Dependências não instaladas!')
  console.log('Execute: npm install')
} else {
  console.log('\n✅ Dependências instaladas')
}

// Verificar se o Supabase está configurado
const envContent = fs.readFileSync(envPath, 'utf8')
if (envContent.includes('your-project-id') || envContent.includes('your-anon-key')) {
  console.log('\n⚠️  Configure suas credenciais do Supabase no arquivo .env')
} else {
  console.log('\n✅ Credenciais do Supabase configuradas')
}

console.log('\n🎉 Setup concluído! Bom desenvolvimento!')

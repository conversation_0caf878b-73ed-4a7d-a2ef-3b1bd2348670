<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg512" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ee5a24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="512" height="512" rx="64" fill="url(#bg512)"/>
  
  <!-- Heart -->
  <path d="M256 400c-45-40-120-100-120-160 0-40 30-70 70-70 20 0 35 10 50 25 15-15 30-25 50-25 40 0 70 30 70 70 0 60-75 120-120 160z" fill="white"/>
  
  <!-- Text -->
  <text x="256" y="300" font-family="Arial, sans-serif" font-size="64" font-weight="bold" text-anchor="middle" fill="white">Choose</text>
  <text x="256" y="370" font-family="Arial, sans-serif" font-size="64" font-weight="bold" text-anchor="middle" fill="white">Me</text>
</svg>

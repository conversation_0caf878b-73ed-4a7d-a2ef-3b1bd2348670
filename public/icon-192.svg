<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="192" height="192" rx="32" fill="url(#backgroundGradient)"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fce7f3;stop-opacity:1" />
      <stop offset="20%" style="stop-color:#f9a8d4;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="heartShadow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#be185d;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#831843;stop-opacity:0.8" />
    </linearGradient>
    
    <linearGradient id="heartHighlight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- Heart shadow -->
  <path 
    d="M96 158c-2-2-18-15-37-34-15-15-26-30-26-49 0-19 15-34 34-34 11 0 21 6 30 15 9-9 19-15 30-15 19 0 34 15 34 34 0 19-11 34-26 49-19 19-35 32-37 34z" 
    fill="url(#heartShadow)"
    transform="translate(2, 2)"
  />
  
  <!-- Main heart -->
  <path 
    d="M96 154c-2-2-18-15-37-34-15-15-26-30-26-49 0-19 15-34 34-34 11 0 21 6 30 15 9-9 19-15 30-15 19 0 34 15 34 34 0 19-11 34-26 49-19 19-35 32-37 34z" 
    fill="url(#heartGradient)"
  />
  
  <!-- Heart highlight -->
  <path 
    d="M96 154c-2-2-18-15-37-34-15-15-26-30-26-49 0-19 15-34 34-34 11 0 21 6 30 15 9-9 19-15 30-15 19 0 34 15 34 34 0 19-11 34-26 49-19 19-35 32-37 34z" 
    fill="url(#heartHighlight)"
    opacity="0.3"
  />
  
  <!-- Top highlight -->
  <ellipse 
    cx="79" 
    cy="85" 
    rx="9" 
    ry="4" 
    fill="white" 
    opacity="0.8"
    transform="rotate(-20 79 85)"
  />
</svg>

<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="32" height="32" rx="6" fill="url(#backgroundGradient)"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fce7f3;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Heart -->
  <path 
    d="M16 26c-0.5-0.5-3-2.5-6-5.5-2.5-2.5-4-5-4-8 0-3 2.5-5.5 5.5-5.5 2 0 3.5 1 5 2.5 1.5-1.5 3-2.5 5-2.5 3 0 5.5 2.5 5.5 5.5 0 3-1.5 5.5-4 8-3 3-5.5 5-6 5.5z" 
    fill="url(#heartGradient)"
  />
  
  <!-- Highlight -->
  <ellipse 
    cx="13" 
    cy="12" 
    rx="2" 
    ry="1" 
    fill="white" 
    opacity="0.6"
    transform="rotate(-20 13 12)"
  />
</svg>

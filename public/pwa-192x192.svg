<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg192" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ee5a24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="192" height="192" rx="24" fill="url(#bg192)"/>
  
  <!-- Heart -->
  <path d="M96 150c-18-16-48-40-48-64 0-16 12-28 28-28 8 0 14 4 20 10 6-6 12-10 20-10 16 0 28 12 28 28 0 24-30 48-48 64z" fill="white"/>
  
  <!-- Text -->
  <text x="96" y="110" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">Choose</text>
  <text x="96" y="135" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">Me</text>
</svg>

import{d as S,f as l,g as V,k as w,c as d,n as x,a as e,t as u,b as v,w as p,r as C,u as I,e as g,o as n}from"./index-BDYoTXYr.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";const N={class:"min-h-screen flex items-center justify-center p-4"},A={key:0,class:"max-w-2xl mx-auto text-center space-y-8"},D={class:"card bg-gradient-to-br from-pink-50 to-red-50 shadow-2xl border border-pink-200"},M={class:"card-body space-y-6"},P={class:"text-3xl font-bold text-center text-pink-700"},j={class:"text-center space-y-4"},E={class:"flex justify-center gap-6 mt-8"},B={key:1,class:"max-w-2xl mx-auto text-center space-y-8"},O={class:"card bg-gradient-to-br from-green-50 to-emerald-50 shadow-2xl border border-green-200"},R={class:"card-body text-center space-y-6"},T={class:"space-y-4"},$={class:"text-xl font-bold text-green-600"},z={key:2,class:"max-w-2xl mx-auto text-center space-y-8"},J={class:"card bg-gradient-to-br from-red-50 to-pink-50 shadow-2xl border border-red-200"},L={class:"card-body text-center space-y-6"},Q={class:"space-y-4"},F=S({__name:"PedidoView",setup(G){const b=I(),r=l(!1),i=l(!1),c=l("Você quer namorar comigo? 💖"),f=V(()=>new Date().toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})),m=o=>{if(r.value=!0,i.value=o,o){const t=new Date;localStorage.setItem("chooseme-inicio-namoro",t.toISOString()),localStorage.setItem("chooseme-namoro-aceito","true"),_()}},y=()=>{r.value=!1,i.value=!1},_=()=>{console.log("🎉 CELEBRANDO! 🎉")},h=()=>{const o=JSON.parse(localStorage.getItem("chooseme-progresso")||"{}"),t=5;let a=0;for(let s=1;s<=t;s++)o[`enigma_${s}`]&&a++;a<t&&b.push("/namorada")},k=()=>{const o=localStorage.getItem("chooseme-config");if(o){const t=JSON.parse(o);t.mensagemPedido&&(c.value=t.mensagemPedido)}};return w(()=>{h(),k()}),(o,t)=>{const a=C("router-link");return n(),d("div",N,[r.value?i.value?(n(),d("div",B,[t[9]||(t[9]=e("div",{class:"text-center space-y-6"},[e("div",{class:"text-9xl animate-bounce"},"🎉"),e("h1",{class:"text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-emerald-600"}," SIM!!! "),e("p",{class:"text-2xl text-gray-700"}," Você disse SIM! Meu coração está explodindo de felicidade! 💖 ")],-1)),e("div",O,[e("div",R,[t[7]||(t[7]=e("h2",{class:"text-3xl font-bold text-green-700"}," Agora somos oficialmente namorados! 💑 ",-1)),t[8]||(t[8]=e("p",{class:"text-lg text-gray-700"}," Este é o início da nossa história de amor oficial! Vamos criar muitas memórias incríveis juntos! ✨ ",-1)),e("div",T,[e("p",$," Data do nosso namoro: "+u(f.value),1),v(a,{to:"/namorada/cronometro",class:"btn btn-primary btn-lg"},{default:p(()=>t[6]||(t[6]=[g(" Ver Nosso Cronômetro de Amor ⏰💕 ")])),_:1,__:[6]})])])]),t[10]||(t[10]=x('<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-4xl" data-v-65d60768><div class="animate-bounce delay-100" data-v-65d60768>💖</div><div class="animate-bounce delay-200" data-v-65d60768>💕</div><div class="animate-bounce delay-300" data-v-65d60768>💝</div><div class="animate-bounce delay-400" data-v-65d60768>💗</div></div>',1))])):(n(),d("div",z,[t[13]||(t[13]=e("div",{class:"text-center space-y-6"},[e("div",{class:"text-9xl"},"💔"),e("h1",{class:"text-4xl font-bold text-red-600"}," Que pena... "),e("p",{class:"text-xl text-gray-700"}," Tudo bem, eu entendo. Talvez não seja o momento certo ainda. ")],-1)),e("div",J,[e("div",L,[t[12]||(t[12]=e("p",{class:"text-lg text-gray-700"}," Mesmo assim, quero que saiba que você é muito especial para mim. Vou continuar te amando e respeitando sua decisão. 💕 ",-1)),e("div",Q,[e("button",{onClick:y,class:"btn btn-primary"}," Talvez eu tenha mudado de ideia... 💭 "),v(a,{to:"/namorada",class:"btn btn-outline"},{default:p(()=>t[11]||(t[11]=[g(" Voltar ao Início ")])),_:1,__:[11]})])])])])):(n(),d("div",A,[t[4]||(t[4]=x('<div class="relative" data-v-65d60768><div class="text-8xl animate-pulse" data-v-65d60768>💖</div><div class="absolute -top-4 -left-4 text-4xl animate-bounce delay-100" data-v-65d60768>💕</div><div class="absolute -top-4 -right-4 text-4xl animate-bounce delay-200" data-v-65d60768>💝</div><div class="absolute -bottom-4 -left-4 text-4xl animate-bounce delay-300" data-v-65d60768>💗</div><div class="absolute -bottom-4 -right-4 text-4xl animate-bounce delay-400" data-v-65d60768>💘</div></div><div class="space-y-6" data-v-65d60768><h1 class="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-red-600" data-v-65d60768> Parabéns, Meu Amor! </h1><p class="text-xl text-gray-700 leading-relaxed" data-v-65d60768> Você conseguiu resolver todos os enigmas e coletou todas as chaves! 🔑✨<br data-v-65d60768> Cada resposta mostrou o quanto você se lembra dos nossos momentos especiais. </p></div>',2)),e("div",D,[e("div",M,[e("h2",P,u(c.value),1),t[3]||(t[3]=e("p",{class:"text-lg text-center text-gray-700"}," Depois de toda essa jornada juntos, de todos os momentos que compartilhamos, e de todo o amor que sinto por você... ",-1)),e("div",j,[t[2]||(t[2]=e("p",{class:"text-2xl font-bold text-pink-600"}," Você aceita namorar comigo? 💕 ",-1)),e("div",E,[e("button",{onClick:t[0]||(t[0]=s=>m(!0)),class:"btn btn-success btn-lg text-white hover:scale-110 transform transition-all duration-300"}," 💖 SIM! 💖 "),e("button",{onClick:t[1]||(t[1]=s=>m(!1)),class:"btn btn-error btn-lg text-white hover:scale-110 transform transition-all duration-300"}," 💔 Não... ")])])])]),t[5]||(t[5]=e("div",{class:"text-center"},[e("p",{class:"text-gray-600 italic"},' "Você é a resposta para todas as perguntas que eu nem sabia que tinha." ✨ ')],-1))]))])}}}),U=q(F,[["__scopeId","data-v-65d60768"]]);export{U as default};

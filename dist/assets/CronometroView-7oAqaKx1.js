import{d as I,f as c,g as x,k as R,C as S,c as r,a as t,t as e,F as D,h as T,e as _,b as V,w as A,r as j,o as l}from"./index-BDYoTXYr.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";const q={class:"min-h-screen flex items-center justify-center p-4"},L={key:0,class:"max-w-4xl mx-auto text-center space-y-8"},U={class:"card bg-gradient-to-br from-pink-50 to-red-50 shadow-2xl border border-pink-200"},z={class:"card-body"},E={class:"grid grid-cols-1 md:grid-cols-4 gap-6 text-center"},F={class:"space-y-2"},H={class:"text-5xl font-bold text-pink-600"},J={class:"text-lg font-semibold text-gray-700"},O={class:"space-y-2"},P={class:"text-5xl font-bold text-red-600"},$={class:"text-lg font-semibold text-gray-700"},G={class:"space-y-2"},K={class:"text-5xl font-bold text-purple-600"},Q={class:"text-lg font-semibold text-gray-700"},W={class:"space-y-2"},X={class:"text-5xl font-bold text-indigo-600 animate-pulse"},Y={class:"text-lg font-semibold text-gray-700"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-6 text-center"},tt={class:"space-y-2"},st={class:"text-2xl font-bold text-pink-600"},et={class:"space-y-2"},ot={class:"text-2xl font-bold text-red-600"},at={class:"card bg-base-100 shadow-xl"},it={class:"card-body"},dt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},nt={class:"card-body text-center p-4"},rt={class:"text-3xl mb-2"},lt={class:"font-bold text-sm"},ct={class:"text-xs text-gray-600 mt-1"},mt={key:0,class:"badge badge-success badge-sm mt-2"},ut={key:1,class:"text-xs text-gray-500 mt-2"},gt={class:"card bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-xl"},xt={class:"card-body text-center"},vt={class:"text-lg italic"},pt={class:"text-center"},ft={class:"text-gray-600"},_t={key:1,class:"max-w-2xl mx-auto text-center space-y-8"},bt={class:"card bg-base-100 shadow-xl"},ht={class:"card-body text-center"},yt=I({__name:"CronometroView",setup(wt){const v=c(!1),o=c(null),a=c({dias:0,horas:0,minutos:0,segundos:0}),u=c(null),m=["Cada momento com você é mágico ✨","Você faz meu coração bater mais forte 💓","Nosso amor cresce a cada segundo ⏰💕","Você é minha pessoa favorita no mundo 🌍💖","Juntos somos infinito ♾️💝","Você é a razão do meu sorriso 😊💕","Nosso amor é a coisa mais linda que existe 🌹","Cada dia com você é uma nova aventura 🚀💖","Você é meu para sempre 💍💕","Nosso amor é eterno como as estrelas ⭐💫"],g=c(m[0]),p=c([{id:1,titulo:"Primeiro Dia",descricao:"24 horas de amor",icone:"🌅",diasNecessarios:1,atingido:!1,tempoRestante:""},{id:2,titulo:"Uma Semana",descricao:"7 dias juntos",icone:"📅",diasNecessarios:7,atingido:!1,tempoRestante:""},{id:3,titulo:"Um Mês",descricao:"30 dias de felicidade",icone:"🗓️",diasNecessarios:30,atingido:!1,tempoRestante:""},{id:4,titulo:"100 Dias",descricao:"Centena de amor",icone:"💯",diasNecessarios:100,atingido:!1,tempoRestante:""},{id:5,titulo:"6 Meses",descricao:"Meio ano juntos",icone:"💑",diasNecessarios:180,atingido:!1,tempoRestante:""},{id:6,titulo:"Um Ano",descricao:"365 dias de amor",icone:"🎂",diasNecessarios:365,atingido:!1,tempoRestante:""}]),b=x(()=>{if(!o.value)return 0;const s=new Date().getTime()-o.value.getTime();return Math.floor(s/(1e3*60*60))}),h=x(()=>{if(!o.value)return 0;const s=new Date().getTime()-o.value.getTime();return Math.floor(s/(1e3*60))}),y=x(()=>o.value?o.value.toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}):""),f=()=>{if(!o.value)return;const s=new Date().getTime()-o.value.getTime(),n=Math.floor(s/(1e3*60*60*24)),d=Math.floor(s%(1e3*60*60*24)/(1e3*60*60)),N=Math.floor(s%(1e3*60*60)/(1e3*60)),C=Math.floor(s%(1e3*60)/1e3);a.value={dias:n,horas:d,minutos:N,segundos:C},w(n)},w=i=>{p.value.forEach(s=>{if(i>=s.diasNecessarios)s.atingido=!0,s.tempoRestante="Atingido!";else{s.atingido=!1;const n=s.diasNecessarios-i;s.tempoRestante=`${n} ${n===1?"dia":"dias"}`}})},M=()=>{const s=(m.indexOf(g.value)+1)%m.length;g.value=m[s]},k=()=>{const i=localStorage.getItem("chooseme-namoro-aceito"),s=localStorage.getItem("chooseme-inicio-namoro");i==="true"&&s&&(v.value=!0,o.value=new Date(s),f(),u.value=window.setInterval(f,1e3))};return R(()=>{k()}),S(()=>{u.value&&clearInterval(u.value)}),(i,s)=>{const n=j("router-link");return l(),r("div",q,[v.value?(l(),r("div",L,[s[11]||(s[11]=t("div",{class:"space-y-4"},[t("h1",{class:"text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-red-600"}," 💕 Nosso Tempo Juntos 💕 "),t("p",{class:"text-xl text-gray-700"},' Desde que você disse "SIM!" ✨ ')],-1)),t("div",U,[t("div",z,[t("div",E,[t("div",F,[t("div",H,e(a.value.dias),1),t("div",J,e(a.value.dias===1?"Dia":"Dias"),1),s[0]||(s[0]=t("div",{class:"text-sm text-gray-500"},"de amor",-1))]),t("div",O,[t("div",P,e(a.value.horas),1),t("div",$,e(a.value.horas===1?"Hora":"Horas"),1),s[1]||(s[1]=t("div",{class:"text-sm text-gray-500"},"de felicidade",-1))]),t("div",G,[t("div",K,e(a.value.minutos),1),t("div",Q,e(a.value.minutos===1?"Minuto":"Minutos"),1),s[2]||(s[2]=t("div",{class:"text-sm text-gray-500"},"de carinho",-1))]),t("div",W,[t("div",X,e(a.value.segundos),1),t("div",Y,e(a.value.segundos===1?"Segundo":"Segundos"),1),s[3]||(s[3]=t("div",{class:"text-sm text-gray-500"},"de amor",-1))])]),s[6]||(s[6]=t("div",{class:"divider my-8"},null,-1)),t("div",Z,[t("div",tt,[t("div",st,e(b.value.toLocaleString()),1),s[4]||(s[4]=t("div",{class:"text-sm text-gray-600"},"Total de horas juntos",-1))]),t("div",et,[t("div",ot,e(h.value.toLocaleString()),1),s[5]||(s[5]=t("div",{class:"text-sm text-gray-600"},"Total de minutos de amor",-1))])])])]),t("div",at,[t("div",it,[s[7]||(s[7]=t("h2",{class:"card-title justify-center text-2xl mb-6"},"🏆 Marcos do Nosso Amor",-1)),t("div",dt,[(l(!0),r(D,null,T(p.value,d=>(l(),r("div",{key:d.id,class:"card bg-gradient-to-br from-pink-100 to-purple-100 shadow-md"},[t("div",nt,[t("div",rt,e(d.icone),1),t("h3",lt,e(d.titulo),1),t("p",ct,e(d.descricao),1),d.atingido?(l(),r("div",mt,"Atingido!")):(l(),r("div",ut,"Em "+e(d.tempoRestante),1))])]))),128))])])]),t("div",gt,[t("div",xt,[s[8]||(s[8]=t("h2",{class:"text-2xl font-bold mb-4"},"💌 Mensagem do Coração",-1)),t("p",vt,e(g.value),1),t("div",{class:"mt-4"},[t("button",{onClick:M,class:"btn btn-outline btn-white"}," Nova Mensagem 💕 ")])])]),t("div",pt,[t("p",ft,[s[9]||(s[9]=t("strong",null,"Início do nosso namoro:",-1)),_(" "+e(y.value),1)]),s[10]||(s[10]=t("p",{class:"text-sm text-gray-500 mt-2"},' "Cada segundo com você é um presente" ✨ ',-1))])])):(l(),r("div",_t,[t("div",bt,[t("div",ht,[s[13]||(s[13]=t("h2",{class:"text-3xl font-bold text-gray-600 mb-4"},"⏰ Cronômetro Bloqueado",-1)),s[14]||(s[14]=t("p",{class:"text-lg text-gray-700 mb-6"}," O cronômetro do nosso amor só será ativado quando você aceitar meu pedido de namoro! 💕 ",-1)),V(n,{to:"/namorada/pedido",class:"btn btn-primary"},{default:A(()=>s[12]||(s[12]=[_(" Ir para o Pedido 💖 ")])),_:1,__:[12]})])])]))])}}}),Nt=B(yt,[["__scopeId","data-v-537a93cf"]]);export{Nt as default};

const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AdminLayout-BHMInFHR.js","assets/auth-daSeXAUl.js","assets/AdminLoginView-BRciyepv.js","assets/PedidoView-DSxZNCJ8.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/PedidoView-ZhvNpYe-.css","assets/CronometroView-7oAqaKx1.js","assets/CronometroView-B7K6QmtD.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function vs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Z={},At=[],Ke=()=>{},_i=()=>!1,Tn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),bs=e=>e.startsWith("onUpdate:"),he=Object.assign,Es=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},yi=Object.prototype.hasOwnProperty,J=(e,t)=>yi.call(e,t),F=Array.isArray,Ot=e=>on(e)==="[object Map]",Dt=e=>on(e)==="[object Set]",Ws=e=>on(e)==="[object Date]",H=e=>typeof e=="function",ie=e=>typeof e=="string",Ue=e=>typeof e=="symbol",ne=e=>e!==null&&typeof e=="object",Ur=e=>(ne(e)||H(e))&&H(e.then)&&H(e.catch),Br=Object.prototype.toString,on=e=>Br.call(e),vi=e=>on(e).slice(8,-1),Wr=e=>on(e)==="[object Object]",xs=e=>ie(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,kt=vs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),In=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},bi=/-(\w)/g,Me=In(e=>e.replace(bi,(t,n)=>n?n.toUpperCase():"")),Ei=/\B([A-Z])/g,pt=In(e=>e.replace(Ei,"-$1").toLowerCase()),Mn=In(e=>e.charAt(0).toUpperCase()+e.slice(1)),qn=In(e=>e?`on${Mn(e)}`:""),ft=(e,t)=>!Object.is(e,t),gn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},qr=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},bn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let qs;const Ln=()=>qs||(qs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ss(e){if(F(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ie(s)?Ri(s):Ss(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ie(e)||ne(e))return e}const xi=/;(?![^(]*\))/g,Si=/:([^]+)/,wi=/\/\*[^]*?\*\//g;function Ri(e){const t={};return e.replace(wi,"").split(xi).forEach(n=>{if(n){const s=n.split(Si);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function ws(e){let t="";if(ie(e))t=e;else if(F(e))for(let n=0;n<e.length;n++){const s=ws(e[n]);s&&(t+=s+" ")}else if(ne(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Pi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ci=vs(Pi);function Gr(e){return!!e||e===""}function Ai(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=ln(e[s],t[s]);return n}function ln(e,t){if(e===t)return!0;let n=Ws(e),s=Ws(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Ue(e),s=Ue(t),n||s)return e===t;if(n=F(e),s=F(t),n||s)return n&&s?Ai(e,t):!1;if(n=ne(e),s=ne(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!ln(e[i],t[i]))return!1}}return String(e)===String(t)}function Rs(e,t){return e.findIndex(n=>ln(n,t))}const zr=e=>!!(e&&e.__v_isRef===!0),Oi=e=>ie(e)?e:e==null?"":F(e)||ne(e)&&(e.toString===Br||!H(e.toString))?zr(e)?Oi(e.value):JSON.stringify(e,Jr,2):String(e),Jr=(e,t)=>zr(t)?Jr(e,t.value):Ot(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Gn(s,o)+" =>"]=r,n),{})}:Dt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Gn(n))}:Ue(t)?Gn(t):ne(t)&&!F(t)&&!Wr(t)?String(t):t,Gn=(e,t="")=>{var n;return Ue(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let fe;class Qr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=fe,!t&&fe&&(this.index=(fe.scopes||(fe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=fe;try{return fe=this,t()}finally{fe=n}}}on(){++this._on===1&&(this.prevScope=fe,fe=this)}off(){this._on>0&&--this._on===0&&(fe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Yr(e){return new Qr(e)}function Xr(){return fe}function Ti(e,t=!1){fe&&fe.cleanups.push(e)}let te;const zn=new WeakSet;class Zr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,fe&&fe.active&&fe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,zn.has(this)&&(zn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||to(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gs(this),no(this);const t=te,n=Le;te=this,Le=!0;try{return this.fn()}finally{so(this),te=t,Le=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)As(t);this.deps=this.depsTail=void 0,Gs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?zn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){os(this)&&this.run()}get dirty(){return os(this)}}let eo=0,Kt,Ut;function to(e,t=!1){if(e.flags|=8,t){e.next=Ut,Ut=e;return}e.next=Kt,Kt=e}function Ps(){eo++}function Cs(){if(--eo>0)return;if(Ut){let t=Ut;for(Ut=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Kt;){let t=Kt;for(Kt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function no(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function so(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),As(s),Ii(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function os(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ro(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ro(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Yt)||(e.globalVersion=Yt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!os(e))))return;e.flags|=2;const t=e.dep,n=te,s=Le;te=e,Le=!0;try{no(e);const r=e.fn(e._value);(t.version===0||ft(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{te=n,Le=s,so(e),e.flags&=-3}}function As(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)As(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ii(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Le=!0;const oo=[];function Ze(){oo.push(Le),Le=!1}function et(){const e=oo.pop();Le=e===void 0?!0:e}function Gs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=te;te=void 0;try{t()}finally{te=n}}}let Yt=0;class Mi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Os{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!te||!Le||te===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==te)n=this.activeLink=new Mi(te,this),te.deps?(n.prevDep=te.depsTail,te.depsTail.nextDep=n,te.depsTail=n):te.deps=te.depsTail=n,io(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=te.depsTail,n.nextDep=void 0,te.depsTail.nextDep=n,te.depsTail=n,te.deps===n&&(te.deps=s)}return n}trigger(t){this.version++,Yt++,this.notify(t)}notify(t){Ps();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Cs()}}}function io(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)io(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const En=new WeakMap,vt=Symbol(""),is=Symbol(""),Xt=Symbol("");function ae(e,t,n){if(Le&&te){let s=En.get(e);s||En.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Os),r.map=s,r.key=n),r.track()}}function Qe(e,t,n,s,r,o){const i=En.get(e);if(!i){Yt++;return}const l=c=>{c&&c.trigger()};if(Ps(),t==="clear")i.forEach(l);else{const c=F(e),h=c&&xs(n);if(c&&n==="length"){const u=Number(s);i.forEach((a,g)=>{(g==="length"||g===Xt||!Ue(g)&&g>=u)&&l(a)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),h&&l(i.get(Xt)),t){case"add":c?h&&l(i.get("length")):(l(i.get(vt)),Ot(e)&&l(i.get(is)));break;case"delete":c||(l(i.get(vt)),Ot(e)&&l(i.get(is)));break;case"set":Ot(e)&&l(i.get(vt));break}}Cs()}function Li(e,t){const n=En.get(e);return n&&n.get(t)}function wt(e){const t=q(e);return t===e?t:(ae(t,"iterate",Xt),Te(e)?t:t.map(ce))}function jn(e){return ae(e=q(e),"iterate",Xt),e}const ji={__proto__:null,[Symbol.iterator](){return Jn(this,Symbol.iterator,ce)},concat(...e){return wt(this).concat(...e.map(t=>F(t)?wt(t):t))},entries(){return Jn(this,"entries",e=>(e[1]=ce(e[1]),e))},every(e,t){return qe(this,"every",e,t,void 0,arguments)},filter(e,t){return qe(this,"filter",e,t,n=>n.map(ce),arguments)},find(e,t){return qe(this,"find",e,t,ce,arguments)},findIndex(e,t){return qe(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return qe(this,"findLast",e,t,ce,arguments)},findLastIndex(e,t){return qe(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return qe(this,"forEach",e,t,void 0,arguments)},includes(...e){return Qn(this,"includes",e)},indexOf(...e){return Qn(this,"indexOf",e)},join(e){return wt(this).join(e)},lastIndexOf(...e){return Qn(this,"lastIndexOf",e)},map(e,t){return qe(this,"map",e,t,void 0,arguments)},pop(){return Ft(this,"pop")},push(...e){return Ft(this,"push",e)},reduce(e,...t){return zs(this,"reduce",e,t)},reduceRight(e,...t){return zs(this,"reduceRight",e,t)},shift(){return Ft(this,"shift")},some(e,t){return qe(this,"some",e,t,void 0,arguments)},splice(...e){return Ft(this,"splice",e)},toReversed(){return wt(this).toReversed()},toSorted(e){return wt(this).toSorted(e)},toSpliced(...e){return wt(this).toSpliced(...e)},unshift(...e){return Ft(this,"unshift",e)},values(){return Jn(this,"values",ce)}};function Jn(e,t,n){const s=jn(e),r=s[t]();return s!==e&&!Te(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Di=Array.prototype;function qe(e,t,n,s,r,o){const i=jn(e),l=i!==e&&!Te(e),c=i[t];if(c!==Di[t]){const a=c.apply(e,o);return l?ce(a):a}let h=n;i!==e&&(l?h=function(a,g){return n.call(this,ce(a),g,e)}:n.length>2&&(h=function(a,g){return n.call(this,a,g,e)}));const u=c.call(i,h,s);return l&&r?r(u):u}function zs(e,t,n,s){const r=jn(e);let o=n;return r!==e&&(Te(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,ce(l),c,e)}),r[t](o,...s)}function Qn(e,t,n){const s=q(e);ae(s,"iterate",Xt);const r=s[t](...n);return(r===-1||r===!1)&&Ms(n[0])?(n[0]=q(n[0]),s[t](...n)):r}function Ft(e,t,n=[]){Ze(),Ps();const s=q(e)[t].apply(e,n);return Cs(),et(),s}const Ni=vs("__proto__,__v_isRef,__isVue"),lo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ue));function Fi(e){Ue(e)||(e=String(e));const t=q(this);return ae(t,"has",e),t.hasOwnProperty(e)}class co{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?Gi:ho:o?ao:fo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=F(t);if(!r){let c;if(i&&(c=ji[n]))return c;if(n==="hasOwnProperty")return Fi}const l=Reflect.get(t,n,oe(t)?t:s);return(Ue(n)?lo.has(n):Ni(n))||(r||ae(t,"get",n),o)?l:oe(l)?i&&xs(n)?l:l.value:ne(l)?r?go(l):cn(l):l}}class uo extends co{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=dt(o);if(!Te(s)&&!dt(s)&&(o=q(o),s=q(s)),!F(t)&&oe(o)&&!oe(s))return c?!1:(o.value=s,!0)}const i=F(t)&&xs(n)?Number(n)<t.length:J(t,n),l=Reflect.set(t,n,s,oe(t)?t:r);return t===q(r)&&(i?ft(s,o)&&Qe(t,"set",n,s):Qe(t,"add",n,s)),l}deleteProperty(t,n){const s=J(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Qe(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Ue(n)||!lo.has(n))&&ae(t,"has",n),s}ownKeys(t){return ae(t,"iterate",F(t)?"length":vt),Reflect.ownKeys(t)}}class $i extends co{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Vi=new uo,Hi=new $i,ki=new uo(!0);const ls=e=>e,dn=e=>Reflect.getPrototypeOf(e);function Ki(e,t,n){return function(...s){const r=this.__v_raw,o=q(r),i=Ot(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,h=r[e](...s),u=n?ls:t?xn:ce;return!t&&ae(o,"iterate",c?is:vt),{next(){const{value:a,done:g}=h.next();return g?{value:a,done:g}:{value:l?[u(a[0]),u(a[1])]:u(a),done:g}},[Symbol.iterator](){return this}}}}function hn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Ui(e,t){const n={get(r){const o=this.__v_raw,i=q(o),l=q(r);e||(ft(r,l)&&ae(i,"get",r),ae(i,"get",l));const{has:c}=dn(i),h=t?ls:e?xn:ce;if(c.call(i,r))return h(o.get(r));if(c.call(i,l))return h(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&ae(q(r),"iterate",vt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=q(o),l=q(r);return e||(ft(r,l)&&ae(i,"has",r),ae(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=q(l),h=t?ls:e?xn:ce;return!e&&ae(c,"iterate",vt),l.forEach((u,a)=>r.call(o,h(u),h(a),i))}};return he(n,e?{add:hn("add"),set:hn("set"),delete:hn("delete"),clear:hn("clear")}:{add(r){!t&&!Te(r)&&!dt(r)&&(r=q(r));const o=q(this);return dn(o).has.call(o,r)||(o.add(r),Qe(o,"add",r,r)),this},set(r,o){!t&&!Te(o)&&!dt(o)&&(o=q(o));const i=q(this),{has:l,get:c}=dn(i);let h=l.call(i,r);h||(r=q(r),h=l.call(i,r));const u=c.call(i,r);return i.set(r,o),h?ft(o,u)&&Qe(i,"set",r,o):Qe(i,"add",r,o),this},delete(r){const o=q(this),{has:i,get:l}=dn(o);let c=i.call(o,r);c||(r=q(r),c=i.call(o,r)),l&&l.call(o,r);const h=o.delete(r);return c&&Qe(o,"delete",r,void 0),h},clear(){const r=q(this),o=r.size!==0,i=r.clear();return o&&Qe(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Ki(r,e,t)}),n}function Ts(e,t){const n=Ui(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(J(n,r)&&r in s?n:s,r,o)}const Bi={get:Ts(!1,!1)},Wi={get:Ts(!1,!0)},qi={get:Ts(!0,!1)};const fo=new WeakMap,ao=new WeakMap,ho=new WeakMap,Gi=new WeakMap;function zi(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ji(e){return e.__v_skip||!Object.isExtensible(e)?0:zi(vi(e))}function cn(e){return dt(e)?e:Is(e,!1,Vi,Bi,fo)}function po(e){return Is(e,!1,ki,Wi,ao)}function go(e){return Is(e,!0,Hi,qi,ho)}function Is(e,t,n,s,r){if(!ne(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Ji(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function at(e){return dt(e)?at(e.__v_raw):!!(e&&e.__v_isReactive)}function dt(e){return!!(e&&e.__v_isReadonly)}function Te(e){return!!(e&&e.__v_isShallow)}function Ms(e){return e?!!e.__v_raw:!1}function q(e){const t=e&&e.__v_raw;return t?q(t):e}function Ls(e){return!J(e,"__v_skip")&&Object.isExtensible(e)&&qr(e,"__v_skip",!0),e}const ce=e=>ne(e)?cn(e):e,xn=e=>ne(e)?go(e):e;function oe(e){return e?e.__v_isRef===!0:!1}function Dn(e){return mo(e,!1)}function Qi(e){return mo(e,!0)}function mo(e,t){return oe(e)?e:new Yi(e,t)}class Yi{constructor(t,n){this.dep=new Os,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:q(t),this._value=n?t:ce(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Te(t)||dt(t);t=s?t:q(t),ft(t,n)&&(this._rawValue=t,this._value=s?t:ce(t),this.dep.trigger())}}function bt(e){return oe(e)?e.value:e}const Xi={get:(e,t,n)=>t==="__v_raw"?e:bt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return oe(r)&&!oe(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function _o(e){return at(e)?e:new Proxy(e,Xi)}function Zi(e){const t=F(e)?new Array(e.length):{};for(const n in e)t[n]=tl(e,n);return t}class el{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Li(q(this._object),this._key)}}function tl(e,t,n){const s=e[t];return oe(s)?s:new el(e,t,n)}class nl{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Os(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Yt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&te!==this)return to(this,!0),!0}get value(){const t=this.dep.track();return ro(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function sl(e,t,n=!1){let s,r;return H(e)?s=e:(s=e.get,r=e.set),new nl(s,r,n)}const pn={},Sn=new WeakMap;let yt;function rl(e,t=!1,n=yt){if(n){let s=Sn.get(n);s||Sn.set(n,s=[]),s.push(e)}}function ol(e,t,n=Z){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,h=T=>r?T:Te(T)||r===!1||r===0?Ye(T,1):Ye(T);let u,a,g,m,R=!1,O=!1;if(oe(e)?(a=()=>e.value,R=Te(e)):at(e)?(a=()=>h(e),R=!0):F(e)?(O=!0,R=e.some(T=>at(T)||Te(T)),a=()=>e.map(T=>{if(oe(T))return T.value;if(at(T))return h(T);if(H(T))return c?c(T,2):T()})):H(e)?t?a=c?()=>c(e,2):e:a=()=>{if(g){Ze();try{g()}finally{et()}}const T=yt;yt=u;try{return c?c(e,3,[m]):e(m)}finally{yt=T}}:a=Ke,t&&r){const T=a,K=r===!0?1/0:r;a=()=>Ye(T(),K)}const V=Xr(),D=()=>{u.stop(),V&&V.active&&Es(V.effects,u)};if(o&&t){const T=t;t=(...K)=>{T(...K),D()}}let I=O?new Array(e.length).fill(pn):pn;const j=T=>{if(!(!(u.flags&1)||!u.dirty&&!T))if(t){const K=u.run();if(r||R||(O?K.some((le,Q)=>ft(le,I[Q])):ft(K,I))){g&&g();const le=yt;yt=u;try{const Q=[K,I===pn?void 0:O&&I[0]===pn?[]:I,m];I=K,c?c(t,3,Q):t(...Q)}finally{yt=le}}}else u.run()};return l&&l(j),u=new Zr(a),u.scheduler=i?()=>i(j,!1):j,m=T=>rl(T,!1,u),g=u.onStop=()=>{const T=Sn.get(u);if(T){if(c)c(T,4);else for(const K of T)K();Sn.delete(u)}},t?s?j(!0):I=u.run():i?i(j.bind(null,!0),!0):u.run(),D.pause=u.pause.bind(u),D.resume=u.resume.bind(u),D.stop=D,D}function Ye(e,t=1/0,n){if(t<=0||!ne(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,oe(e))Ye(e.value,t,n);else if(F(e))for(let s=0;s<e.length;s++)Ye(e[s],t,n);else if(Dt(e)||Ot(e))e.forEach(s=>{Ye(s,t,n)});else if(Wr(e)){for(const s in e)Ye(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Ye(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function un(e,t,n,s){try{return s?e(...s):e()}catch(r){Nn(r,t,n)}}function Be(e,t,n,s){if(H(e)){const r=un(e,t,n,s);return r&&Ur(r)&&r.catch(o=>{Nn(o,t,n)}),r}if(F(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Be(e[o],t,n,s));return r}}function Nn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Z;if(t){let l=t.parent;const c=t.proxy,h=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let a=0;a<u.length;a++)if(u[a](e,c,h)===!1)return}l=l.parent}if(o){Ze(),un(o,null,10,[e,c,h]),et();return}}il(e,n,r,s,i)}function il(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const me=[];let He=-1;const Tt=[];let it=null,Pt=0;const yo=Promise.resolve();let wn=null;function Fn(e){const t=wn||yo;return e?t.then(this?e.bind(this):e):t}function ll(e){let t=He+1,n=me.length;for(;t<n;){const s=t+n>>>1,r=me[s],o=Zt(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function js(e){if(!(e.flags&1)){const t=Zt(e),n=me[me.length-1];!n||!(e.flags&2)&&t>=Zt(n)?me.push(e):me.splice(ll(t),0,e),e.flags|=1,vo()}}function vo(){wn||(wn=yo.then(Eo))}function cl(e){F(e)?Tt.push(...e):it&&e.id===-1?it.splice(Pt+1,0,e):e.flags&1||(Tt.push(e),e.flags|=1),vo()}function Js(e,t,n=He+1){for(;n<me.length;n++){const s=me[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;me.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function bo(e){if(Tt.length){const t=[...new Set(Tt)].sort((n,s)=>Zt(n)-Zt(s));if(Tt.length=0,it){it.push(...t);return}for(it=t,Pt=0;Pt<it.length;Pt++){const n=it[Pt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}it=null,Pt=0}}const Zt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Eo(e){try{for(He=0;He<me.length;He++){const t=me[He];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),un(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;He<me.length;He++){const t=me[He];t&&(t.flags&=-2)}He=-1,me.length=0,bo(),wn=null,(me.length||Tt.length)&&Eo()}}let be=null,xo=null;function Rn(e){const t=be;return be=e,xo=e&&e.type.__scopeId||null,t}function ul(e,t=be,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&or(-1);const o=Rn(t);let i;try{i=e(...r)}finally{Rn(o),s._d&&or(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function uf(e,t){if(be===null)return e;const n=Kn(be),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=Z]=t[r];o&&(H(o)&&(o={mounted:o,updated:o}),o.deep&&Ye(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function mt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(Ze(),Be(c,n,8,[e.el,l,e,t]),et())}}const fl=Symbol("_vte"),al=e=>e.__isTeleport;function Ds(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ds(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function $n(e,t){return H(e)?he({name:e.name},t,{setup:e}):e}function So(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Pn(e,t,n,s,r=!1){if(F(e)){e.forEach((R,O)=>Pn(R,t&&(F(t)?t[O]:t),n,s,r));return}if(Bt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Pn(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Kn(s.component):s.el,i=r?null:o,{i:l,r:c}=e,h=t&&t.r,u=l.refs===Z?l.refs={}:l.refs,a=l.setupState,g=q(a),m=a===Z?()=>!1:R=>J(g,R);if(h!=null&&h!==c&&(ie(h)?(u[h]=null,m(h)&&(a[h]=null)):oe(h)&&(h.value=null)),H(c))un(c,l,12,[i,u]);else{const R=ie(c),O=oe(c);if(R||O){const V=()=>{if(e.f){const D=R?m(c)?a[c]:u[c]:c.value;r?F(D)&&Es(D,o):F(D)?D.includes(o)||D.push(o):R?(u[c]=[o],m(c)&&(a[c]=u[c])):(c.value=[o],e.k&&(u[e.k]=c.value))}else R?(u[c]=i,m(c)&&(a[c]=i)):O&&(c.value=i,e.k&&(u[e.k]=i))};i?(V.id=-1,Re(V,n)):V()}}}Ln().requestIdleCallback;Ln().cancelIdleCallback;const Bt=e=>!!e.type.__asyncLoader,wo=e=>e.type.__isKeepAlive;function dl(e,t){Ro(e,"a",t)}function hl(e,t){Ro(e,"da",t)}function Ro(e,t,n=ue){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Vn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)wo(r.parent.vnode)&&pl(s,t,n,r),r=r.parent}}function pl(e,t,n,s){const r=Vn(t,e,s,!0);Co(()=>{Es(s[t],r)},n)}function Vn(e,t,n=ue,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Ze();const l=fn(n),c=Be(t,n,e,i);return l(),et(),c});return s?r.unshift(o):r.push(o),o}}const tt=e=>(t,n=ue)=>{(!tn||e==="sp")&&Vn(e,(...s)=>t(...s),n)},gl=tt("bm"),Po=tt("m"),ml=tt("bu"),_l=tt("u"),yl=tt("bum"),Co=tt("um"),vl=tt("sp"),bl=tt("rtg"),El=tt("rtc");function xl(e,t=ue){Vn("ec",e,t)}const Sl="components";function ff(e,t){return Rl(Sl,e,!0,t)||e}const wl=Symbol.for("v-ndc");function Rl(e,t,n=!0,s=!1){const r=be||ue;if(r){const o=r.type;{const l=gc(o,!1);if(l&&(l===t||l===Me(t)||l===Mn(Me(t))))return o}const i=Qs(r[e]||o[e],t)||Qs(r.appContext[e],t);return!i&&s?o:i}}function Qs(e,t){return e&&(e[t]||e[Me(t)]||e[Mn(Me(t))])}function af(e,t,n,s){let r;const o=n,i=F(e);if(i||ie(e)){const l=i&&at(e);let c=!1,h=!1;l&&(c=!Te(e),h=dt(e),e=jn(e)),r=new Array(e.length);for(let u=0,a=e.length;u<a;u++)r[u]=t(c?h?xn(ce(e[u])):ce(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ne(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,h=l.length;c<h;c++){const u=l[c];r[c]=t(e[u],u,c,o)}}else r=[];return r}const cs=e=>e?zo(e)?Kn(e):cs(e.parent):null,Wt=he(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>cs(e.parent),$root:e=>cs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Oo(e),$forceUpdate:e=>e.f||(e.f=()=>{js(e.update)}),$nextTick:e=>e.n||(e.n=Fn.bind(e.proxy)),$watch:e=>Gl.bind(e)}),Yn=(e,t)=>e!==Z&&!e.__isScriptSetup&&J(e,t),Pl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let h;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Yn(s,t))return i[t]=1,s[t];if(r!==Z&&J(r,t))return i[t]=2,r[t];if((h=e.propsOptions[0])&&J(h,t))return i[t]=3,o[t];if(n!==Z&&J(n,t))return i[t]=4,n[t];us&&(i[t]=0)}}const u=Wt[t];let a,g;if(u)return t==="$attrs"&&ae(e.attrs,"get",""),u(e);if((a=l.__cssModules)&&(a=a[t]))return a;if(n!==Z&&J(n,t))return i[t]=4,n[t];if(g=c.config.globalProperties,J(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Yn(r,t)?(r[t]=n,!0):s!==Z&&J(s,t)?(s[t]=n,!0):J(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==Z&&J(e,i)||Yn(t,i)||(l=o[0])&&J(l,i)||J(s,i)||J(Wt,i)||J(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:J(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ys(e){return F(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let us=!0;function Cl(e){const t=Oo(e),n=e.proxy,s=e.ctx;us=!1,t.beforeCreate&&Xs(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:h,created:u,beforeMount:a,mounted:g,beforeUpdate:m,updated:R,activated:O,deactivated:V,beforeDestroy:D,beforeUnmount:I,destroyed:j,unmounted:T,render:K,renderTracked:le,renderTriggered:Q,errorCaptured:U,serverPrefetch:B,expose:re,inheritAttrs:pe,components:Ee,directives:_e,filters:gt}=t;if(h&&Al(h,s,null),i)for(const k in i){const G=i[k];H(G)&&(s[k]=G.bind(n))}if(r){const k=r.call(n,n);ne(k)&&(e.data=cn(k))}if(us=!0,o)for(const k in o){const G=o[k],We=H(G)?G.bind(n,n):H(G.get)?G.get.bind(n,n):Ke,st=!H(G)&&H(G.set)?G.set.bind(n):Ke,De=Oe({get:We,set:st});Object.defineProperty(s,k,{enumerable:!0,configurable:!0,get:()=>De.value,set:ye=>De.value=ye})}if(l)for(const k in l)Ao(l[k],s,n,k);if(c){const k=H(c)?c.call(n):c;Reflect.ownKeys(k).forEach(G=>{mn(G,k[G])})}u&&Xs(u,e,"c");function se(k,G){F(G)?G.forEach(We=>k(We.bind(n))):G&&k(G.bind(n))}if(se(gl,a),se(Po,g),se(ml,m),se(_l,R),se(dl,O),se(hl,V),se(xl,U),se(El,le),se(bl,Q),se(yl,I),se(Co,T),se(vl,B),F(re))if(re.length){const k=e.exposed||(e.exposed={});re.forEach(G=>{Object.defineProperty(k,G,{get:()=>n[G],set:We=>n[G]=We})})}else e.exposed||(e.exposed={});K&&e.render===Ke&&(e.render=K),pe!=null&&(e.inheritAttrs=pe),Ee&&(e.components=Ee),_e&&(e.directives=_e),B&&So(e)}function Al(e,t,n=Ke){F(e)&&(e=fs(e));for(const s in e){const r=e[s];let o;ne(r)?"default"in r?o=Ie(r.from||s,r.default,!0):o=Ie(r.from||s):o=Ie(r),oe(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function Xs(e,t,n){Be(F(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ao(e,t,n,s){let r=s.includes(".")?Ko(n,s):()=>n[s];if(ie(e)){const o=t[e];H(o)&&qt(r,o)}else if(H(e))qt(r,e.bind(n));else if(ne(e))if(F(e))e.forEach(o=>Ao(o,t,n,s));else{const o=H(e.handler)?e.handler.bind(n):t[e.handler];H(o)&&qt(r,o,e)}}function Oo(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(h=>Cn(c,h,i,!0)),Cn(c,t,i)),ne(t)&&o.set(t,c),c}function Cn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Cn(e,o,n,!0),r&&r.forEach(i=>Cn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Ol[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Ol={data:Zs,props:er,emits:er,methods:Ht,computed:Ht,beforeCreate:ge,created:ge,beforeMount:ge,mounted:ge,beforeUpdate:ge,updated:ge,beforeDestroy:ge,beforeUnmount:ge,destroyed:ge,unmounted:ge,activated:ge,deactivated:ge,errorCaptured:ge,serverPrefetch:ge,components:Ht,directives:Ht,watch:Il,provide:Zs,inject:Tl};function Zs(e,t){return t?e?function(){return he(H(e)?e.call(this,this):e,H(t)?t.call(this,this):t)}:t:e}function Tl(e,t){return Ht(fs(e),fs(t))}function fs(e){if(F(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ge(e,t){return e?[...new Set([].concat(e,t))]:t}function Ht(e,t){return e?he(Object.create(null),e,t):t}function er(e,t){return e?F(e)&&F(t)?[...new Set([...e,...t])]:he(Object.create(null),Ys(e),Ys(t??{})):t}function Il(e,t){if(!e)return t;if(!t)return e;const n=he(Object.create(null),e);for(const s in t)n[s]=ge(e[s],t[s]);return n}function To(){return{app:null,config:{isNativeTag:_i,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ml=0;function Ll(e,t){return function(s,r=null){H(s)||(s=he({},s)),r!=null&&!ne(r)&&(r=null);const o=To(),i=new WeakSet,l=[];let c=!1;const h=o.app={_uid:Ml++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:_c,get config(){return o.config},set config(u){},use(u,...a){return i.has(u)||(u&&H(u.install)?(i.add(u),u.install(h,...a)):H(u)&&(i.add(u),u(h,...a))),h},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),h},component(u,a){return a?(o.components[u]=a,h):o.components[u]},directive(u,a){return a?(o.directives[u]=a,h):o.directives[u]},mount(u,a,g){if(!c){const m=h._ceVNode||de(s,r);return m.appContext=o,g===!0?g="svg":g===!1&&(g=void 0),e(m,u,g),c=!0,h._container=u,u.__vue_app__=h,Kn(m.component)}},onUnmount(u){l.push(u)},unmount(){c&&(Be(l,h._instance,16),e(null,h._container),delete h._container.__vue_app__)},provide(u,a){return o.provides[u]=a,h},runWithContext(u){const a=Et;Et=h;try{return u()}finally{Et=a}}};return h}}let Et=null;function mn(e,t){if(ue){let n=ue.provides;const s=ue.parent&&ue.parent.provides;s===n&&(n=ue.provides=Object.create(s)),n[e]=t}}function Ie(e,t,n=!1){const s=ue||be;if(s||Et){let r=Et?Et._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&H(t)?t.call(s&&s.proxy):t}}function jl(){return!!(ue||be||Et)}const Io={},Mo=()=>Object.create(Io),Lo=e=>Object.getPrototypeOf(e)===Io;function Dl(e,t,n,s=!1){const r={},o=Mo();e.propsDefaults=Object.create(null),jo(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:po(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Nl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=q(r),[c]=e.propsOptions;let h=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let a=0;a<u.length;a++){let g=u[a];if(Hn(e.emitsOptions,g))continue;const m=t[g];if(c)if(J(o,g))m!==o[g]&&(o[g]=m,h=!0);else{const R=Me(g);r[R]=as(c,l,R,m,e,!1)}else m!==o[g]&&(o[g]=m,h=!0)}}}else{jo(e,t,r,o)&&(h=!0);let u;for(const a in l)(!t||!J(t,a)&&((u=pt(a))===a||!J(t,u)))&&(c?n&&(n[a]!==void 0||n[u]!==void 0)&&(r[a]=as(c,l,a,void 0,e,!0)):delete r[a]);if(o!==l)for(const a in o)(!t||!J(t,a))&&(delete o[a],h=!0)}h&&Qe(e.attrs,"set","")}function jo(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(kt(c))continue;const h=t[c];let u;r&&J(r,u=Me(c))?!o||!o.includes(u)?n[u]=h:(l||(l={}))[u]=h:Hn(e.emitsOptions,c)||(!(c in s)||h!==s[c])&&(s[c]=h,i=!0)}if(o){const c=q(n),h=l||Z;for(let u=0;u<o.length;u++){const a=o[u];n[a]=as(r,c,a,h[a],e,!J(h,a))}}return i}function as(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=J(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&H(c)){const{propsDefaults:h}=r;if(n in h)s=h[n];else{const u=fn(r);s=h[n]=c.call(null,t),u()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===pt(n))&&(s=!0))}return s}const Fl=new WeakMap;function Do(e,t,n=!1){const s=n?Fl:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!H(e)){const u=a=>{c=!0;const[g,m]=Do(a,t,!0);he(i,g),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!c)return ne(e)&&s.set(e,At),At;if(F(o))for(let u=0;u<o.length;u++){const a=Me(o[u]);tr(a)&&(i[a]=Z)}else if(o)for(const u in o){const a=Me(u);if(tr(a)){const g=o[u],m=i[a]=F(g)||H(g)?{type:g}:he({},g),R=m.type;let O=!1,V=!0;if(F(R))for(let D=0;D<R.length;++D){const I=R[D],j=H(I)&&I.name;if(j==="Boolean"){O=!0;break}else j==="String"&&(V=!1)}else O=H(R)&&R.name==="Boolean";m[0]=O,m[1]=V,(O||J(m,"default"))&&l.push(a)}}const h=[i,l];return ne(e)&&s.set(e,h),h}function tr(e){return e[0]!=="$"&&!kt(e)}const Ns=e=>e[0]==="_"||e==="$stable",Fs=e=>F(e)?e.map(ke):[ke(e)],$l=(e,t,n)=>{if(t._n)return t;const s=ul((...r)=>Fs(t(...r)),n);return s._c=!1,s},No=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Ns(r))continue;const o=e[r];if(H(o))t[r]=$l(r,o,s);else if(o!=null){const i=Fs(o);t[r]=()=>i}}},Fo=(e,t)=>{const n=Fs(t);e.slots.default=()=>n},$o=(e,t,n)=>{for(const s in t)(n||!Ns(s))&&(e[s]=t[s])},Vl=(e,t,n)=>{const s=e.slots=Mo();if(e.vnode.shapeFlag&32){const r=t._;r?($o(s,t,n),n&&qr(s,"_",r,!0)):No(t,s)}else t&&Fo(e,t)},Hl=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=Z;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:$o(r,t,n):(o=!t.$stable,No(t,r)),i=t}else t&&(Fo(e,t),i={default:1});if(o)for(const l in r)!Ns(l)&&i[l]==null&&delete r[l]},Re=ec;function kl(e){return Kl(e)}function Kl(e,t){const n=Ln();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:h,setElementText:u,parentNode:a,nextSibling:g,setScopeId:m=Ke,insertStaticContent:R}=e,O=(f,d,p,_=null,b=null,v=null,w=void 0,S=null,x=!!d.dynamicChildren)=>{if(f===d)return;f&&!$t(f,d)&&(_=y(f),ye(f,b,v,!0),f=null),d.patchFlag===-2&&(x=!1,d.dynamicChildren=null);const{type:E,ref:N,shapeFlag:C}=d;switch(E){case kn:V(f,d,p,_);break;case ht:D(f,d,p,_);break;case _n:f==null&&I(d,p,_,w);break;case Je:Ee(f,d,p,_,b,v,w,S,x);break;default:C&1?K(f,d,p,_,b,v,w,S,x):C&6?_e(f,d,p,_,b,v,w,S,x):(C&64||C&128)&&E.process(f,d,p,_,b,v,w,S,x,M)}N!=null&&b&&Pn(N,f&&f.ref,v,d||f,!d)},V=(f,d,p,_)=>{if(f==null)s(d.el=l(d.children),p,_);else{const b=d.el=f.el;d.children!==f.children&&h(b,d.children)}},D=(f,d,p,_)=>{f==null?s(d.el=c(d.children||""),p,_):d.el=f.el},I=(f,d,p,_)=>{[f.el,f.anchor]=R(f.children,d,p,_,f.el,f.anchor)},j=({el:f,anchor:d},p,_)=>{let b;for(;f&&f!==d;)b=g(f),s(f,p,_),f=b;s(d,p,_)},T=({el:f,anchor:d})=>{let p;for(;f&&f!==d;)p=g(f),r(f),f=p;r(d)},K=(f,d,p,_,b,v,w,S,x)=>{d.type==="svg"?w="svg":d.type==="math"&&(w="mathml"),f==null?le(d,p,_,b,v,w,S,x):B(f,d,b,v,w,S,x)},le=(f,d,p,_,b,v,w,S)=>{let x,E;const{props:N,shapeFlag:C,transition:L,dirs:$}=f;if(x=f.el=i(f.type,v,N&&N.is,N),C&8?u(x,f.children):C&16&&U(f.children,x,null,_,b,Xn(f,v),w,S),$&&mt(f,null,_,"created"),Q(x,f,f.scopeId,w,_),N){for(const ee in N)ee!=="value"&&!kt(ee)&&o(x,ee,null,N[ee],v,_);"value"in N&&o(x,"value",null,N.value,v),(E=N.onVnodeBeforeMount)&&Ve(E,_,f)}$&&mt(f,null,_,"beforeMount");const W=Ul(b,L);W&&L.beforeEnter(x),s(x,d,p),((E=N&&N.onVnodeMounted)||W||$)&&Re(()=>{E&&Ve(E,_,f),W&&L.enter(x),$&&mt(f,null,_,"mounted")},b)},Q=(f,d,p,_,b)=>{if(p&&m(f,p),_)for(let v=0;v<_.length;v++)m(f,_[v]);if(b){let v=b.subTree;if(d===v||Bo(v.type)&&(v.ssContent===d||v.ssFallback===d)){const w=b.vnode;Q(f,w,w.scopeId,w.slotScopeIds,b.parent)}}},U=(f,d,p,_,b,v,w,S,x=0)=>{for(let E=x;E<f.length;E++){const N=f[E]=S?lt(f[E]):ke(f[E]);O(null,N,d,p,_,b,v,w,S)}},B=(f,d,p,_,b,v,w)=>{const S=d.el=f.el;let{patchFlag:x,dynamicChildren:E,dirs:N}=d;x|=f.patchFlag&16;const C=f.props||Z,L=d.props||Z;let $;if(p&&_t(p,!1),($=L.onVnodeBeforeUpdate)&&Ve($,p,d,f),N&&mt(d,f,p,"beforeUpdate"),p&&_t(p,!0),(C.innerHTML&&L.innerHTML==null||C.textContent&&L.textContent==null)&&u(S,""),E?re(f.dynamicChildren,E,S,p,_,Xn(d,b),v):w||G(f,d,S,null,p,_,Xn(d,b),v,!1),x>0){if(x&16)pe(S,C,L,p,b);else if(x&2&&C.class!==L.class&&o(S,"class",null,L.class,b),x&4&&o(S,"style",C.style,L.style,b),x&8){const W=d.dynamicProps;for(let ee=0;ee<W.length;ee++){const Y=W[ee],xe=C[Y],ve=L[Y];(ve!==xe||Y==="value")&&o(S,Y,xe,ve,b,p)}}x&1&&f.children!==d.children&&u(S,d.children)}else!w&&E==null&&pe(S,C,L,p,b);(($=L.onVnodeUpdated)||N)&&Re(()=>{$&&Ve($,p,d,f),N&&mt(d,f,p,"updated")},_)},re=(f,d,p,_,b,v,w)=>{for(let S=0;S<d.length;S++){const x=f[S],E=d[S],N=x.el&&(x.type===Je||!$t(x,E)||x.shapeFlag&198)?a(x.el):p;O(x,E,N,null,_,b,v,w,!0)}},pe=(f,d,p,_,b)=>{if(d!==p){if(d!==Z)for(const v in d)!kt(v)&&!(v in p)&&o(f,v,d[v],null,b,_);for(const v in p){if(kt(v))continue;const w=p[v],S=d[v];w!==S&&v!=="value"&&o(f,v,S,w,b,_)}"value"in p&&o(f,"value",d.value,p.value,b)}},Ee=(f,d,p,_,b,v,w,S,x)=>{const E=d.el=f?f.el:l(""),N=d.anchor=f?f.anchor:l("");let{patchFlag:C,dynamicChildren:L,slotScopeIds:$}=d;$&&(S=S?S.concat($):$),f==null?(s(E,p,_),s(N,p,_),U(d.children||[],p,N,b,v,w,S,x)):C>0&&C&64&&L&&f.dynamicChildren?(re(f.dynamicChildren,L,p,b,v,w,S),(d.key!=null||b&&d===b.subTree)&&Vo(f,d,!0)):G(f,d,p,N,b,v,w,S,x)},_e=(f,d,p,_,b,v,w,S,x)=>{d.slotScopeIds=S,f==null?d.shapeFlag&512?b.ctx.activate(d,p,_,w,x):gt(d,p,_,b,v,w,x):nt(f,d,x)},gt=(f,d,p,_,b,v,w)=>{const S=f.component=fc(f,_,b);if(wo(f)&&(S.ctx.renderer=M),ac(S,!1,w),S.asyncDep){if(b&&b.registerDep(S,se,w),!f.el){const x=S.subTree=de(ht);D(null,x,d,p)}}else se(S,f,d,p,b,v,w)},nt=(f,d,p)=>{const _=d.component=f.component;if(Xl(f,d,p))if(_.asyncDep&&!_.asyncResolved){k(_,d,p);return}else _.next=d,_.update();else d.el=f.el,_.vnode=d},se=(f,d,p,_,b,v,w)=>{const S=()=>{if(f.isMounted){let{next:C,bu:L,u:$,parent:W,vnode:ee}=f;{const Fe=Ho(f);if(Fe){C&&(C.el=ee.el,k(f,C,w)),Fe.asyncDep.then(()=>{f.isUnmounted||S()});return}}let Y=C,xe;_t(f,!1),C?(C.el=ee.el,k(f,C,w)):C=ee,L&&gn(L),(xe=C.props&&C.props.onVnodeBeforeUpdate)&&Ve(xe,W,C,ee),_t(f,!0);const ve=sr(f),Ne=f.subTree;f.subTree=ve,O(Ne,ve,a(Ne.el),y(Ne),f,b,v),C.el=ve.el,Y===null&&Zl(f,ve.el),$&&Re($,b),(xe=C.props&&C.props.onVnodeUpdated)&&Re(()=>Ve(xe,W,C,ee),b)}else{let C;const{el:L,props:$}=d,{bm:W,m:ee,parent:Y,root:xe,type:ve}=f,Ne=Bt(d);_t(f,!1),W&&gn(W),!Ne&&(C=$&&$.onVnodeBeforeMount)&&Ve(C,Y,d),_t(f,!0);{xe.ce&&xe.ce._injectChildStyle(ve);const Fe=f.subTree=sr(f);O(null,Fe,p,_,f,b,v),d.el=Fe.el}if(ee&&Re(ee,b),!Ne&&(C=$&&$.onVnodeMounted)){const Fe=d;Re(()=>Ve(C,Y,Fe),b)}(d.shapeFlag&256||Y&&Bt(Y.vnode)&&Y.vnode.shapeFlag&256)&&f.a&&Re(f.a,b),f.isMounted=!0,d=p=_=null}};f.scope.on();const x=f.effect=new Zr(S);f.scope.off();const E=f.update=x.run.bind(x),N=f.job=x.runIfDirty.bind(x);N.i=f,N.id=f.uid,x.scheduler=()=>js(N),_t(f,!0),E()},k=(f,d,p)=>{d.component=f;const _=f.vnode.props;f.vnode=d,f.next=null,Nl(f,d.props,_,p),Hl(f,d.children,p),Ze(),Js(f),et()},G=(f,d,p,_,b,v,w,S,x=!1)=>{const E=f&&f.children,N=f?f.shapeFlag:0,C=d.children,{patchFlag:L,shapeFlag:$}=d;if(L>0){if(L&128){st(E,C,p,_,b,v,w,S,x);return}else if(L&256){We(E,C,p,_,b,v,w,S,x);return}}$&8?(N&16&&Ce(E,b,v),C!==E&&u(p,C)):N&16?$&16?st(E,C,p,_,b,v,w,S,x):Ce(E,b,v,!0):(N&8&&u(p,""),$&16&&U(C,p,_,b,v,w,S,x))},We=(f,d,p,_,b,v,w,S,x)=>{f=f||At,d=d||At;const E=f.length,N=d.length,C=Math.min(E,N);let L;for(L=0;L<C;L++){const $=d[L]=x?lt(d[L]):ke(d[L]);O(f[L],$,p,null,b,v,w,S,x)}E>N?Ce(f,b,v,!0,!1,C):U(d,p,_,b,v,w,S,x,C)},st=(f,d,p,_,b,v,w,S,x)=>{let E=0;const N=d.length;let C=f.length-1,L=N-1;for(;E<=C&&E<=L;){const $=f[E],W=d[E]=x?lt(d[E]):ke(d[E]);if($t($,W))O($,W,p,null,b,v,w,S,x);else break;E++}for(;E<=C&&E<=L;){const $=f[C],W=d[L]=x?lt(d[L]):ke(d[L]);if($t($,W))O($,W,p,null,b,v,w,S,x);else break;C--,L--}if(E>C){if(E<=L){const $=L+1,W=$<N?d[$].el:_;for(;E<=L;)O(null,d[E]=x?lt(d[E]):ke(d[E]),p,W,b,v,w,S,x),E++}}else if(E>L)for(;E<=C;)ye(f[E],b,v,!0),E++;else{const $=E,W=E,ee=new Map;for(E=W;E<=L;E++){const Se=d[E]=x?lt(d[E]):ke(d[E]);Se.key!=null&&ee.set(Se.key,E)}let Y,xe=0;const ve=L-W+1;let Ne=!1,Fe=0;const Nt=new Array(ve);for(E=0;E<ve;E++)Nt[E]=0;for(E=$;E<=C;E++){const Se=f[E];if(xe>=ve){ye(Se,b,v,!0);continue}let $e;if(Se.key!=null)$e=ee.get(Se.key);else for(Y=W;Y<=L;Y++)if(Nt[Y-W]===0&&$t(Se,d[Y])){$e=Y;break}$e===void 0?ye(Se,b,v,!0):(Nt[$e-W]=E+1,$e>=Fe?Fe=$e:Ne=!0,O(Se,d[$e],p,null,b,v,w,S,x),xe++)}const Us=Ne?Bl(Nt):At;for(Y=Us.length-1,E=ve-1;E>=0;E--){const Se=W+E,$e=d[Se],Bs=Se+1<N?d[Se+1].el:_;Nt[E]===0?O(null,$e,p,Bs,b,v,w,S,x):Ne&&(Y<0||E!==Us[Y]?De($e,p,Bs,2):Y--)}}},De=(f,d,p,_,b=null)=>{const{el:v,type:w,transition:S,children:x,shapeFlag:E}=f;if(E&6){De(f.component.subTree,d,p,_);return}if(E&128){f.suspense.move(d,p,_);return}if(E&64){w.move(f,d,p,M);return}if(w===Je){s(v,d,p);for(let C=0;C<x.length;C++)De(x[C],d,p,_);s(f.anchor,d,p);return}if(w===_n){j(f,d,p);return}if(_!==2&&E&1&&S)if(_===0)S.beforeEnter(v),s(v,d,p),Re(()=>S.enter(v),b);else{const{leave:C,delayLeave:L,afterLeave:$}=S,W=()=>{f.ctx.isUnmounted?r(v):s(v,d,p)},ee=()=>{C(v,()=>{W(),$&&$()})};L?L(v,W,ee):ee()}else s(v,d,p)},ye=(f,d,p,_=!1,b=!1)=>{const{type:v,props:w,ref:S,children:x,dynamicChildren:E,shapeFlag:N,patchFlag:C,dirs:L,cacheIndex:$}=f;if(C===-2&&(b=!1),S!=null&&(Ze(),Pn(S,null,p,f,!0),et()),$!=null&&(d.renderCache[$]=void 0),N&256){d.ctx.deactivate(f);return}const W=N&1&&L,ee=!Bt(f);let Y;if(ee&&(Y=w&&w.onVnodeBeforeUnmount)&&Ve(Y,d,f),N&6)an(f.component,p,_);else{if(N&128){f.suspense.unmount(p,_);return}W&&mt(f,null,d,"beforeUnmount"),N&64?f.type.remove(f,d,p,M,_):E&&!E.hasOnce&&(v!==Je||C>0&&C&64)?Ce(E,d,p,!1,!0):(v===Je&&C&384||!b&&N&16)&&Ce(x,d,p),_&&xt(f)}(ee&&(Y=w&&w.onVnodeUnmounted)||W)&&Re(()=>{Y&&Ve(Y,d,f),W&&mt(f,null,d,"unmounted")},p)},xt=f=>{const{type:d,el:p,anchor:_,transition:b}=f;if(d===Je){St(p,_);return}if(d===_n){T(f);return}const v=()=>{r(p),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(f.shapeFlag&1&&b&&!b.persisted){const{leave:w,delayLeave:S}=b,x=()=>w(p,v);S?S(f.el,v,x):x()}else v()},St=(f,d)=>{let p;for(;f!==d;)p=g(f),r(f),f=p;r(d)},an=(f,d,p)=>{const{bum:_,scope:b,job:v,subTree:w,um:S,m:x,a:E,parent:N,slots:{__:C}}=f;nr(x),nr(E),_&&gn(_),N&&F(C)&&C.forEach(L=>{N.renderCache[L]=void 0}),b.stop(),v&&(v.flags|=8,ye(w,f,d,p)),S&&Re(S,d),Re(()=>{f.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Ce=(f,d,p,_=!1,b=!1,v=0)=>{for(let w=v;w<f.length;w++)ye(f[w],d,p,_,b)},y=f=>{if(f.shapeFlag&6)return y(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const d=g(f.anchor||f.el),p=d&&d[fl];return p?g(p):d};let A=!1;const P=(f,d,p)=>{f==null?d._vnode&&ye(d._vnode,null,null,!0):O(d._vnode||null,f,d,null,null,null,p),d._vnode=f,A||(A=!0,Js(),bo(),A=!1)},M={p:O,um:ye,m:De,r:xt,mt:gt,mc:U,pc:G,pbc:re,n:y,o:e};return{render:P,hydrate:void 0,createApp:Ll(P)}}function Xn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function _t({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ul(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Vo(e,t,n=!1){const s=e.children,r=t.children;if(F(s)&&F(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=lt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Vo(i,l)),l.type===kn&&(l.el=i.el),l.type===ht&&!l.el&&(l.el=i.el)}}function Bl(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const h=e[s];if(h!==0){if(r=n[n.length-1],e[r]<h){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<h?o=l+1:i=l;h<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Ho(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ho(t)}function nr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Wl=Symbol.for("v-scx"),ql=()=>Ie(Wl);function qt(e,t,n){return ko(e,t,n)}function ko(e,t,n=Z){const{immediate:s,deep:r,flush:o,once:i}=n,l=he({},n),c=t&&s||!t&&o!=="post";let h;if(tn){if(o==="sync"){const m=ql();h=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=Ke,m.resume=Ke,m.pause=Ke,m}}const u=ue;l.call=(m,R,O)=>Be(m,u,R,O);let a=!1;o==="post"?l.scheduler=m=>{Re(m,u&&u.suspense)}:o!=="sync"&&(a=!0,l.scheduler=(m,R)=>{R?m():js(m)}),l.augmentJob=m=>{t&&(m.flags|=4),a&&(m.flags|=2,u&&(m.id=u.uid,m.i=u))};const g=ol(e,t,l);return tn&&(h?h.push(g):c&&g()),g}function Gl(e,t,n){const s=this.proxy,r=ie(e)?e.includes(".")?Ko(s,e):()=>s[e]:e.bind(s,s);let o;H(t)?o=t:(o=t.handler,n=t);const i=fn(this),l=ko(r,o.bind(s),n);return i(),l}function Ko(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const zl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Me(t)}Modifiers`]||e[`${pt(t)}Modifiers`];function Jl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||Z;let r=n;const o=t.startsWith("update:"),i=o&&zl(s,t.slice(7));i&&(i.trim&&(r=n.map(u=>ie(u)?u.trim():u)),i.number&&(r=n.map(bn)));let l,c=s[l=qn(t)]||s[l=qn(Me(t))];!c&&o&&(c=s[l=qn(pt(t))]),c&&Be(c,e,6,r);const h=s[l+"Once"];if(h){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Be(h,e,6,r)}}function Uo(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!H(e)){const c=h=>{const u=Uo(h,t,!0);u&&(l=!0,he(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(ne(e)&&s.set(e,null),null):(F(o)?o.forEach(c=>i[c]=null):he(i,o),ne(e)&&s.set(e,i),i)}function Hn(e,t){return!e||!Tn(t)?!1:(t=t.slice(2).replace(/Once$/,""),J(e,t[0].toLowerCase()+t.slice(1))||J(e,pt(t))||J(e,t))}function sr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:h,renderCache:u,props:a,data:g,setupState:m,ctx:R,inheritAttrs:O}=e,V=Rn(e);let D,I;try{if(n.shapeFlag&4){const T=r||s,K=T;D=ke(h.call(K,T,u,a,m,g,R)),I=l}else{const T=t;D=ke(T.length>1?T(a,{attrs:l,slots:i,emit:c}):T(a,null)),I=t.props?l:Ql(l)}}catch(T){Gt.length=0,Nn(T,e,1),D=de(ht)}let j=D;if(I&&O!==!1){const T=Object.keys(I),{shapeFlag:K}=j;T.length&&K&7&&(o&&T.some(bs)&&(I=Yl(I,o)),j=It(j,I,!1,!0))}return n.dirs&&(j=It(j,null,!1,!0),j.dirs=j.dirs?j.dirs.concat(n.dirs):n.dirs),n.transition&&Ds(j,n.transition),D=j,Rn(V),D}const Ql=e=>{let t;for(const n in e)(n==="class"||n==="style"||Tn(n))&&((t||(t={}))[n]=e[n]);return t},Yl=(e,t)=>{const n={};for(const s in e)(!bs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Xl(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,h=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?rr(s,i,h):!!i;if(c&8){const u=t.dynamicProps;for(let a=0;a<u.length;a++){const g=u[a];if(i[g]!==s[g]&&!Hn(h,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?rr(s,i,h):!0:!!i;return!1}function rr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Hn(n,o))return!0}return!1}function Zl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Bo=e=>e.__isSuspense;function ec(e,t){t&&t.pendingBranch?F(e)?t.effects.push(...e):t.effects.push(e):cl(e)}const Je=Symbol.for("v-fgt"),kn=Symbol.for("v-txt"),ht=Symbol.for("v-cmt"),_n=Symbol.for("v-stc"),Gt=[];let Pe=null;function $s(e=!1){Gt.push(Pe=e?null:[])}function tc(){Gt.pop(),Pe=Gt[Gt.length-1]||null}let en=1;function or(e,t=!1){en+=e,e<0&&Pe&&t&&(Pe.hasOnce=!0)}function Wo(e){return e.dynamicChildren=en>0?Pe||At:null,tc(),en>0&&Pe&&Pe.push(e),e}function qo(e,t,n,s,r,o){return Wo(Ae(e,t,n,s,r,o,!0))}function nc(e,t,n,s,r){return Wo(de(e,t,n,s,r,!0))}function An(e){return e?e.__v_isVNode===!0:!1}function $t(e,t){return e.type===t.type&&e.key===t.key}const Go=({key:e})=>e??null,yn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ie(e)||oe(e)||H(e)?{i:be,r:e,k:t,f:!!n}:e:null);function Ae(e,t=null,n=null,s=0,r=null,o=e===Je?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Go(t),ref:t&&yn(t),scopeId:xo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:be};return l?(Vs(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ie(n)?8:16),en>0&&!i&&Pe&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Pe.push(c),c}const de=sc;function sc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===wl)&&(e=ht),An(e)){const l=It(e,t,!0);return n&&Vs(l,n),en>0&&!o&&Pe&&(l.shapeFlag&6?Pe[Pe.indexOf(e)]=l:Pe.push(l)),l.patchFlag=-2,l}if(mc(e)&&(e=e.__vccOpts),t){t=rc(t);let{class:l,style:c}=t;l&&!ie(l)&&(t.class=ws(l)),ne(c)&&(Ms(c)&&!F(c)&&(c=he({},c)),t.style=Ss(c))}const i=ie(e)?1:Bo(e)?128:al(e)?64:ne(e)?4:H(e)?2:0;return Ae(e,t,n,s,r,i,o,!0)}function rc(e){return e?Ms(e)||Lo(e)?he({},e):e:null}function It(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,h=t?lc(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:h,key:h&&Go(h),ref:t&&t.ref?n&&o?F(o)?o.concat(yn(t)):[o,yn(t)]:yn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Je?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&It(e.ssContent),ssFallback:e.ssFallback&&It(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ds(u,c.clone(u)),u}function oc(e=" ",t=0){return de(kn,null,e,t)}function df(e,t){const n=de(_n,null,e);return n.staticCount=t,n}function ic(e="",t=!1){return t?($s(),nc(ht,null,e)):de(ht,null,e)}function ke(e){return e==null||typeof e=="boolean"?de(ht):F(e)?de(Je,null,e.slice()):An(e)?lt(e):de(kn,null,String(e))}function lt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:It(e)}function Vs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(F(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Vs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Lo(t)?t._ctx=be:r===3&&be&&(be.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else H(t)?(t={default:t,_ctx:be},n=32):(t=String(t),s&64?(n=16,t=[oc(t)]):n=8);e.children=t,e.shapeFlag|=n}function lc(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=ws([t.class,s.class]));else if(r==="style")t.style=Ss([t.style,s.style]);else if(Tn(r)){const o=t[r],i=s[r];i&&o!==i&&!(F(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Ve(e,t,n,s=null){Be(e,t,7,[n,s])}const cc=To();let uc=0;function fc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||cc,o={uid:uc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Qr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Do(s,r),emitsOptions:Uo(s,r),emit:null,emitted:null,propsDefaults:Z,inheritAttrs:s.inheritAttrs,ctx:Z,data:Z,props:Z,attrs:Z,slots:Z,refs:Z,setupState:Z,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Jl.bind(null,o),e.ce&&e.ce(o),o}let ue=null,On,ds;{const e=Ln(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};On=t("__VUE_INSTANCE_SETTERS__",n=>ue=n),ds=t("__VUE_SSR_SETTERS__",n=>tn=n)}const fn=e=>{const t=ue;return On(e),e.scope.on(),()=>{e.scope.off(),On(t)}},ir=()=>{ue&&ue.scope.off(),On(null)};function zo(e){return e.vnode.shapeFlag&4}let tn=!1;function ac(e,t=!1,n=!1){t&&ds(t);const{props:s,children:r}=e.vnode,o=zo(e);Dl(e,s,o,t),Vl(e,r,n||t);const i=o?dc(e,t):void 0;return t&&ds(!1),i}function dc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Pl);const{setup:s}=n;if(s){Ze();const r=e.setupContext=s.length>1?pc(e):null,o=fn(e),i=un(s,e,0,[e.props,r]),l=Ur(i);if(et(),o(),(l||e.sp)&&!Bt(e)&&So(e),l){if(i.then(ir,ir),t)return i.then(c=>{lr(e,c)}).catch(c=>{Nn(c,e,0)});e.asyncDep=i}else lr(e,i)}else Jo(e)}function lr(e,t,n){H(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ne(t)&&(e.setupState=_o(t)),Jo(e)}function Jo(e,t,n){const s=e.type;e.render||(e.render=s.render||Ke);{const r=fn(e);Ze();try{Cl(e)}finally{et(),r()}}}const hc={get(e,t){return ae(e,"get",""),e[t]}};function pc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,hc),slots:e.slots,emit:e.emit,expose:t}}function Kn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(_o(Ls(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Wt)return Wt[n](e)},has(t,n){return n in t||n in Wt}})):e.proxy}function gc(e,t=!0){return H(e)?e.displayName||e.name:e.name||t&&e.__name}function mc(e){return H(e)&&"__vccOpts"in e}const Oe=(e,t)=>sl(e,t,tn);function Qo(e,t,n){const s=arguments.length;return s===2?ne(t)&&!F(t)?An(t)?de(e,null,[t]):de(e,t):de(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&An(n)&&(n=[n]),de(e,t,n))}const _c="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let hs;const cr=typeof window<"u"&&window.trustedTypes;if(cr)try{hs=cr.createPolicy("vue",{createHTML:e=>e})}catch{}const Yo=hs?e=>hs.createHTML(e):e=>e,yc="http://www.w3.org/2000/svg",vc="http://www.w3.org/1998/Math/MathML",ze=typeof document<"u"?document:null,ur=ze&&ze.createElement("template"),bc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?ze.createElementNS(yc,e):t==="mathml"?ze.createElementNS(vc,e):n?ze.createElement(e,{is:n}):ze.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>ze.createTextNode(e),createComment:e=>ze.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ze.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{ur.innerHTML=Yo(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=ur.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ec=Symbol("_vtc");function xc(e,t,n){const s=e[Ec];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const fr=Symbol("_vod"),Sc=Symbol("_vsh"),wc=Symbol(""),Rc=/(^|;)\s*display\s*:/;function Pc(e,t,n){const s=e.style,r=ie(n);let o=!1;if(n&&!r){if(t)if(ie(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&vn(s,l,"")}else for(const i in t)n[i]==null&&vn(s,i,"");for(const i in n)i==="display"&&(o=!0),vn(s,i,n[i])}else if(r){if(t!==n){const i=s[wc];i&&(n+=";"+i),s.cssText=n,o=Rc.test(n)}}else t&&e.removeAttribute("style");fr in e&&(e[fr]=o?s.display:"",e[Sc]&&(s.display="none"))}const ar=/\s*!important$/;function vn(e,t,n){if(F(n))n.forEach(s=>vn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Cc(e,t);ar.test(n)?e.setProperty(pt(s),n.replace(ar,""),"important"):e[s]=n}}const dr=["Webkit","Moz","ms"],Zn={};function Cc(e,t){const n=Zn[t];if(n)return n;let s=Me(t);if(s!=="filter"&&s in e)return Zn[t]=s;s=Mn(s);for(let r=0;r<dr.length;r++){const o=dr[r]+s;if(o in e)return Zn[t]=o}return t}const hr="http://www.w3.org/1999/xlink";function pr(e,t,n,s,r,o=Ci(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(hr,t.slice(6,t.length)):e.setAttributeNS(hr,t,n):n==null||o&&!Gr(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Ue(n)?String(n):n)}function gr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Yo(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Gr(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function ut(e,t,n,s){e.addEventListener(t,n,s)}function Ac(e,t,n,s){e.removeEventListener(t,n,s)}const mr=Symbol("_vei");function Oc(e,t,n,s,r=null){const o=e[mr]||(e[mr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=Tc(t);if(s){const h=o[t]=Lc(s,r);ut(e,l,h,c)}else i&&(Ac(e,l,i,c),o[t]=void 0)}}const _r=/(?:Once|Passive|Capture)$/;function Tc(e){let t;if(_r.test(e)){t={};let s;for(;s=e.match(_r);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):pt(e.slice(2)),t]}let es=0;const Ic=Promise.resolve(),Mc=()=>es||(Ic.then(()=>es=0),es=Date.now());function Lc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Be(jc(s,n.value),t,5,[s])};return n.value=e,n.attached=Mc(),n}function jc(e,t){if(F(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const yr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Dc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?xc(e,s,i):t==="style"?Pc(e,n,s):Tn(t)?bs(t)||Oc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Nc(e,t,s,i))?(gr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&pr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ie(s))?gr(e,Me(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),pr(e,t,s,i))};function Nc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&yr(t)&&H(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return yr(t)&&ie(n)?!1:t in e}const Mt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return F(t)?n=>gn(t,n):t};function Fc(e){e.target.composing=!0}function vr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Xe=Symbol("_assign"),hf={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Xe]=Mt(r);const o=s||r.props&&r.props.type==="number";ut(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=bn(l)),e[Xe](l)}),n&&ut(e,"change",()=>{e.value=e.value.trim()}),t||(ut(e,"compositionstart",Fc),ut(e,"compositionend",vr),ut(e,"change",vr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[Xe]=Mt(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?bn(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},pf={deep:!0,created(e,t,n){e[Xe]=Mt(n),ut(e,"change",()=>{const s=e._modelValue,r=nn(e),o=e.checked,i=e[Xe];if(F(s)){const l=Rs(s,r),c=l!==-1;if(o&&!c)i(s.concat(r));else if(!o&&c){const h=[...s];h.splice(l,1),i(h)}}else if(Dt(s)){const l=new Set(s);o?l.add(r):l.delete(r),i(l)}else i(Xo(e,o))})},mounted:br,beforeUpdate(e,t,n){e[Xe]=Mt(n),br(e,t,n)}};function br(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(F(t))r=Rs(t,s.props.value)>-1;else if(Dt(t))r=t.has(s.props.value);else{if(t===n)return;r=ln(t,Xo(e,!0))}e.checked!==r&&(e.checked=r)}const gf={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=Dt(t);ut(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?bn(nn(i)):nn(i));e[Xe](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,Fn(()=>{e._assigning=!1})}),e[Xe]=Mt(s)},mounted(e,{value:t}){Er(e,t)},beforeUpdate(e,t,n){e[Xe]=Mt(n)},updated(e,{value:t}){e._assigning||Er(e,t)}};function Er(e,t){const n=e.multiple,s=F(t);if(!(n&&!s&&!Dt(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=nn(i);if(n)if(s){const c=typeof l;c==="string"||c==="number"?i.selected=t.some(h=>String(h)===String(l)):i.selected=Rs(t,l)>-1}else i.selected=t.has(l);else if(ln(nn(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function nn(e){return"_value"in e?e._value:e.value}function Xo(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const $c=["ctrl","shift","alt","meta"],Vc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>$c.some(n=>e[`${n}Key`]&&!t.includes(n))},mf=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Vc[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Hc={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},_f=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=pt(r.key);if(t.some(i=>i===o||Hc[i]===o))return e(r)})},kc=he({patchProp:Dc},bc);let xr;function Kc(){return xr||(xr=kl(kc))}const Uc=(...e)=>{const t=Kc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Wc(s);if(!r)return;const o=t._component;!H(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Bc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Bc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Wc(e){return ie(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Zo;const Un=e=>Zo=e,ei=Symbol();function ps(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var zt;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(zt||(zt={}));function qc(){const e=Yr(!0),t=e.run(()=>Dn({}));let n=[],s=[];const r=Ls({install(o){Un(r),r._a=o,o.provide(ei,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const ti=()=>{};function Sr(e,t,n,s=ti){e.push(t);const r=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&Xr()&&Ti(r),r}function Rt(e,...t){e.slice().forEach(n=>{n(...t)})}const Gc=e=>e(),wr=Symbol(),ts=Symbol();function gs(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];ps(r)&&ps(s)&&e.hasOwnProperty(n)&&!oe(s)&&!at(s)?e[n]=gs(r,s):e[n]=s}return e}const zc=Symbol();function Jc(e){return!ps(e)||!Object.prototype.hasOwnProperty.call(e,zc)}const{assign:ot}=Object;function Qc(e){return!!(oe(e)&&e.effect)}function Yc(e,t,n,s){const{state:r,actions:o,getters:i}=t,l=n.state.value[e];let c;function h(){l||(n.state.value[e]=r?r():{});const u=Zi(n.state.value[e]);return ot(u,o,Object.keys(i||{}).reduce((a,g)=>(a[g]=Ls(Oe(()=>{Un(n);const m=n._s.get(e);return i[g].call(m,m)})),a),{}))}return c=ni(e,h,t,n,s,!0),c}function ni(e,t,n={},s,r,o){let i;const l=ot({actions:{}},n),c={deep:!0};let h,u,a=[],g=[],m;const R=s.state.value[e];!o&&!R&&(s.state.value[e]={}),Dn({});let O;function V(U){let B;h=u=!1,typeof U=="function"?(U(s.state.value[e]),B={type:zt.patchFunction,storeId:e,events:m}):(gs(s.state.value[e],U),B={type:zt.patchObject,payload:U,storeId:e,events:m});const re=O=Symbol();Fn().then(()=>{O===re&&(h=!0)}),u=!0,Rt(a,B,s.state.value[e])}const D=o?function(){const{state:B}=n,re=B?B():{};this.$patch(pe=>{ot(pe,re)})}:ti;function I(){i.stop(),a=[],g=[],s._s.delete(e)}const j=(U,B="")=>{if(wr in U)return U[ts]=B,U;const re=function(){Un(s);const pe=Array.from(arguments),Ee=[],_e=[];function gt(k){Ee.push(k)}function nt(k){_e.push(k)}Rt(g,{args:pe,name:re[ts],store:K,after:gt,onError:nt});let se;try{se=U.apply(this&&this.$id===e?this:K,pe)}catch(k){throw Rt(_e,k),k}return se instanceof Promise?se.then(k=>(Rt(Ee,k),k)).catch(k=>(Rt(_e,k),Promise.reject(k))):(Rt(Ee,se),se)};return re[wr]=!0,re[ts]=B,re},T={_p:s,$id:e,$onAction:Sr.bind(null,g),$patch:V,$reset:D,$subscribe(U,B={}){const re=Sr(a,U,B.detached,()=>pe()),pe=i.run(()=>qt(()=>s.state.value[e],Ee=>{(B.flush==="sync"?u:h)&&U({storeId:e,type:zt.direct,events:m},Ee)},ot({},c,B)));return re},$dispose:I},K=cn(T);s._s.set(e,K);const Q=(s._a&&s._a.runWithContext||Gc)(()=>s._e.run(()=>(i=Yr()).run(()=>t({action:j}))));for(const U in Q){const B=Q[U];if(oe(B)&&!Qc(B)||at(B))o||(R&&Jc(B)&&(oe(B)?B.value=R[U]:gs(B,R[U])),s.state.value[e][U]=B);else if(typeof B=="function"){const re=j(B,U);Q[U]=re,l.actions[U]=B}}return ot(K,Q),ot(q(K),Q),Object.defineProperty(K,"$state",{get:()=>s.state.value[e],set:U=>{V(B=>{ot(B,U)})}}),s._p.forEach(U=>{ot(K,i.run(()=>U({store:K,app:s._a,pinia:s,options:l})))}),R&&o&&n.hydrate&&n.hydrate(K.$state,R),h=!0,u=!0,K}/*! #__NO_SIDE_EFFECTS__ */function yf(e,t,n){let s;const r=typeof t=="function";s=r?n:t;function o(i,l){const c=jl();return i=i||(c?Ie(ei,null):null),i&&Un(i),i=Zo,i._s.has(e)||(r?ni(e,t,s,i):Yc(e,s,i)),i._s.get(e)}return o.$id=e,o}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ct=typeof document<"u";function si(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Xc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&si(e.default)}const z=Object.assign;function ns(e,t){const n={};for(const s in t){const r=t[s];n[s]=je(r)?r.map(e):e(r)}return n}const Jt=()=>{},je=Array.isArray,ri=/#/g,Zc=/&/g,eu=/\//g,tu=/=/g,nu=/\?/g,oi=/\+/g,su=/%5B/g,ru=/%5D/g,ii=/%5E/g,ou=/%60/g,li=/%7B/g,iu=/%7C/g,ci=/%7D/g,lu=/%20/g;function Hs(e){return encodeURI(""+e).replace(iu,"|").replace(su,"[").replace(ru,"]")}function cu(e){return Hs(e).replace(li,"{").replace(ci,"}").replace(ii,"^")}function ms(e){return Hs(e).replace(oi,"%2B").replace(lu,"+").replace(ri,"%23").replace(Zc,"%26").replace(ou,"`").replace(li,"{").replace(ci,"}").replace(ii,"^")}function uu(e){return ms(e).replace(tu,"%3D")}function fu(e){return Hs(e).replace(ri,"%23").replace(nu,"%3F")}function au(e){return e==null?"":fu(e).replace(eu,"%2F")}function sn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const du=/\/$/,hu=e=>e.replace(du,"");function ss(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=_u(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:sn(i)}}function pu(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Rr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function gu(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Lt(t.matched[s],n.matched[r])&&ui(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Lt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ui(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!mu(e[n],t[n]))return!1;return!0}function mu(e,t){return je(e)?Pr(e,t):je(t)?Pr(t,e):e===t}function Pr(e,t){return je(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function _u(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const rt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var rn;(function(e){e.pop="pop",e.push="push"})(rn||(rn={}));var Qt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Qt||(Qt={}));function yu(e){if(!e)if(Ct){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),hu(e)}const vu=/^[^#]+#/;function bu(e,t){return e.replace(vu,"#")+t}function Eu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Bn=()=>({left:window.scrollX,top:window.scrollY});function xu(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Eu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Cr(e,t){return(history.state?history.state.position-t:-1)+e}const _s=new Map;function Su(e,t){_s.set(e,t)}function wu(e){const t=_s.get(e);return _s.delete(e),t}let Ru=()=>location.protocol+"//"+location.host;function fi(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),Rr(c,"")}return Rr(n,e)+s+r}function Pu(e,t,n,s){let r=[],o=[],i=null;const l=({state:g})=>{const m=fi(e,location),R=n.value,O=t.value;let V=0;if(g){if(n.value=m,t.value=g,i&&i===R){i=null;return}V=O?g.position-O.position:0}else s(m);r.forEach(D=>{D(n.value,R,{delta:V,type:rn.pop,direction:V?V>0?Qt.forward:Qt.back:Qt.unknown})})};function c(){i=n.value}function h(g){r.push(g);const m=()=>{const R=r.indexOf(g);R>-1&&r.splice(R,1)};return o.push(m),m}function u(){const{history:g}=window;g.state&&g.replaceState(z({},g.state,{scroll:Bn()}),"")}function a(){for(const g of o)g();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:h,destroy:a}}function Ar(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Bn():null}}function Cu(e){const{history:t,location:n}=window,s={value:fi(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,h,u){const a=e.indexOf("#"),g=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+c:Ru()+e+c;try{t[u?"replaceState":"pushState"](h,"",g),r.value=h}catch(m){console.error(m),n[u?"replace":"assign"](g)}}function i(c,h){const u=z({},t.state,Ar(r.value.back,c,r.value.forward,!0),h,{position:r.value.position});o(c,u,!0),s.value=c}function l(c,h){const u=z({},r.value,t.state,{forward:c,scroll:Bn()});o(u.current,u,!0);const a=z({},Ar(s.value,c,null),{position:u.position+1},h);o(c,a,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Au(e){e=yu(e);const t=Cu(e),n=Pu(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=z({location:"",base:e,go:s,createHref:bu.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Ou(e){return typeof e=="string"||e&&typeof e=="object"}function ai(e){return typeof e=="string"||typeof e=="symbol"}const di=Symbol("");var Or;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Or||(Or={}));function jt(e,t){return z(new Error,{type:e,[di]:!0},t)}function Ge(e,t){return e instanceof Error&&di in e&&(t==null||!!(e.type&t))}const Tr="[^/]+?",Tu={sensitive:!1,strict:!1,start:!0,end:!0},Iu=/[.+*?^${}()[\]/\\]/g;function Mu(e,t){const n=z({},Tu,t),s=[];let r=n.start?"^":"";const o=[];for(const h of e){const u=h.length?[]:[90];n.strict&&!h.length&&(r+="/");for(let a=0;a<h.length;a++){const g=h[a];let m=40+(n.sensitive?.25:0);if(g.type===0)a||(r+="/"),r+=g.value.replace(Iu,"\\$&"),m+=40;else if(g.type===1){const{value:R,repeatable:O,optional:V,regexp:D}=g;o.push({name:R,repeatable:O,optional:V});const I=D||Tr;if(I!==Tr){m+=10;try{new RegExp(`(${I})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${R}" (${I}): `+T.message)}}let j=O?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;a||(j=V&&h.length<2?`(?:/${j})`:"/"+j),V&&(j+="?"),r+=j,m+=20,V&&(m+=-8),O&&(m+=-20),I===".*"&&(m+=-50)}u.push(m)}s.push(u)}if(n.strict&&n.end){const h=s.length-1;s[h][s[h].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(h){const u=h.match(i),a={};if(!u)return null;for(let g=1;g<u.length;g++){const m=u[g]||"",R=o[g-1];a[R.name]=m&&R.repeatable?m.split("/"):m}return a}function c(h){let u="",a=!1;for(const g of e){(!a||!u.endsWith("/"))&&(u+="/"),a=!1;for(const m of g)if(m.type===0)u+=m.value;else if(m.type===1){const{value:R,repeatable:O,optional:V}=m,D=R in h?h[R]:"";if(je(D)&&!O)throw new Error(`Provided param "${R}" is an array but it is not repeatable (* or + modifiers)`);const I=je(D)?D.join("/"):D;if(!I)if(V)g.length<2&&(u.endsWith("/")?u=u.slice(0,-1):a=!0);else throw new Error(`Missing required param "${R}"`);u+=I}}return u||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Lu(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function hi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Lu(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Ir(s))return 1;if(Ir(r))return-1}return r.length-s.length}function Ir(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ju={type:0,value:""},Du=/[a-zA-Z0-9_]/;function Nu(e){if(!e)return[[]];if(e==="/")return[[ju]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${h}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,h="",u="";function a(){h&&(n===0?o.push({type:0,value:h}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${h}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:h,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),h="")}function g(){h+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(h&&a(),i()):c===":"?(a(),n=1):g();break;case 4:g(),n=s;break;case 1:c==="("?n=2:Du.test(c)?g():(a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${h}"`),a(),i(),r}function Fu(e,t,n){const s=Mu(Nu(e.path),n),r=z(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function $u(e,t){const n=[],s=new Map;t=Dr({strict:!1,end:!0,sensitive:!1},t);function r(a){return s.get(a)}function o(a,g,m){const R=!m,O=Lr(a);O.aliasOf=m&&m.record;const V=Dr(t,a),D=[O];if("alias"in a){const T=typeof a.alias=="string"?[a.alias]:a.alias;for(const K of T)D.push(Lr(z({},O,{components:m?m.record.components:O.components,path:K,aliasOf:m?m.record:O})))}let I,j;for(const T of D){const{path:K}=T;if(g&&K[0]!=="/"){const le=g.record.path,Q=le[le.length-1]==="/"?"":"/";T.path=g.record.path+(K&&Q+K)}if(I=Fu(T,g,V),m?m.alias.push(I):(j=j||I,j!==I&&j.alias.push(I),R&&a.name&&!jr(I)&&i(a.name)),pi(I)&&c(I),O.children){const le=O.children;for(let Q=0;Q<le.length;Q++)o(le[Q],I,m&&m.children[Q])}m=m||I}return j?()=>{i(j)}:Jt}function i(a){if(ai(a)){const g=s.get(a);g&&(s.delete(a),n.splice(n.indexOf(g),1),g.children.forEach(i),g.alias.forEach(i))}else{const g=n.indexOf(a);g>-1&&(n.splice(g,1),a.record.name&&s.delete(a.record.name),a.children.forEach(i),a.alias.forEach(i))}}function l(){return n}function c(a){const g=ku(a,n);n.splice(g,0,a),a.record.name&&!jr(a)&&s.set(a.record.name,a)}function h(a,g){let m,R={},O,V;if("name"in a&&a.name){if(m=s.get(a.name),!m)throw jt(1,{location:a});V=m.record.name,R=z(Mr(g.params,m.keys.filter(j=>!j.optional).concat(m.parent?m.parent.keys.filter(j=>j.optional):[]).map(j=>j.name)),a.params&&Mr(a.params,m.keys.map(j=>j.name))),O=m.stringify(R)}else if(a.path!=null)O=a.path,m=n.find(j=>j.re.test(O)),m&&(R=m.parse(O),V=m.record.name);else{if(m=g.name?s.get(g.name):n.find(j=>j.re.test(g.path)),!m)throw jt(1,{location:a,currentLocation:g});V=m.record.name,R=z({},g.params,a.params),O=m.stringify(R)}const D=[];let I=m;for(;I;)D.unshift(I.record),I=I.parent;return{name:V,path:O,params:R,matched:D,meta:Hu(D)}}e.forEach(a=>o(a));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:h,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:r}}function Mr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Lr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Vu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Vu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function jr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Hu(e){return e.reduce((t,n)=>z(t,n.meta),{})}function Dr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function ku(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;hi(e,t[o])<0?s=o:n=o+1}const r=Ku(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Ku(e){let t=e;for(;t=t.parent;)if(pi(t)&&hi(e,t)===0)return t}function pi({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Uu(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(oi," "),i=o.indexOf("="),l=sn(i<0?o:o.slice(0,i)),c=i<0?null:sn(o.slice(i+1));if(l in t){let h=t[l];je(h)||(h=t[l]=[h]),h.push(c)}else t[l]=c}return t}function Nr(e){let t="";for(let n in e){const s=e[n];if(n=uu(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(je(s)?s.map(o=>o&&ms(o)):[s&&ms(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Bu(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=je(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Wu=Symbol(""),Fr=Symbol(""),Wn=Symbol(""),ks=Symbol(""),ys=Symbol("");function Vt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ct(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const h=g=>{g===!1?c(jt(4,{from:n,to:t})):g instanceof Error?c(g):Ou(g)?c(jt(2,{from:t,to:g})):(i&&s.enterCallbacks[r]===i&&typeof g=="function"&&i.push(g),l())},u=o(()=>e.call(s&&s.instances[r],t,n,h));let a=Promise.resolve(u);e.length<3&&(a=a.then(h)),a.catch(g=>c(g))})}function rs(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(si(c)){const u=(c.__vccOpts||c)[t];u&&o.push(ct(u,n,s,i,l,r))}else{let h=c();o.push(()=>h.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const a=Xc(u)?u.default:u;i.mods[l]=u,i.components[l]=a;const m=(a.__vccOpts||a)[t];return m&&ct(m,n,s,i,l,r)()}))}}return o}function $r(e){const t=Ie(Wn),n=Ie(ks),s=Oe(()=>{const c=bt(e.to);return t.resolve(c)}),r=Oe(()=>{const{matched:c}=s.value,{length:h}=c,u=c[h-1],a=n.matched;if(!u||!a.length)return-1;const g=a.findIndex(Lt.bind(null,u));if(g>-1)return g;const m=Vr(c[h-2]);return h>1&&Vr(u)===m&&a[a.length-1].path!==m?a.findIndex(Lt.bind(null,c[h-2])):g}),o=Oe(()=>r.value>-1&&Qu(n.params,s.value.params)),i=Oe(()=>r.value>-1&&r.value===n.matched.length-1&&ui(n.params,s.value.params));function l(c={}){if(Ju(c)){const h=t[bt(e.replace)?"replace":"push"](bt(e.to)).catch(Jt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>h),h}return Promise.resolve()}return{route:s,href:Oe(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function qu(e){return e.length===1?e[0]:e}const Gu=$n({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:$r,setup(e,{slots:t}){const n=cn($r(e)),{options:s}=Ie(Wn),r=Oe(()=>({[Hr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Hr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&qu(t.default(n));return e.custom?o:Qo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),zu=Gu;function Ju(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Qu(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!je(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Vr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Hr=(e,t,n)=>e??t??n,Yu=$n({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ie(ys),r=Oe(()=>e.route||s.value),o=Ie(Fr,0),i=Oe(()=>{let h=bt(o);const{matched:u}=r.value;let a;for(;(a=u[h])&&!a.components;)h++;return h}),l=Oe(()=>r.value.matched[i.value]);mn(Fr,Oe(()=>i.value+1)),mn(Wu,l),mn(ys,r);const c=Dn();return qt(()=>[c.value,l.value,e.name],([h,u,a],[g,m,R])=>{u&&(u.instances[a]=h,m&&m!==u&&h&&h===g&&(u.leaveGuards.size||(u.leaveGuards=m.leaveGuards),u.updateGuards.size||(u.updateGuards=m.updateGuards))),h&&u&&(!m||!Lt(u,m)||!g)&&(u.enterCallbacks[a]||[]).forEach(O=>O(h))},{flush:"post"}),()=>{const h=r.value,u=e.name,a=l.value,g=a&&a.components[u];if(!g)return kr(n.default,{Component:g,route:h});const m=a.props[u],R=m?m===!0?h.params:typeof m=="function"?m(h):m:null,V=Qo(g,z({},R,t,{onVnodeUnmounted:D=>{D.component.isUnmounted&&(a.instances[u]=null)},ref:c}));return kr(n.default,{Component:V,route:h})||V}}});function kr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const gi=Yu;function Xu(e){const t=$u(e.routes,e),n=e.parseQuery||Uu,s=e.stringifyQuery||Nr,r=e.history,o=Vt(),i=Vt(),l=Vt(),c=Qi(rt);let h=rt;Ct&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ns.bind(null,y=>""+y),a=ns.bind(null,au),g=ns.bind(null,sn);function m(y,A){let P,M;return ai(y)?(P=t.getRecordMatcher(y),M=A):M=y,t.addRoute(M,P)}function R(y){const A=t.getRecordMatcher(y);A&&t.removeRoute(A)}function O(){return t.getRoutes().map(y=>y.record)}function V(y){return!!t.getRecordMatcher(y)}function D(y,A){if(A=z({},A||c.value),typeof y=="string"){const p=ss(n,y,A.path),_=t.resolve({path:p.path},A),b=r.createHref(p.fullPath);return z(p,_,{params:g(_.params),hash:sn(p.hash),redirectedFrom:void 0,href:b})}let P;if(y.path!=null)P=z({},y,{path:ss(n,y.path,A.path).path});else{const p=z({},y.params);for(const _ in p)p[_]==null&&delete p[_];P=z({},y,{params:a(p)}),A.params=a(A.params)}const M=t.resolve(P,A),X=y.hash||"";M.params=u(g(M.params));const f=pu(s,z({},y,{hash:cu(X),path:M.path})),d=r.createHref(f);return z({fullPath:f,hash:X,query:s===Nr?Bu(y.query):y.query||{}},M,{redirectedFrom:void 0,href:d})}function I(y){return typeof y=="string"?ss(n,y,c.value.path):z({},y)}function j(y,A){if(h!==y)return jt(8,{from:A,to:y})}function T(y){return Q(y)}function K(y){return T(z(I(y),{replace:!0}))}function le(y){const A=y.matched[y.matched.length-1];if(A&&A.redirect){const{redirect:P}=A;let M=typeof P=="function"?P(y):P;return typeof M=="string"&&(M=M.includes("?")||M.includes("#")?M=I(M):{path:M},M.params={}),z({query:y.query,hash:y.hash,params:M.path!=null?{}:y.params},M)}}function Q(y,A){const P=h=D(y),M=c.value,X=y.state,f=y.force,d=y.replace===!0,p=le(P);if(p)return Q(z(I(p),{state:typeof p=="object"?z({},X,p.state):X,force:f,replace:d}),A||P);const _=P;_.redirectedFrom=A;let b;return!f&&gu(s,M,P)&&(b=jt(16,{to:_,from:M}),De(M,M,!0,!1)),(b?Promise.resolve(b):re(_,M)).catch(v=>Ge(v)?Ge(v,2)?v:st(v):G(v,_,M)).then(v=>{if(v){if(Ge(v,2))return Q(z({replace:d},I(v.to),{state:typeof v.to=="object"?z({},X,v.to.state):X,force:f}),A||_)}else v=Ee(_,M,!0,d,X);return pe(_,M,v),v})}function U(y,A){const P=j(y,A);return P?Promise.reject(P):Promise.resolve()}function B(y){const A=St.values().next().value;return A&&typeof A.runWithContext=="function"?A.runWithContext(y):y()}function re(y,A){let P;const[M,X,f]=Zu(y,A);P=rs(M.reverse(),"beforeRouteLeave",y,A);for(const p of M)p.leaveGuards.forEach(_=>{P.push(ct(_,y,A))});const d=U.bind(null,y,A);return P.push(d),Ce(P).then(()=>{P=[];for(const p of o.list())P.push(ct(p,y,A));return P.push(d),Ce(P)}).then(()=>{P=rs(X,"beforeRouteUpdate",y,A);for(const p of X)p.updateGuards.forEach(_=>{P.push(ct(_,y,A))});return P.push(d),Ce(P)}).then(()=>{P=[];for(const p of f)if(p.beforeEnter)if(je(p.beforeEnter))for(const _ of p.beforeEnter)P.push(ct(_,y,A));else P.push(ct(p.beforeEnter,y,A));return P.push(d),Ce(P)}).then(()=>(y.matched.forEach(p=>p.enterCallbacks={}),P=rs(f,"beforeRouteEnter",y,A,B),P.push(d),Ce(P))).then(()=>{P=[];for(const p of i.list())P.push(ct(p,y,A));return P.push(d),Ce(P)}).catch(p=>Ge(p,8)?p:Promise.reject(p))}function pe(y,A,P){l.list().forEach(M=>B(()=>M(y,A,P)))}function Ee(y,A,P,M,X){const f=j(y,A);if(f)return f;const d=A===rt,p=Ct?history.state:{};P&&(M||d?r.replace(y.fullPath,z({scroll:d&&p&&p.scroll},X)):r.push(y.fullPath,X)),c.value=y,De(y,A,P,d),st()}let _e;function gt(){_e||(_e=r.listen((y,A,P)=>{if(!an.listening)return;const M=D(y),X=le(M);if(X){Q(z(X,{replace:!0,force:!0}),M).catch(Jt);return}h=M;const f=c.value;Ct&&Su(Cr(f.fullPath,P.delta),Bn()),re(M,f).catch(d=>Ge(d,12)?d:Ge(d,2)?(Q(z(I(d.to),{force:!0}),M).then(p=>{Ge(p,20)&&!P.delta&&P.type===rn.pop&&r.go(-1,!1)}).catch(Jt),Promise.reject()):(P.delta&&r.go(-P.delta,!1),G(d,M,f))).then(d=>{d=d||Ee(M,f,!1),d&&(P.delta&&!Ge(d,8)?r.go(-P.delta,!1):P.type===rn.pop&&Ge(d,20)&&r.go(-1,!1)),pe(M,f,d)}).catch(Jt)}))}let nt=Vt(),se=Vt(),k;function G(y,A,P){st(y);const M=se.list();return M.length?M.forEach(X=>X(y,A,P)):console.error(y),Promise.reject(y)}function We(){return k&&c.value!==rt?Promise.resolve():new Promise((y,A)=>{nt.add([y,A])})}function st(y){return k||(k=!y,gt(),nt.list().forEach(([A,P])=>y?P(y):A()),nt.reset()),y}function De(y,A,P,M){const{scrollBehavior:X}=e;if(!Ct||!X)return Promise.resolve();const f=!P&&wu(Cr(y.fullPath,0))||(M||!P)&&history.state&&history.state.scroll||null;return Fn().then(()=>X(y,A,f)).then(d=>d&&xu(d)).catch(d=>G(d,y,A))}const ye=y=>r.go(y);let xt;const St=new Set,an={currentRoute:c,listening:!0,addRoute:m,removeRoute:R,clearRoutes:t.clearRoutes,hasRoute:V,getRoutes:O,resolve:D,options:e,push:T,replace:K,go:ye,back:()=>ye(-1),forward:()=>ye(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:se.add,isReady:We,install(y){const A=this;y.component("RouterLink",zu),y.component("RouterView",gi),y.config.globalProperties.$router=A,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>bt(c)}),Ct&&!xt&&c.value===rt&&(xt=!0,T(r.location).catch(X=>{}));const P={};for(const X in rt)Object.defineProperty(P,X,{get:()=>c.value[X],enumerable:!0});y.provide(Wn,A),y.provide(ks,po(P)),y.provide(ys,c);const M=y.unmount;St.add(y),y.unmount=function(){St.delete(y),St.size<1&&(h=rt,_e&&_e(),_e=null,c.value=rt,xt=!1,k=!1),M()}}};function Ce(y){return y.reduce((A,P)=>A.then(()=>B(P)),Promise.resolve())}return an}function Zu(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(h=>Lt(h,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(h=>Lt(h,c))||r.push(c))}return[n,s,r]}function vf(){return Ie(Wn)}function bf(e){return Ie(ks)}const ef={key:0,class:"position-fixed bottom-0 start-0 end-0 p-3",style:{"z-index":"1050"}},tf=$n({__name:"PWAInstallPrompt",setup(e){const t=Dn(!1);let n=null;const s=async()=>{if(n){n.prompt();const{outcome:o}=await n.userChoice;o==="accepted"&&console.log("PWA instalado!"),n=null,t.value=!1}},r=()=>{t.value=!1,localStorage.setItem("pwa-install-dismissed","true")};return Po(()=>{localStorage.getItem("pwa-install-dismissed")||(window.addEventListener("beforeinstallprompt",i=>{i.preventDefault(),n=i,t.value=!0}),window.addEventListener("appinstalled",()=>{console.log("PWA foi instalado!"),t.value=!1}),window.matchMedia("(display-mode: standalone)").matches&&console.log("Rodando como PWA!"))}),(o,i)=>t.value?($s(),qo("div",ef,[Ae("div",{class:"alert alert-info d-flex align-items-center justify-content-between shadow-lg"},[i[0]||(i[0]=Ae("div",{class:"d-flex align-items-center"},[Ae("span",{class:"me-2"},"📱"),Ae("div",null,[Ae("strong",null,"Instalar App"),Ae("br"),Ae("small",null,"Adicione o Choose Me à sua tela inicial!")])],-1)),Ae("div",null,[Ae("button",{onClick:s,class:"btn btn-primary btn-sm me-2"}," Instalar "),Ae("button",{onClick:r,class:"btn btn-outline-secondary btn-sm"}," ✕ ")])])])):ic("",!0)}}),nf={id:"app"},sf=$n({__name:"App",setup(e){return(t,n)=>($s(),qo("div",nf,[de(bt(gi)),de(tf)]))}}),rf="modulepreload",of=function(e){return"/"+e},Kr={},we=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let i=function(h){return Promise.all(h.map(u=>Promise.resolve(u).then(a=>({status:"fulfilled",value:a}),a=>({status:"rejected",reason:a}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),c=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));r=i(n.map(h=>{if(h=of(h),h in Kr)return;Kr[h]=!0;const u=h.endsWith(".css"),a=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${h}"]${a}`))return;const g=document.createElement("link");if(g.rel=u?"stylesheet":rf,u||(g.as="script"),g.crossOrigin="",g.href=h,c&&g.setAttribute("nonce",c),document.head.appendChild(g),u)return new Promise((m,R)=>{g.addEventListener("load",m),g.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${h}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})},mi=Xu({history:Au("/"),routes:[{path:"/",name:"home",component:()=>we(()=>import("./HomeView-P5wpAoCm.js"),[])},{path:"/admin",name:"admin",component:()=>we(()=>import("./AdminLayout-BHMInFHR.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0},children:[{path:"",name:"admin-dashboard",component:()=>we(()=>import("./DashboardView-DN9Oa-GN.js"),[])},{path:"enigmas",name:"admin-enigmas",component:()=>we(()=>import("./EnigmasView-jfL39SQ2.js"),[])},{path:"configuracoes",name:"admin-config",component:()=>we(()=>import("./ConfiguracoesView-gtxUdBBr.js"),[])}]},{path:"/admin-login",name:"admin-login",component:()=>we(()=>import("./AdminLoginView-BRciyepv.js"),__vite__mapDeps([2,1]))},{path:"/namorada",name:"namorada",component:()=>we(()=>import("./NamoradaLayout-CXfjMvdo.js"),[]),children:[{path:"",name:"namorada-inicio",component:()=>we(()=>import("./InicioView-BRDSy08p.js"),[])},{path:"enigma/:id",name:"namorada-enigma",component:()=>we(()=>import("./EnigmaView-CG5btywX.js"),[])},{path:"progresso",name:"namorada-progresso",component:()=>we(()=>import("./ProgressoView-DOxCUNJY.js"),[])},{path:"pedido",name:"namorada-pedido",component:()=>we(()=>import("./PedidoView-DSxZNCJ8.js"),__vite__mapDeps([3,4,5]))},{path:"cronometro",name:"namorada-cronometro",component:()=>we(()=>import("./CronometroView-7oAqaKx1.js"),__vite__mapDeps([6,4,7]))}]}]});mi.beforeEach((e,t,n)=>{e.meta.requiresAuth?localStorage.getItem("chooseme-admin")==="true"?n():n("/admin-login"):n()});function lf(e={}){const{immediate:t=!1,onNeedRefresh:n,onOfflineReady:s,onRegistered:r,onRegisteredSW:o,onRegisterError:i}=e;let l,c;const h=async(a=!0)=>{await c};async function u(){if("serviceWorker"in navigator){if(l=await we(async()=>{const{Workbox:a}=await import("./workbox-window.prod.es5-B9K5rw8f.js");return{Workbox:a}},[]).then(({Workbox:a})=>new a("/sw.js",{scope:"/",type:"classic"})).catch(a=>{i==null||i(a)}),!l)return;l.addEventListener("activated",a=>{(a.isUpdate||a.isExternal)&&window.location.reload()}),l.addEventListener("installed",a=>{a.isUpdate||s==null||s()}),l.register({immediate:t}).then(a=>{o?o("/sw.js",a):r==null||r(a)}).catch(a=>{i==null||i(a)})}}return c=u(),h}const cf=lf({onNeedRefresh(){confirm("Nova versão disponível! Deseja atualizar?")&&cf(!0)},onOfflineReady(){console.log("App pronto para funcionar offline!")}}),Ks=Uc(sf);Ks.use(qc());Ks.use(mi);Ks.mount("#app");export{bf as A,_f as B,Co as C,Je as F,Ae as a,de as b,qo as c,$n as d,oc as e,Dn as f,Oe as g,af as h,cn as i,uf as j,Po as k,pf as l,gf as m,df as n,$s as o,ic as p,mf as q,ff as r,ws as s,Oi as t,vf as u,hf as v,ul as w,yf as x,Ss as y,nc as z};

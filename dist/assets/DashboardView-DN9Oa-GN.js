import{d as p,f as r,g as b,c as l,a as t,t as e,b as c,w as v,r as _,F as f,h as y,e as o,o as u}from"./index-BDYoTXYr.js";const C={class:"space-y-6"},k={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},w={class:"stat bg-primary text-primary-content rounded-lg"},E={class:"stat-value"},N={class:"stat bg-secondary text-secondary-content rounded-lg"},V={class:"stat-value"},P={class:"stat-desc text-secondary-content/70"},R={class:"stat bg-accent text-accent-content rounded-lg"},A={class:"stat-value text-sm"},B={class:"card bg-base-100 shadow-xl"},D={class:"card-body"},S={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"},T={class:"card bg-base-100 shadow-xl"},j={class:"card-body"},z={class:"space-y-2"},F={class:"text-sm"},L=p({__name:"DashboardView",setup(G){const d=r(0),a=r(0),m=b(()=>a.value===0?"Não iniciada":a.value===d.value?"Completa!":"Em progresso"),g=r([{id:1,data:"14/06/2025",descricao:"Sistema inicializado"}]),x=()=>{confirm("Tem certeza que deseja resetar todo o progresso?")&&(a.value=0,alert("Progresso resetado!"))};return(q,s)=>{const n=_("router-link");return u(),l("div",C,[s[13]||(s[13]=t("div",{class:"text-center"},[t("h1",{class:"text-4xl font-bold text-primary mb-2"},"Dashboard Administrativo"),t("p",{class:"text-lg text-base-content/70"},"Gerencie sua jornada de amor")],-1)),t("div",k,[t("div",w,[s[0]||(s[0]=t("div",{class:"stat-figure"},[t("div",{class:"text-3xl"},"🧩")],-1)),s[1]||(s[1]=t("div",{class:"stat-title text-primary-content/70"},"Total de Enigmas",-1)),t("div",E,e(d.value),1),s[2]||(s[2]=t("div",{class:"stat-desc text-primary-content/70"},"Criados no sistema",-1))]),t("div",N,[s[3]||(s[3]=t("div",{class:"stat-figure"},[t("div",{class:"text-3xl"},"🔑")],-1)),s[4]||(s[4]=t("div",{class:"stat-title text-secondary-content/70"},"Chaves Coletadas",-1)),t("div",V,e(a.value),1),t("div",P,"de "+e(d.value),1)]),t("div",R,[s[5]||(s[5]=t("div",{class:"stat-figure"},[t("div",{class:"text-3xl"},"💖")],-1)),s[6]||(s[6]=t("div",{class:"stat-title text-accent-content/70"},"Status",-1)),t("div",A,e(m.value),1),s[7]||(s[7]=t("div",{class:"stat-desc text-accent-content/70"},"Progresso atual",-1))])]),t("div",B,[t("div",D,[s[11]||(s[11]=t("h2",{class:"card-title"},"Ações Rápidas",-1)),t("div",S,[c(n,{to:"/admin/enigmas",class:"btn btn-primary"},{default:v(()=>s[8]||(s[8]=[o(" 🧩 Gerenciar Enigmas ")])),_:1,__:[8]}),c(n,{to:"/admin/configuracoes",class:"btn btn-secondary"},{default:v(()=>s[9]||(s[9]=[o(" ⚙️ Configurações ")])),_:1,__:[9]}),t("button",{onClick:x,class:"btn btn-warning"}," 🔄 Resetar Progresso "),c(n,{to:"/namorada",class:"btn btn-accent"},{default:v(()=>s[10]||(s[10]=[o(" 👀 Ver como Namorada ")])),_:1,__:[10]})])])]),t("div",T,[t("div",j,[s[12]||(s[12]=t("h2",{class:"card-title"},"Atividade Recente",-1)),t("div",z,[(u(!0),l(f,null,y(g.value,i=>(u(),l("div",{key:i.id,class:"alert alert-info"},[t("div",F,[t("strong",null,e(i.data),1),o(" - "+e(i.descricao),1)])]))),128))])])])])}}});export{L as default};

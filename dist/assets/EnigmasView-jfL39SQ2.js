import{d as w,f as r,i as y,c as m,a as t,F as h,h as k,t as u,j as c,v as p,o as b}from"./index-BDYoTXYr.js";const C={class:"space-y-6"},j={class:"grid gap-4"},M={class:"card-body"},V={class:"flex justify-between items-start"},N={class:"flex-1"},U={class:"card-title"},z={class:"text-base-content/70 mb-2"},B={class:"badge badge-outline"},D={class:"flex gap-2"},I=["onClick"],T=["onClick"],q={class:"modal-box"},F={class:"font-bold text-lg"},O={class:"form-control w-full mt-4"},P={class:"form-control w-full mt-4"},R={class:"form-control w-full mt-4"},S={class:"form-control w-full mt-4"},L=w({__name:"EnigmasView",setup($){const i=r(),d=r(!1),n=r(null),o=r([{id:1,titulo:"Nosso Primeiro Encontro",pergunta:"Em que lugar nos conhecemos pela primeira vez?",resposta:"faculdade",dica:"Um lugar de aprendizado..."}]),l=y({titulo:"",pergunta:"",resposta:"",dica:""}),f=()=>{var a;d.value=!1,n.value=null,Object.assign(l,{titulo:"",pergunta:"",resposta:"",dica:""}),(a=i.value)==null||a.showModal()},g=a=>{var s;d.value=!0,n.value=a.id,Object.assign(l,a),(s=i.value)==null||s.showModal()},x=()=>{if(d.value&&n.value){const a=o.value.findIndex(s=>s.id===n.value);a!==-1&&(o.value[a]={...l,id:n.value})}else{const a=Math.max(...o.value.map(s=>s.id),0)+1;o.value.push({...l,id:a})}v()},_=a=>{confirm("Tem certeza que deseja excluir este enigma?")&&(o.value=o.value.filter(s=>s.id!==a))},v=()=>{var a;(a=i.value)==null||a.close()};return(a,s)=>(b(),m("div",C,[t("div",{class:"flex justify-between items-center"},[s[4]||(s[4]=t("h1",{class:"text-3xl font-bold text-primary"},"Gerenciar Enigmas",-1)),t("button",{onClick:f,class:"btn btn-primary"}," ➕ Novo Enigma ")]),t("div",j,[(b(!0),m(h,null,k(o.value,e=>(b(),m("div",{key:e.id,class:"card bg-base-100 shadow-xl"},[t("div",M,[t("div",V,[t("div",N,[t("h2",U,u(e.titulo),1),t("p",z,u(e.pergunta),1),t("div",B,"Resposta: "+u(e.resposta),1)]),t("div",D,[t("button",{onClick:E=>g(e),class:"btn btn-sm btn-secondary"}," ✏️ Editar ",8,I),t("button",{onClick:E=>_(e.id),class:"btn btn-sm btn-error"}," 🗑️ Excluir ",8,T)])])])]))),128))]),t("dialog",{ref_key:"modalEnigma",ref:i,class:"modal"},[t("div",q,[t("h3",F,u(d.value?"Editar":"Novo")+" Enigma",1),t("div",O,[s[5]||(s[5]=t("label",{class:"label"},[t("span",{class:"label-text"},"Título do Enigma")],-1)),c(t("input",{"onUpdate:modelValue":s[0]||(s[0]=e=>l.titulo=e),type:"text",class:"input input-bordered w-full"},null,512),[[p,l.titulo]])]),t("div",P,[s[6]||(s[6]=t("label",{class:"label"},[t("span",{class:"label-text"},"Pergunta/Enigma")],-1)),c(t("textarea",{"onUpdate:modelValue":s[1]||(s[1]=e=>l.pergunta=e),class:"textarea textarea-bordered h-24"},null,512),[[p,l.pergunta]])]),t("div",R,[s[7]||(s[7]=t("label",{class:"label"},[t("span",{class:"label-text"},"Resposta Correta")],-1)),c(t("input",{"onUpdate:modelValue":s[2]||(s[2]=e=>l.resposta=e),type:"text",class:"input input-bordered w-full"},null,512),[[p,l.resposta]])]),t("div",S,[s[8]||(s[8]=t("label",{class:"label"},[t("span",{class:"label-text"},"Dica (opcional)")],-1)),c(t("input",{"onUpdate:modelValue":s[3]||(s[3]=e=>l.dica=e),type:"text",class:"input input-bordered w-full"},null,512),[[p,l.dica]])]),t("div",{class:"modal-action"},[t("button",{onClick:x,class:"btn btn-primary"},"Salvar"),t("button",{onClick:v,class:"btn"},"Cancelar")])])],512)]))}});export{L as default};

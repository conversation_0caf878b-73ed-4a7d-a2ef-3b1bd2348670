import{d as E,f,g as n,k as M,c as a,a as s,p as b,t as l,y as S,F as _,h,b as y,e as x,w as k,r as q,s as g,o as i}from"./index-BDYoTXYr.js";const V={class:"space-y-6"},$={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},z={class:"stat bg-gradient-to-r from-pink-400 to-pink-600 text-white rounded-lg"},A={class:"stat-value"},B={class:"stat-desc text-white/80"},J={class:"stat bg-gradient-to-r from-purple-400 to-purple-600 text-white rounded-lg"},D={class:"stat-value"},Q={class:"stat bg-gradient-to-r from-indigo-400 to-indigo-600 text-white rounded-lg"},F={class:"stat-value text-sm"},I={class:"stat-desc text-white/80"},L={class:"card bg-base-100 shadow-xl"},O={class:"card-body"},T={class:"flex justify-center mb-8"},G={class:"space-y-4"},H={class:"flex-shrink-0"},K={key:0,class:"w-12 h-12 bg-success rounded-full flex items-center justify-center text-white"},U={key:1,class:"w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white"},W={key:2,class:"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-gray-600"},X={class:"flex-1"},Y={class:"text-sm text-base-content/70"},Z={key:0,class:"text-xs text-base-content/50"},ss={class:"flex-shrink-0"},es={key:0,class:"badge badge-success"},ts={key:1,class:"badge badge-primary"},os={key:2,class:"badge badge-ghost"},as={class:"card bg-base-100 shadow-xl"},ls={class:"card-body"},is={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"},ds={class:"text-2xl"},rs={class:"text-sm text-base-content/70"},ns={key:0,class:"card bg-gradient-to-r from-pink-100 to-purple-100 shadow-xl"},us={class:"card-body text-center"},cs={class:"text-lg mb-4"},vs={key:1,class:"card bg-gradient-to-r from-green-100 to-emerald-100 shadow-xl"},gs={class:"card-body text-center"},xs=E({__name:"ProgressoView",setup(ms){const u=f([{id:1,titulo:"Nosso Primeiro Encontro",resolvido:!1,disponivel:!0},{id:2,titulo:"Nossa Primeira Conversa",resolvido:!1,disponivel:!1},{id:3,titulo:"Nosso Primeiro Momento Especial",resolvido:!1,disponivel:!1},{id:4,titulo:"O Que Mais Amo em Você",resolvido:!1,disponivel:!1},{id:5,titulo:"Nossos Sonhos Juntos",resolvido:!1,disponivel:!1}]),c=f([{id:1,titulo:"Primeira Chave",descricao:"Resolva seu primeiro enigma",icone:"🔑",desbloqueada:!1},{id:2,titulo:"Meio Caminho",descricao:"Resolva 50% dos enigmas",icone:"🌟",desbloqueada:!1},{id:3,titulo:"Quase Lá",descricao:"Resolva 80% dos enigmas",icone:"🚀",desbloqueada:!1},{id:4,titulo:"Mestre dos Enigmas",descricao:"Resolva todos os enigmas",icone:"👑",desbloqueada:!1}]),r=n(()=>u.value.length),d=n(()=>u.value.filter(o=>o.resolvido).length),m=n(()=>Math.round(d.value/r.value*100)),w=n(()=>d.value===r.value),C=n(()=>d.value===0?"Iniciante":d.value===r.value?"Completo!":d.value>=r.value*.8?"Quase lá!":d.value>=r.value*.5?"Progredindo":"Começando"),P=n(()=>d.value===0?"Comece sua jornada!":d.value===r.value?"Parabéns! 🎉":"Continue assim!"),p=n(()=>u.value.find(o=>!o.resolvido&&o.disponivel)),N=()=>{const o=JSON.parse(localStorage.getItem("chooseme-progresso")||"{}");u.value.forEach((e,v)=>{e.resolvido=o[`enigma_${e.id}`]||!1,v===0?e.disponivel=!0:e.disponivel=u.value[v-1].resolvido,e.resolvido&&o[`enigma_${e.id}_data`]&&(e.dataResolucao=o[`enigma_${e.id}_data`])}),R()},R=()=>{const o=d.value,e=r.value;c.value[0].desbloqueada=o>=1,c.value[1].desbloqueada=o>=Math.ceil(e*.5),c.value[2].desbloqueada=o>=Math.ceil(e*.8),c.value[3].desbloqueada=o===e};return M(()=>{N()}),(o,e)=>{const v=q("router-link");return i(),a("div",V,[e[15]||(e[15]=s("div",{class:"text-center"},[s("h1",{class:"text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-4"}," Meu Progresso 📊 "),s("p",{class:"text-lg text-gray-700"}," Acompanhe sua jornada através dos enigmas do amor ")],-1)),s("div",$,[s("div",z,[e[0]||(e[0]=s("div",{class:"stat-figure"},[s("div",{class:"text-3xl"},"🔑")],-1)),e[1]||(e[1]=s("div",{class:"stat-title text-white/80"},"Chaves Coletadas",-1)),s("div",A,l(d.value),1),s("div",B,"de "+l(r.value)+" total",1)]),s("div",J,[e[2]||(e[2]=s("div",{class:"stat-figure"},[s("div",{class:"text-3xl"},"📈")],-1)),e[3]||(e[3]=s("div",{class:"stat-title text-white/80"},"Progresso",-1)),s("div",D,l(m.value)+"%",1),e[4]||(e[4]=s("div",{class:"stat-desc text-white/80"},"Completo",-1))]),s("div",Q,[e[5]||(e[5]=s("div",{class:"stat-figure"},[s("div",{class:"text-3xl"},"⭐")],-1)),e[6]||(e[6]=s("div",{class:"stat-title text-white/80"},"Status",-1)),s("div",F,l(C.value),1),s("div",I,l(P.value),1)])]),s("div",L,[s("div",O,[e[7]||(e[7]=s("h2",{class:"card-title justify-center mb-6"},"Jornada dos Enigmas",-1)),s("div",T,[s("div",{class:"radial-progress text-primary text-4xl",style:S(`--value:${m.value}; --size:12rem; --thickness: 8px`),role:"progressbar"},l(m.value)+"% ",5)]),s("div",G,[(i(!0),a(_,null,h(u.value,(t,j)=>(i(),a("div",{key:t.id,class:g(["flex items-center gap-4 p-4 rounded-lg",t.resolvido?"bg-success/10":"bg-base-200"])},[s("div",H,[t.resolvido?(i(),a("div",K," ✅ ")):t.disponivel?(i(),a("div",U,l(j+1),1)):(i(),a("div",W," 🔒 "))]),s("div",X,[s("h3",{class:g(["font-bold",t.resolvido?"text-success":""])},l(t.titulo),3),s("p",Y,l(t.resolvido?"Resolvido com sucesso! 🎉":t.disponivel?"Disponível para resolver":"Aguardando enigma anterior"),1),t.dataResolucao?(i(),a("div",Z," Resolvido em: "+l(t.dataResolucao),1)):b("",!0)]),s("div",ss,[t.resolvido?(i(),a("div",es,"Completo")):t.disponivel?(i(),a("div",ts,"Disponível")):(i(),a("div",os,"Bloqueado"))])],2))),128))])])]),s("div",as,[s("div",ls,[e[8]||(e[8]=s("h2",{class:"card-title"},"🏆 Conquistas",-1)),s("div",is,[(i(!0),a(_,null,h(c.value,t=>(i(),a("div",{key:t.id,class:g(["flex items-center gap-3 p-3 rounded-lg",t.desbloqueada?"bg-warning/10":"bg-base-200"])},[s("div",ds,l(t.desbloqueada?t.icone:"🔒"),1),s("div",null,[s("h4",{class:g(["font-bold",t.desbloqueada?"text-warning":"text-base-content/50"])},l(t.titulo),3),s("p",rs,l(t.descricao),1)])],2))),128))])])]),p.value?(i(),a("div",ns,[s("div",us,[e[11]||(e[11]=s("h2",{class:"card-title justify-center"},"🎯 Próximo Passo",-1)),s("p",cs,[e[9]||(e[9]=x(" Continue sua jornada resolvendo: ")),s("strong",null,l(p.value.titulo),1)]),y(v,{to:`/namorada/enigma/${p.value.id}`,class:"btn btn-primary"},{default:k(()=>e[10]||(e[10]=[x(" Resolver Próximo Enigma 🧩 ")])),_:1,__:[10]},8,["to"])])])):w.value?(i(),a("div",vs,[s("div",gs,[e[13]||(e[13]=s("h2",{class:"card-title justify-center text-success"},"🎉 Jornada Completa!",-1)),e[14]||(e[14]=s("p",{class:"text-lg mb-4"}," Parabéns! Você resolveu todos os enigmas e coletou todas as chaves! ",-1)),y(v,{to:"/namorada/pedido",class:"btn btn-success"},{default:k(()=>e[12]||(e[12]=[x(" Ver Minha Surpresa 💖 ")])),_:1,__:[12]})])])):b("",!0)])}}});export{xs as default};

import{d as w,g as k,A as E,f as m,k as h,c as a,a as s,b as c,w as p,r as P,t as i,p as x,j as q,v as A,B as I,e as n,u as R,o as r}from"./index-BDYoTXYr.js";const D={class:"max-w-2xl mx-auto space-y-6"},J={class:"text-center"},M={class:"text-3xl font-bold text-primary mb-2"},O={class:"badge badge-primary badge-lg"},j={key:0,class:"card bg-base-100 shadow-xl"},z={class:"card-body"},B={class:"text-center mb-6"},Q={class:"text-2xl font-bold mb-4"},T={key:0,class:"space-y-4"},U={class:"form-control"},$={class:"text-center space-y-4"},K={key:0,class:"alert alert-info"},L={class:"text-sm"},F={key:0,class:"alert alert-error"},G={key:1,class:"text-center space-y-6"},H={class:"space-y-4"},W={key:1,class:"card bg-base-100 shadow-xl"},X={class:"card-body text-center"},ee=w({__name:"EnigmaView",setup(Y){const V=E();R();const u=k(()=>parseInt(V.params.id)),v=m(""),g=m(!1),l=m(""),b=m(!1),N=[{id:1,titulo:"Nosso Primeiro Encontro",pergunta:"Em que lugar nos conhecemos pela primeira vez?",resposta:"faculdade",dica:"Um lugar onde as pessoas vão para aprender e estudar..."},{id:2,titulo:"Nossa Primeira Conversa",pergunta:"Qual foi o primeiro assunto que conversamos?",resposta:"programação",dica:"Algo relacionado ao que estudamos..."},{id:3,titulo:"Nosso Primeiro Momento Especial",pergunta:"Onde foi nosso primeiro encontro romântico?",resposta:"cinema",dica:"Um lugar onde assistimos filmes juntos..."},{id:4,titulo:"O Que Mais Amo em Você",pergunta:"Qual característica sua eu mais admiro?",resposta:"sorriso",dica:"Algo que você faz que ilumina meu dia..."},{id:5,titulo:"Nossos Sonhos Juntos",pergunta:"Qual é nosso sonho em comum?",resposta:"viajar",dica:"Algo que queremos fazer juntos pelo mundo..."}],t=k(()=>N.find(o=>o.id===u.value)),f=()=>{if(!t.value)return;const o=v.value.toLowerCase().trim(),e=t.value.resposta.toLowerCase().trim();o===e?(b.value=!0,l.value="",C()):(l.value="Resposta incorreta. Tente novamente! 💕",setTimeout(()=>{l.value=""},3e3))},C=()=>{const o=JSON.parse(localStorage.getItem("chooseme-progresso")||"{}");o[`enigma_${u.value}`]=!0,localStorage.setItem("chooseme-progresso",JSON.stringify(o))},S=()=>{const o=JSON.parse(localStorage.getItem("chooseme-progresso")||"{}");b.value=o[`enigma_${u.value}`]||!1};return h(()=>{S()}),(o,e)=>{var _;const d=P("router-link");return r(),a("div",D,[s("div",J,[c(d,{to:"/namorada",class:"btn btn-ghost mb-4"},{default:p(()=>e[2]||(e[2]=[n(" ← Voltar aos Enigmas ")])),_:1,__:[2]}),s("h1",M,i((_=t.value)==null?void 0:_.titulo),1),s("div",O,"Enigma "+i(u.value),1)]),t.value?(r(),a("div",j,[s("div",z,[s("div",B,[e[3]||(e[3]=s("div",{class:"text-6xl mb-4"},"🧩",-1)),s("h2",Q,i(t.value.pergunta),1)]),b.value?(r(),a("div",G,[e[9]||(e[9]=s("div",{class:"text-6xl"},"🎉",-1)),e[10]||(e[10]=s("h3",{class:"text-2xl font-bold text-success"},"Parabéns! Você acertou!",-1)),e[11]||(e[11]=s("p",{class:"text-lg"},"Você ganhou uma chave! 🔑",-1)),s("div",H,[c(d,{to:"/namorada",class:"btn btn-primary btn-lg"},{default:p(()=>e[7]||(e[7]=[n(" Continuar Jornada ✨ ")])),_:1,__:[7]}),c(d,{to:"/namorada/progresso",class:"btn btn-outline"},{default:p(()=>e[8]||(e[8]=[n(" Ver Meu Progresso 📊 ")])),_:1,__:[8]})])])):(r(),a("div",T,[s("div",U,[e[4]||(e[4]=s("label",{class:"label"},[s("span",{class:"label-text text-lg"},"Sua resposta:")],-1)),q(s("input",{"onUpdate:modelValue":e[0]||(e[0]=y=>v.value=y),type:"text",class:"input input-bordered input-lg text-center",placeholder:"Digite sua resposta...",onKeyup:I(f,["enter"])},null,544),[[A,v.value]])]),s("div",$,[s("button",{onClick:f,class:"btn btn-primary btn-lg"}," Verificar Resposta 💖 "),g.value&&t.value.dica?(r(),a("div",K,[s("div",L,[e[5]||(e[5]=n(" 💡 ")),e[6]||(e[6]=s("strong",null,"Dica:",-1)),n(" "+i(t.value.dica),1)])])):x("",!0),!g.value&&t.value.dica?(r(),a("button",{key:1,onClick:e[1]||(e[1]=y=>g.value=!0),class:"btn btn-outline btn-sm"}," 💡 Preciso de uma dica ")):x("",!0)]),l.value?(r(),a("div",F,[s("div",null,i(l.value),1)])):x("",!0)]))])])):(r(),a("div",W,[s("div",X,[e[13]||(e[13]=s("h2",{class:"text-2xl font-bold text-error"},"Enigma não encontrado",-1)),e[14]||(e[14]=s("p",null,"Este enigma não existe ou não está disponível ainda.",-1)),c(d,{to:"/namorada",class:"btn btn-primary mt-4"},{default:p(()=>e[12]||(e[12]=[n(" Voltar ao Início ")])),_:1,__:[12]})])]))])}}});export{ee as default};

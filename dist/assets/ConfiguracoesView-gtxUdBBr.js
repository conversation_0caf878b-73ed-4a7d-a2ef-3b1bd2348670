import{d as u,i as p,k as b,c as n,a,j as l,v as t,l as r,m as f,n as v,p as g,o as d}from"./index-BDYoTXYr.js";const x={class:"space-y-6"},y={class:"card bg-base-100 shadow-xl"},k={class:"card-body"},V={class:"form-control w-full mt-4"},C={class:"form-control w-full mt-4"},N={class:"form-control mt-4"},w={class:"label cursor-pointer"},P={class:"form-control mt-4"},D={class:"label cursor-pointer"},R={class:"card bg-base-100 shadow-xl"},q={class:"card-body"},S={class:"form-control w-full mt-4"},E={class:"form-control w-full mt-4"},U={class:"card bg-base-100 shadow-xl"},I={class:"card-body"},M={class:"form-control mt-4"},j={class:"label cursor-pointer"},O={key:0,class:"form-control w-full mt-4"},h=u({__name:"ConfiguracoesView",setup(B){const e=p({mensagemPedido:"Você quer namorar comigo? 💖",dataInicioNamoro:"",permitirPular:!1,mostrarDicas:!0,tema:"valentine",corDestaque:"#ff69b4",emailEnigmaResolvido:!1,emailNotificacao:""}),m=()=>{localStorage.setItem("chooseme-config",JSON.stringify(e)),alert("Configurações salvas com sucesso!")},c=()=>{confirm("Tem certeza que deseja resetar todas as configurações?")&&Object.assign(e,{mensagemPedido:"Você quer namorar comigo? 💖",dataInicioNamoro:"",permitirPular:!1,mostrarDicas:!0,tema:"valentine",corDestaque:"#ff69b4",emailEnigmaResolvido:!1,emailNotificacao:""})};return b(()=>{const i=localStorage.getItem("chooseme-config");i&&Object.assign(e,JSON.parse(i))}),(i,o)=>(d(),n("div",x,[o[20]||(o[20]=a("h1",{class:"text-3xl font-bold text-primary"},"Configurações",-1)),a("div",y,[a("div",k,[o[12]||(o[12]=a("h2",{class:"card-title"},"Configurações do Pedido de Namoro",-1)),a("div",V,[o[8]||(o[8]=a("label",{class:"label"},[a("span",{class:"label-text"},"Mensagem do Pedido")],-1)),l(a("textarea",{"onUpdate:modelValue":o[0]||(o[0]=s=>e.mensagemPedido=s),class:"textarea textarea-bordered h-32",placeholder:"Digite a mensagem especial para o pedido..."},null,512),[[t,e.mensagemPedido]])]),a("div",C,[o[9]||(o[9]=a("label",{class:"label"},[a("span",{class:"label-text"},"Data de Início do Namoro (para cronômetro)")],-1)),l(a("input",{"onUpdate:modelValue":o[1]||(o[1]=s=>e.dataInicioNamoro=s),type:"datetime-local",class:"input input-bordered w-full"},null,512),[[t,e.dataInicioNamoro]])]),a("div",N,[a("label",w,[o[10]||(o[10]=a("span",{class:"label-text"},"Permitir pular enigmas",-1)),l(a("input",{"onUpdate:modelValue":o[2]||(o[2]=s=>e.permitirPular=s),type:"checkbox",class:"checkbox checkbox-primary"},null,512),[[r,e.permitirPular]])])]),a("div",P,[a("label",D,[o[11]||(o[11]=a("span",{class:"label-text"},"Mostrar dicas automaticamente",-1)),l(a("input",{"onUpdate:modelValue":o[3]||(o[3]=s=>e.mostrarDicas=s),type:"checkbox",class:"checkbox checkbox-primary"},null,512),[[r,e.mostrarDicas]])])])])]),a("div",R,[a("div",q,[o[16]||(o[16]=a("h2",{class:"card-title"},"Configurações Visuais",-1)),a("div",S,[o[14]||(o[14]=a("label",{class:"label"},[a("span",{class:"label-text"},"Tema Principal")],-1)),l(a("select",{"onUpdate:modelValue":o[4]||(o[4]=s=>e.tema=s),class:"select select-bordered w-full"},o[13]||(o[13]=[v('<option value="valentine">Valentine (Rosa/Vermelho)</option><option value="cupcake">Cupcake (Rosa Claro)</option><option value="pastel">Pastel (Cores Suaves)</option><option value="fantasy">Fantasy (Roxo/Rosa)</option><option value="garden">Garden (Verde/Rosa)</option>',5)]),512),[[f,e.tema]])]),a("div",E,[o[15]||(o[15]=a("label",{class:"label"},[a("span",{class:"label-text"},"Cor de Destaque")],-1)),l(a("input",{"onUpdate:modelValue":o[5]||(o[5]=s=>e.corDestaque=s),type:"color",class:"input input-bordered w-full h-12"},null,512),[[t,e.corDestaque]])])])]),a("div",U,[a("div",I,[o[19]||(o[19]=a("h2",{class:"card-title"},"Notificações",-1)),a("div",M,[a("label",j,[o[17]||(o[17]=a("span",{class:"label-text"},"Enviar email quando enigma for resolvido",-1)),l(a("input",{"onUpdate:modelValue":o[6]||(o[6]=s=>e.emailEnigmaResolvido=s),type:"checkbox",class:"checkbox checkbox-primary"},null,512),[[r,e.emailEnigmaResolvido]])])]),e.emailEnigmaResolvido?(d(),n("div",O,[o[18]||(o[18]=a("label",{class:"label"},[a("span",{class:"label-text"},"Email para notificações")],-1)),l(a("input",{"onUpdate:modelValue":o[7]||(o[7]=s=>e.emailNotificacao=s),type:"email",class:"input input-bordered w-full",placeholder:"<EMAIL>"},null,512),[[t,e.emailNotificacao]])])):g("",!0)])]),a("div",{class:"flex gap-4"},[a("button",{onClick:m,class:"btn btn-primary"}," 💾 Salvar Configurações "),a("button",{onClick:c,class:"btn btn-warning"}," 🔄 Resetar para Padrão ")])]))}});export{h as default};

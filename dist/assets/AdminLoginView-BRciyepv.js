import{d as x,f as n,c as r,a as e,n as g,q as _,p as l,j as h,v as y,s as w,t as d,e as c,b as V,w as S,r as k,u as D,o as i}from"./index-BDYoTXYr.js";import{u as A}from"./auth-daSeXAUl.js";const C={class:"min-h-screen bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center p-4"},N={class:"max-w-md w-full"},T={class:"card bg-base-100 shadow-2xl"},B={class:"card-body"},E={class:"form-control"},j={key:0,class:"alert alert-error"},q={class:"text-sm"},z={class:"form-control mt-6"},L=["disabled"],M={key:0,class:"loading loading-spinner loading-sm"},I={class:"text-center"},F=x({__name:"AdminLoginView",setup(P){const u=D(),m=A(),o=n(""),t=n(""),a=n(!1),p=async()=>{t.value="",a.value=!0,await new Promise(s=>setTimeout(s,500)),m.login(o.value)?u.push("/admin"):(t.value="Senha incorreta. Tente novamente.",o.value=""),a.value=!1};return(v,s)=>{const f=k("router-link");return i(),r("div",C,[e("div",N,[e("div",T,[e("div",B,[s[3]||(s[3]=e("div",{class:"text-center mb-6"},[e("h1",{class:"text-3xl font-bold text-primary"},"🔐 Acesso Admin"),e("p",{class:"text-gray-600 mt-2"},"Digite a senha para acessar o painel administrativo")],-1)),e("form",{onSubmit:_(p,["prevent"]),class:"space-y-4"},[e("div",E,[s[1]||(s[1]=e("label",{class:"label"},[e("span",{class:"label-text"},"Senha de Administrador")],-1)),h(e("input",{"onUpdate:modelValue":s[0]||(s[0]=b=>o.value=b),type:"password",class:w(["input input-bordered w-full",{"input-error":t.value}]),placeholder:"Digite a senha...",required:""},null,2),[[y,o.value]])]),t.value?(i(),r("div",j,[e("div",q,d(t.value),1)])):l("",!0),e("div",z,[e("button",{type:"submit",class:"btn btn-primary",disabled:a.value},[a.value?(i(),r("span",M)):l("",!0),c(" "+d(a.value?"Verificando...":"Entrar"),1)],8,L)])],32),s[4]||(s[4]=e("div",{class:"divider"},"ou",-1)),e("div",I,[V(f,{to:"/",class:"btn btn-ghost"},{default:S(()=>s[2]||(s[2]=[c(" ← Voltar ao Início ")])),_:1,__:[2]})]),s[5]||(s[5]=g('<div class="mt-6 p-4 bg-info/10 rounded-lg"><h3 class="font-bold text-sm text-info mb-2">💡 Dica para Desenvolvimento:</h3><p class="text-xs text-gray-600"> Senha padrão: <code class="bg-gray-200 px-1 rounded">admin123</code></p><p class="text-xs text-gray-500 mt-1"> (Em produção, isso seria configurado de forma mais segura) </p></div>',1))])])])])}}});export{F as default};

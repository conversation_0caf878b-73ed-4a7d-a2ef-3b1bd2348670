import{d as b,f as g,g as d,c as o,a as e,p as y,y as k,t as l,F as p,h as x,b as w,w as f,r as j,z as C,e as _,o as t}from"./index-BDYoTXYr.js";const N={class:"space-y-8"},P={class:"card bg-gradient-to-r from-pink-100 to-purple-100 shadow-xl"},V={class:"card-body text-center"},B={class:"mt-4 text-lg"},E={class:"flex justify-center gap-2 mt-4"},M={key:0,class:"w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center"},S={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},q={class:"grid gap-6"},A={class:"card-body"},R={class:"flex items-center gap-4"},z={class:"flex-shrink-0"},F={key:0,class:"w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-2xl"},$={key:1,class:"w-16 h-16 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center text-white text-2xl"},D={key:2,class:"w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-2xl"},I={class:"flex-1"},J={class:"text-xl font-bold"},L={class:"text-base-content/70"},O={class:"flex-shrink-0"},Q={key:1,class:"btn btn-success",disabled:""},T={key:2,class:"btn btn-disabled",disabled:""},G={key:0,class:"text-center"},H={class:"card bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-xl"},K={class:"card-body text-center"},X=b({__name:"InicioView",setup(U){const n=g([{id:1,titulo:"Nosso Primeiro Encontro",resolvido:!1,disponivel:!0},{id:2,titulo:"Nossa Primeira Conversa",resolvido:!1,disponivel:!1},{id:3,titulo:"Nosso Primeiro Momento Especial",resolvido:!1,disponivel:!1},{id:4,titulo:"O Que Mais Amo em Você",resolvido:!1,disponivel:!1},{id:5,titulo:"Nossos Sonhos Juntos",resolvido:!1,disponivel:!1}]),r=d(()=>n.value.length),i=d(()=>n.value.filter(u=>u.resolvido).length),c=d(()=>Math.round(i.value/r.value*100)),m=d(()=>i.value===r.value);return(u,a)=>{const v=j("router-link");return t(),o("div",N,[a[5]||(a[5]=e("div",{class:"text-center"},[e("h1",{class:"text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-4"}," Bem-vinda, Meu Amor! 💖 "),e("p",{class:"text-xl text-gray-700 max-w-2xl mx-auto"}," Preparei uma jornada especial cheia de enigmas sobre nós dois. Cada enigma resolvido te dará uma chave... e quando você tiver todas as chaves, uma surpresa incrível te espera! ✨ ")],-1)),e("div",P,[e("div",V,[a[0]||(a[0]=e("h2",{class:"card-title justify-center text-2xl mb-4"},"Seu Progresso",-1)),e("div",{class:"radial-progress text-primary text-2xl",style:k(`--value:${c.value}`),role:"progressbar"},l(c.value)+"% ",5),e("p",B,l(i.value)+" de "+l(r.value)+" chaves coletadas 🔑 ",1),e("div",E,[(t(!0),o(p,null,x(r.value,s=>(t(),o("div",{key:s,class:"w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"},[s<=i.value?(t(),o("div",M," 🔑 ")):(t(),o("div",S," 🔒 "))]))),128))])])]),e("div",q,[(t(!0),o(p,null,x(n.value,(s,h)=>(t(),o("div",{key:s.id,class:"card bg-base-100 shadow-xl"},[e("div",A,[e("div",R,[e("div",z,[s.resolvido?(t(),o("div",F," ✅ ")):s.disponivel?(t(),o("div",$,l(h+1),1)):(t(),o("div",D," 🔒 "))]),e("div",I,[e("h3",J,l(s.titulo),1),e("p",L,l(s.resolvido?"Enigma resolvido! 🎉":s.disponivel?"Pronto para ser resolvido":"Resolva o enigma anterior primeiro"),1)]),e("div",O,[s.disponivel&&!s.resolvido?(t(),C(v,{key:0,to:`/namorada/enigma/${s.id}`,class:"btn btn-primary"},{default:f(()=>a[1]||(a[1]=[_(" Resolver 🧩 ")])),_:2,__:[1]},1032,["to"])):s.resolvido?(t(),o("button",Q," Completo ✨ ")):(t(),o("button",T," Bloqueado 🔒 "))])])])]))),128))]),m.value?(t(),o("div",G,[e("div",H,[e("div",K,[a[3]||(a[3]=e("h2",{class:"card-title justify-center text-3xl mb-4"},"🎉 Parabéns! 🎉",-1)),a[4]||(a[4]=e("p",{class:"text-xl mb-6"}," Você coletou todas as chaves! Agora é hora da surpresa especial que preparei para você... ",-1)),w(v,{to:"/namorada/pedido",class:"btn btn-outline btn-white btn-lg hover:bg-white hover:text-pink-500"},{default:f(()=>a[2]||(a[2]=[_(" Ver Minha Surpresa 💖 ")])),_:1,__:[2]})])])])):y("",!0)])}}});export{X as default};

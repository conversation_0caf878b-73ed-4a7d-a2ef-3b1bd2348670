import{d as _,c as m,a as o,b as s,w as e,r as l,u as b,e as a,o as c}from"./index-BDYoTXYr.js";import{u as g}from"./auth-daSeXAUl.js";const p={class:"min-h-screen bg-base-200"},f={class:"navbar bg-primary text-primary-content"},h={class:"navbar-start"},v={class:"dropdown"},x={tabindex:"0",class:"menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52"},w={class:"navbar-center hidden lg:flex"},k={class:"menu menu-horizontal px-1"},C={class:"navbar-end"},B={class:"container mx-auto p-4"},M=_({__name:"AdminLayout",setup(V){const i=b(),r=g(),d=()=>{r.logout(),i.push("/")};return(y,t)=>{const n=l("router-link"),u=l("router-view");return c(),m("div",p,[o("div",f,[o("div",h,[o("div",v,[t[3]||(t[3]=o("div",{tabindex:"0",role:"button",class:"btn btn-ghost lg:hidden"},[o("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h8m-8 6h16"})])],-1)),o("ul",x,[o("li",null,[s(n,{to:"/admin",class:"text-base-content"},{default:e(()=>t[0]||(t[0]=[a("Dashboard")])),_:1,__:[0]})]),o("li",null,[s(n,{to:"/admin/enigmas",class:"text-base-content"},{default:e(()=>t[1]||(t[1]=[a("Enigmas")])),_:1,__:[1]})]),o("li",null,[s(n,{to:"/admin/configuracoes",class:"text-base-content"},{default:e(()=>t[2]||(t[2]=[a("Configurações")])),_:1,__:[2]})])])]),s(n,{to:"/admin",class:"btn btn-ghost text-xl"},{default:e(()=>t[4]||(t[4]=[a("💕 Choose Me - Admin")])),_:1,__:[4]})]),o("div",w,[o("ul",k,[o("li",null,[s(n,{to:"/admin",class:"btn btn-ghost"},{default:e(()=>t[5]||(t[5]=[a("Dashboard")])),_:1,__:[5]})]),o("li",null,[s(n,{to:"/admin/enigmas",class:"btn btn-ghost"},{default:e(()=>t[6]||(t[6]=[a("Enigmas")])),_:1,__:[6]})]),o("li",null,[s(n,{to:"/admin/configuracoes",class:"btn btn-ghost"},{default:e(()=>t[7]||(t[7]=[a("Configurações")])),_:1,__:[7]})])])]),o("div",C,[s(n,{to:"/",class:"btn btn-ghost"},{default:e(()=>t[8]||(t[8]=[a("Voltar ao Início")])),_:1,__:[8]}),o("button",{onClick:d,class:"btn btn-ghost"},"Sair")])]),o("div",B,[s(u)])])}}});export{M as default};

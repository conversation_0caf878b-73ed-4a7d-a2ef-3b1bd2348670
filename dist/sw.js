if(!self.define){let e,s={};const i=(i,n)=>(i=new URL(i+".js",n).href,s[i]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()})).then((()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e})));self.define=(n,o)=>{const r=e||("document"in self?document.currentScript.src:"")||location.href;if(s[r])return;let l={};const a=e=>i(e,r),u={module:{uri:r},exports:l,require:a};s[r]=Promise.all(n.map((e=>u[e]||a(e)))).then((e=>(o(...e),l)))}}define(["./workbox-74f2ef77"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"apple-touch-icon.png",revision:"a26748f40d053dcb9d5334a8ca0254ac"},{url:"assets/_plugin-vue_export-helper-DlAUqK2U.js",revision:null},{url:"assets/AdminLayout-BHMInFHR.js",revision:null},{url:"assets/AdminLoginView-BRciyepv.js",revision:null},{url:"assets/auth-daSeXAUl.js",revision:null},{url:"assets/ConfiguracoesView-gtxUdBBr.js",revision:null},{url:"assets/CronometroView-7oAqaKx1.js",revision:null},{url:"assets/CronometroView-B7K6QmtD.css",revision:null},{url:"assets/DashboardView-DN9Oa-GN.js",revision:null},{url:"assets/EnigmasView-jfL39SQ2.js",revision:null},{url:"assets/EnigmaView-CG5btywX.js",revision:null},{url:"assets/HomeView-P5wpAoCm.js",revision:null},{url:"assets/index-BDYoTXYr.js",revision:null},{url:"assets/index-BvV7RB7e.css",revision:null},{url:"assets/InicioView-BRDSy08p.js",revision:null},{url:"assets/NamoradaLayout-CXfjMvdo.js",revision:null},{url:"assets/PedidoView-DSxZNCJ8.js",revision:null},{url:"assets/PedidoView-ZhvNpYe-.css",revision:null},{url:"assets/ProgressoView-DOxCUNJY.js",revision:null},{url:"assets/workbox-window.prod.es5-B9K5rw8f.js",revision:null},{url:"favicon.ico",revision:"1ba2ae710d927f13d483fd5d1e548c9b"},{url:"icon.svg",revision:"37cf4adca6adc46944212f21fca3a55c"},{url:"index.html",revision:"6cce262e75019d0dc252a7c91508dcfc"},{url:"masked-icon.svg",revision:"2758bc1ea50bf7be4372d65412e291c0"},{url:"pwa-192x192.svg",revision:"15840a850719ff9f5a90253e8c6dc5e8"},{url:"pwa-512x512.svg",revision:"0259f5c5ef951928eb24e9f3fdba5a1e"},{url:"apple-touch-icon.png",revision:"a26748f40d053dcb9d5334a8ca0254ac"},{url:"favicon.ico",revision:"1ba2ae710d927f13d483fd5d1e548c9b"},{url:"masked-icon.svg",revision:"2758bc1ea50bf7be4372d65412e291c0"},{url:"pwa-192x192.svg",revision:"15840a850719ff9f5a90253e8c6dc5e8"},{url:"pwa-512x512.svg",revision:"0259f5c5ef951928eb24e9f3fdba5a1e"},{url:"manifest.webmanifest",revision:"9a8855f12f176a251a2116f865a2a694"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/fonts\.googleapis\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-cache",plugins:[new e.ExpirationPlugin({maxEntries:10,maxAgeSeconds:31536e3})]}),"GET")}));

import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', () => {
  const isAdmin = ref(false)
  const adminPassword = ref('admin123') // Em produção, isso seria mais seguro

  const login = (password: string) => {
    if (password === adminPassword.value) {
      isAdmin.value = true
      localStorage.setItem('chooseme-admin', 'true')
      return true
    }
    return false
  }

  const logout = () => {
    isAdmin.value = false
    localStorage.removeItem('chooseme-admin')
  }

  const checkAuth = () => {
    const adminSaved = localStorage.getItem('chooseme-admin')
    isAdmin.value = adminSaved === 'true'
  }

  return { isAdmin, login, logout, checkAuth }
})

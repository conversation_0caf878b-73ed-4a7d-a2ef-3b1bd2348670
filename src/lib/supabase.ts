import { createClient } from '@supabase/supabase-js'

// Essas variáveis devem ser configuradas no .env
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

// Verificar se o Supabase está configurado
export const isSupabaseConfigured = () => {
  return (
    supabaseUrl !== 'https://your-project.supabase.co' &&
    supabaseAnonKey !== 'your-anon-key' &&
    supabaseUrl.includes('supabase.co') &&
    supabaseAnonKey.length > 50
  )
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Tipos TypeScript para o banco
export interface Enigma {
  id: number
  titulo: string
  pergunta: string
  resposta: string
  dicas: string[]
  resolvido: boolean
  ordem: number
  created_at?: string
  updated_at?: string
}

export interface ProgressoJogo {
  id: number
  enigmas_resolvidos: number[]
  chaves_coletadas: number
  jogo_completo: boolean
  created_at?: string
  updated_at?: string
}

// Funções para gerenciar enigmas
export const enigmasService = {
  // Buscar todos os enigmas
  async getAll(): Promise<Enigma[]> {
    const { data, error } = await supabase
      .from('enigmas')
      .select('*')
      .order('ordem', { ascending: true })

    if (error) {
      console.error('Erro ao buscar enigmas:', error)
      throw error
    }

    return data || []
  },

  // Buscar enigma por ID
  async getById(id: number): Promise<Enigma | null> {
    const { data, error } = await supabase.from('enigmas').select('*').eq('id', id).single()

    if (error) {
      console.error('Erro ao buscar enigma:', error)
      return null
    }

    return data
  },

  // Criar novo enigma
  async create(enigma: Omit<Enigma, 'id' | 'created_at' | 'updated_at'>): Promise<Enigma | null> {
    const { data, error } = await supabase.from('enigmas').insert([enigma]).select().single()

    if (error) {
      console.error('Erro ao criar enigma:', error)
      throw error
    }

    return data
  },

  // Atualizar enigma
  async update(id: number, updates: Partial<Enigma>): Promise<Enigma | null> {
    const { data, error } = await supabase
      .from('enigmas')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Erro ao atualizar enigma:', error)
      throw error
    }

    return data
  },

  // Deletar enigma
  async delete(id: number): Promise<boolean> {
    const { error } = await supabase.from('enigmas').delete().eq('id', id)

    if (error) {
      console.error('Erro ao deletar enigma:', error)
      return false
    }

    return true
  },

  // Marcar enigma como resolvido
  async marcarResolvido(id: number): Promise<boolean> {
    const { error } = await supabase
      .from('enigmas')
      .update({ resolvido: true, updated_at: new Date().toISOString() })
      .eq('id', id)

    if (error) {
      console.error('Erro ao marcar enigma como resolvido:', error)
      return false
    }

    return true
  },
}

// Funções para gerenciar progresso
export const progressoService = {
  // Buscar progresso atual
  async get(): Promise<ProgressoJogo | null> {
    const { data, error } = await supabase.from('progresso_jogo').select('*').limit(1).single()

    if (error && error.code !== 'PGRST116') {
      // PGRST116 = no rows
      console.error('Erro ao buscar progresso:', error)
      return null
    }

    return data
  },

  // Criar ou atualizar progresso
  async upsert(
    progresso: Omit<ProgressoJogo, 'id' | 'created_at' | 'updated_at'>,
  ): Promise<ProgressoJogo | null> {
    const { data, error } = await supabase
      .from('progresso_jogo')
      .upsert([{ ...progresso, updated_at: new Date().toISOString() }])
      .select()
      .single()

    if (error) {
      console.error('Erro ao salvar progresso:', error)
      return null
    }

    return data
  },

  // Adicionar enigma resolvido
  async adicionarEnigmaResolvido(enigmaId: number): Promise<boolean> {
    const progressoAtual = await this.get()

    if (!progressoAtual) {
      // Criar novo progresso
      return (
        (await this.upsert({
          enigmas_resolvidos: [enigmaId],
          chaves_coletadas: 1,
          jogo_completo: false,
        })) !== null
      )
    }

    // Atualizar progresso existente
    const enigmasResolvidos = [...progressoAtual.enigmas_resolvidos, enigmaId]
    const chavesColetadas = enigmasResolvidos.length
    const jogoCompleto = chavesColetadas >= 7 // Assumindo 7 enigmas

    return (
      (await this.upsert({
        enigmas_resolvidos: enigmasResolvidos,
        chaves_coletadas: chavesColetadas,
        jogo_completo: jogoCompleto,
      })) !== null
    )
  },
}

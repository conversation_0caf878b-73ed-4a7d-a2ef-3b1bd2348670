@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the love app */
.gradient-bg {
  background: linear-gradient(135deg, #fce7f3 0%, #e9d5ff 50%, #ddd6fe 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #ec4899 0%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.love-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.romantic-gradient {
  background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);
}

.progress-circle {
  width: 128px;
  height: 128px;
  border-radius: 50%;
  background: conic-gradient(
    #ec4899 0deg,
    #ec4899 var(--progress, 0deg),
    #e5e7eb var(--progress, 0deg)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.progress-text {
  position: relative;
  z-index: 10;
  font-weight: bold;
  color: #ec4899;
}

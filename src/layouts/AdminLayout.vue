<template>
  <div class="min-h-screen bg-base-200">
    <!-- Navbar -->
    <div class="navbar bg-primary text-primary-content">
      <div class="navbar-start">
        <div class="dropdown">
          <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h8m-8 6h16"
              ></path>
            </svg>
          </div>
          <ul
            tabindex="0"
            class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52"
          >
            <li>
              <router-link to="/admin" class="text-base-content"
                >Dashboard</router-link
              >
            </li>
            <li>
              <router-link to="/admin/enigmas" class="text-base-content"
                >Enigmas</router-link
              >
            </li>
            <li>
              <router-link to="/admin/configuracoes" class="text-base-content"
                >Configurações</router-link
              >
            </li>
          </ul>
        </div>
        <router-link to="/admin" class="btn btn-ghost text-xl"
          >💕 Choose Me - Admin</router-link
        >
      </div>
      <div class="navbar-center hidden lg:flex">
        <ul class="menu menu-horizontal px-1">
          <li>
            <router-link to="/admin" class="btn btn-ghost"
              >Dashboard</router-link
            >
          </li>
          <li>
            <router-link to="/admin/enigmas" class="btn btn-ghost"
              >Enigmas</router-link
            >
          </li>
          <li>
            <router-link to="/admin/configuracoes" class="btn btn-ghost"
              >Configurações</router-link
            >
          </li>
        </ul>
      </div>
      <div class="navbar-end">
        <router-link to="/" class="btn btn-ghost">Voltar ao Início</router-link>
        <button @click="logout" class="btn btn-ghost">Sair</button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto p-4">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

const logout = () => {
  authStore.logout();
  router.push('/');
};
</script>

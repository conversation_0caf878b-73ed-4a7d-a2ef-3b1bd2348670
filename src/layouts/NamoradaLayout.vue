<template>
  <div class="min-h-screen bg-gradient-to-br from-pink-100 to-purple-100">
    <!-- Header romântico -->
    <div class="navbar bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg">
      <div class="navbar-start">
        <router-link to="/namorada" class="btn btn-ghost text-xl">
          💖 Nossa Jornada de Amor
        </router-link>
      </div>
      <div class="navbar-center hidden lg:flex">
        <ul class="menu menu-horizontal px-1">
          <li><router-link to="/namorada" class="btn btn-ghost">Início</router-link></li>
          <li><router-link to="/namorada/progresso" class="btn btn-ghost">Meu Progresso</router-link></li>
        </ul>
      </div>
      <div class="navbar-end">
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
            </svg>
          </div>
          <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li><router-link to="/namorada" class="text-base-content">Início</router-link></li>
            <li><router-link to="/namorada/progresso" class="text-base-content">Meu Progresso</router-link></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto p-4">
      <router-view />
    </div>

    <!-- Footer romântico -->
    <footer class="footer footer-center p-4 bg-base-300 text-base-content mt-8">
      <aside>
        <p>💕 Feito com muito amor para você 💕</p>
      </aside>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Layout para área da namorada
</script>

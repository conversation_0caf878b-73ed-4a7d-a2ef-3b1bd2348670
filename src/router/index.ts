import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'admin-dashboard',
          component: () => import('@/views/admin/DashboardView.vue'),
        },
        {
          path: 'enigmas',
          name: 'admin-enigmas',
          component: () => import('@/views/admin/EnigmasView.vue'),
        },
        {
          path: 'configuracoes',
          name: 'admin-config',
          component: () => import('@/views/admin/ConfiguracoesView.vue'),
        },
      ],
    },
    {
      path: '/admin-login',
      name: 'admin-login',
      component: () => import('@/views/AdminLoginView.vue'),
    },
    {
      path: '/love',
      name: 'love',
      component: () => import('@/layouts/NamoradaLayout.vue'),
      children: [
        {
          path: '',
          name: 'love-inicio',
          component: () => import('@/views/namorada/InicioView.vue'),
        },
        {
          path: 'enigma/:id',
          name: 'love-enigma',
          component: () => import('@/views/namorada/EnigmaView.vue'),
        },
        {
          path: 'progresso',
          name: 'love-progresso',
          component: () => import('@/views/namorada/ProgressoView.vue'),
        },
        {
          path: 'pedido',
          name: 'love-pedido',
          component: () => import('@/views/namorada/PedidoView.vue'),
        },
        {
          path: 'cronometro',
          name: 'love-cronometro',
          component: () => import('@/views/namorada/CronometroView.vue'),
        },
      ],
    },
  ],
});

// Guard para rotas que precisam de autenticação
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    const isAdmin = localStorage.getItem('chooseme-admin') === 'true';
    if (!isAdmin) {
      next('/admin-login');
    } else {
      next();
    }
  } else {
    next();
  }
});

export default router;

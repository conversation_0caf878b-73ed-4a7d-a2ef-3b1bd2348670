<template>
  <div v-if="showInstallPrompt" class="position-fixed bottom-0 start-0 end-0 p-3" style="z-index: 1050;">
    <div class="alert alert-info d-flex align-items-center justify-content-between shadow-lg">
      <div class="d-flex align-items-center">
        <span class="me-2">📱</span>
        <div>
          <strong>Instalar App</strong><br>
          <small>Adicione o Choose Me à sua tela inicial!</small>
        </div>
      </div>
      <div>
        <button @click="installPWA" class="btn btn-primary btn-sm me-2">
          Instalar
        </button>
        <button @click="dismissPrompt" class="btn btn-outline-secondary btn-sm">
          ✕
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const showInstallPrompt = ref(false)
let deferredPrompt: any = null

const installPWA = async () => {
  if (deferredPrompt) {
    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('PWA instalado!')
    }
    
    deferredPrompt = null
    showInstallPrompt.value = false
  }
}

const dismissPrompt = () => {
  showInstallPrompt.value = false
  localStorage.setItem('pwa-install-dismissed', 'true')
}

onMounted(() => {
  // Verificar se já foi dispensado
  const dismissed = localStorage.getItem('pwa-install-dismissed')
  if (dismissed) return

  // Escutar evento de instalação
  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault()
    deferredPrompt = e
    showInstallPrompt.value = true
  })

  // Verificar se já está instalado
  window.addEventListener('appinstalled', () => {
    console.log('PWA foi instalado!')
    showInstallPrompt.value = false
  })

  // Verificar se está rodando como PWA
  if (window.matchMedia('(display-mode: standalone)').matches) {
    console.log('Rodando como PWA!')
  }
})
</script>

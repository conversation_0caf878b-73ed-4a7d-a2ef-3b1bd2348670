<script setup lang="ts">
import { RouterView } from 'vue-router'
import { computed } from 'vue'
import PWAInstallPrompt from './components/PWAInstallPrompt.vue'
import SupabaseSetup from './components/SupabaseSetup.vue'
import { isSupabaseConfigured } from './lib/supabase'

const supabaseConfigurado = computed(() => isSupabaseConfigured())
</script>

<template>
  <div id="app">
    <!-- Mostrar setup do Supabase se não estiver configurado -->
    <SupabaseSetup v-if="!supabaseConfigurado" />

    <!-- App normal se Supabase estiver configurado -->
    <template v-else>
      <RouterView />
      <PWAInstallPrompt />
    </template>
  </div>
</template>

<style>
/* Reset global styles */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
}

/* Remove any conflicting styles */
.router-view {
  width: 100%;
  min-height: 100vh;
}
</style>

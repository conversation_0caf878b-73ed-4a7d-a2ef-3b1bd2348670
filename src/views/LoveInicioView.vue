<template>
  <div
    class="min-h-screen bg-gradient-to-br from-pink-100 via-purple-100 to-indigo-100 flex items-center justify-center p-4 w-full"
  >
    <!-- Floating Hearts Background -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
      <div
        v-for="i in 20"
        :key="i"
        class="absolute animate-float-up opacity-20"
        :style="{
          left: Math.random() * 100 + '%',
          animationDelay: Math.random() * 10 + 's',
          fontSize: Math.random() * 1 + 1 + 'rem',
        }"
      >
        💖
      </div>
    </div>

    <!-- Love Letter Card -->
    <div class="relative z-10 max-w-2xl w-full">
      <div class="card bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-pink-200">
        <div class="card-body p-8">
          <!-- Header with Heart -->
          <div class="text-center mb-8">
            <div class="text-6xl mb-4 animate-pulse">💕</div>
            <h1
              class="text-4xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-2"
            >
              Para Você, <PERSON>u <PERSON>
            </h1>
            <div class="divider divider-secondary"></div>
          </div>

          <!-- Love Letter Content -->
          <div class="prose prose-lg max-w-none text-gray-700 leading-relaxed space-y-6">
            <p class="text-xl font-medium text-center text-pink-700">💌 Uma Carta Especial 💌</p>

            <p>
              Meu amor, preparei algo muito especial para você! Este não é apenas um jogo, é uma
              jornada através das nossas memórias mais preciosas e dos momentos que tornaram nosso
              amor tão único e especial.
            </p>

            <p>
              Criei <span class="font-bold text-purple-600">7 enigmas românticos</span>
              baseados na nossa história juntos. Cada enigma que você resolver te dará uma
              <span class="font-bold text-pink-600">chave dourada</span> 🗝️
            </p>

            <div class="bg-pink-50 p-6 rounded-xl border-l-4 border-pink-400">
              <h3 class="font-bold text-pink-800 mb-3">Como Funciona:</h3>
              <ul class="space-y-2 text-pink-700">
                <li>• Você pode resolver os enigmas em qualquer ordem</li>
                <li>• Cada enigma tem dicas para te ajudar</li>
                <li>• Colete todas as 7 chaves para desbloquear a surpresa</li>
                <li>• A surpresa final é algo muito especial que preparei para nós! 💖</li>
              </ul>
            </div>

            <p class="text-center font-medium text-purple-700">
              Cada pergunta é uma lembrança nossa, cada resposta é um pedacinho do nosso amor. Está
              pronta para essa aventura romântica? ✨
            </p>
          </div>

          <!-- Action Button -->
          <div class="text-center mt-8">
            <router-link
              to="/love/jogo"
              class="btn btn-lg bg-gradient-to-r from-pink-500 to-purple-600 text-white border-none hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl px-8"
            >
              <span class="text-2xl mr-2">💝</span>
              Começar Nossa Jornada de Amor
              <span class="text-2xl ml-2">💝</span>
            </router-link>
          </div>

          <!-- Romantic Quote -->
          <div class="text-center mt-8 pt-6 border-t border-pink-200">
            <p class="italic text-gray-600">
              "O amor não é apenas olhar um para o outro, é olhar juntos na mesma direção."
            </p>
            <p class="text-sm text-gray-500 mt-1">- Antoine de Saint-Exupéry</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Página inicial com carta de amor explicando a dinâmica
</script>

<style scoped>
@keyframes float-up {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.animate-float-up {
  animation: float-up 15s infinite linear;
}
</style>

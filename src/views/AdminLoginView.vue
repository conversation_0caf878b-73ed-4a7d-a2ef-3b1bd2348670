<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center p-4">
    <div class="max-w-md w-full">
      <div class="card bg-base-100 shadow-2xl">
        <div class="card-body">
          <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-primary">🔐 Acesso Admin</h1>
            <p class="text-gray-600 mt-2">Digite a senha para acessar o painel administrativo</p>
          </div>

          <form @submit.prevent="fazerLogin" class="space-y-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Senha de Administrador</span>
              </label>
              <input 
                v-model="senha" 
                type="password" 
                class="input input-bordered w-full"
                :class="{ 'input-error': erro }"
                placeholder="Digite a senha..."
                required
              />
            </div>

            <div v-if="erro" class="alert alert-error">
              <div class="text-sm">{{ erro }}</div>
            </div>

            <div class="form-control mt-6">
              <button type="submit" class="btn btn-primary" :disabled="carregando">
                <span v-if="carregando" class="loading loading-spinner loading-sm"></span>
                {{ carregando ? 'Verificando...' : 'Entrar' }}
              </button>
            </div>
          </form>

          <div class="divider">ou</div>

          <div class="text-center">
            <router-link to="/" class="btn btn-ghost">
              ← Voltar ao Início
            </router-link>
          </div>

          <div class="mt-6 p-4 bg-info/10 rounded-lg">
            <h3 class="font-bold text-sm text-info mb-2">💡 Dica para Desenvolvimento:</h3>
            <p class="text-xs text-gray-600">
              Senha padrão: <code class="bg-gray-200 px-1 rounded">admin123</code>
            </p>
            <p class="text-xs text-gray-500 mt-1">
              (Em produção, isso seria configurado de forma mais segura)
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const senha = ref('')
const erro = ref('')
const carregando = ref(false)

const fazerLogin = async () => {
  erro.value = ''
  carregando.value = true

  // Simular um pequeno delay para parecer mais realista
  await new Promise(resolve => setTimeout(resolve, 500))

  const sucesso = authStore.login(senha.value)
  
  if (sucesso) {
    router.push('/admin')
  } else {
    erro.value = 'Senha incorreta. Tente novamente.'
    senha.value = ''
  }
  
  carregando.value = false
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 p-4 w-full">
    <div class="container mx-auto max-w-4xl">
      <!-- Back Button -->
      <div class="mb-6">
        <router-link to="/love/jogo" class="btn btn-ghost"> ← Voltar aos Enigmas </router-link>
      </div>

      <!-- Enigma Card -->
      <div class="card bg-white/90 backdrop-blur-sm shadow-2xl">
        <div class="card-body p-8">
          <!-- Header -->
          <div class="text-center mb-8">
            <div class="badge badge-primary badge-lg mb-4">Enigma {{ enigmaAtual?.id }}</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
              {{ enigmaAtual?.titulo }}
            </h1>
            <div class="divider"></div>
          </div>

          <!-- Enigma Content -->
          <div v-if="!enigmaResolvido" class="space-y-6">
            <!-- Question -->
            <div class="bg-purple-50 p-6 rounded-xl border-l-4 border-purple-400">
              <h3 class="font-bold text-purple-800 mb-3 text-lg">💭 Pergunta:</h3>
              <p class="text-purple-700 text-lg">
                {{ perguntaAtual }}
              </p>
            </div>

            <!-- Current Hint -->
            <div v-if="dicaAtual" class="bg-pink-50 p-6 rounded-xl border-l-4 border-pink-400">
              <h3 class="font-bold text-pink-800 mb-3 text-lg">💡 Dica {{ dicaIndex + 1 }}:</h3>
              <p class="text-pink-700">
                {{ dicaAtual }}
              </p>
            </div>

            <!-- Answer Input -->
            <div class="form-control">
              <label class="label">
                <span class="label-text text-lg font-medium">Sua resposta:</span>
              </label>
              <div class="flex gap-3">
                <input
                  v-model="resposta"
                  type="text"
                  placeholder="Digite sua resposta aqui..."
                  class="input input-bordered flex-1 text-lg"
                  @keyup.enter="verificarResposta"
                />
                <button @click="verificarResposta" class="btn btn-primary">Verificar ✨</button>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="erro" class="alert alert-error">
              <span>{{ erro }}</span>
            </div>

            <!-- Hint Button -->
            <div v-if="dicaIndex < dicas.length - 1" class="text-center">
              <button @click="proximaDica" class="btn btn-outline btn-secondary">
                💡 Preciso de uma dica
              </button>
            </div>
          </div>

          <!-- Success State -->
          <div v-else class="text-center space-y-6">
            <div class="text-8xl mb-4">🎉</div>
            <h2 class="text-3xl font-bold text-green-600">Parabéns!</h2>
            <p class="text-xl text-gray-700">Você resolveu o enigma e ganhou uma chave dourada!</p>
            <div class="text-6xl">🗝️</div>
            <router-link to="/love/jogo" class="btn btn-primary btn-lg">
              Continuar Jornada 💖
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const enigmaId = computed(() => parseInt(route.params.id as string))

// Mock data - será substituído por store/API
const enigmasData = {
  1: {
    id: 1,
    titulo: 'Nosso Primeiro Encontro',
    pergunta: 'Em que lugar nos conhecemos pela primeira vez?',
    resposta: 'faculdade',
    dicas: [
      'Foi em um lugar onde estudamos...',
      'Tem muitos jovens por lá...',
      'É um lugar de aprendizado...',
      'Rima com "cidade"...',
    ],
  },
  2: {
    id: 2,
    titulo: 'Nossa Primeira Conversa',
    pergunta: 'Qual foi o primeiro assunto que conversamos?',
    resposta: 'musica',
    dicas: [
      'Foi sobre algo que amamos ouvir...',
      'Tem ritmo e melodia...',
      'Pode ser rock, pop, sertanejo...',
      'Rima com "harmônica"...',
    ],
  },
  // Adicionar outros enigmas...
}

const enigmaAtual = computed(() => enigmasData[enigmaId.value as keyof typeof enigmasData])
const perguntaAtual = computed(() => enigmaAtual.value?.pergunta || '')
const dicas = computed(() => enigmaAtual.value?.dicas || [])

const resposta = ref('')
const dicaIndex = ref(0)
const erro = ref('')
const enigmaResolvido = ref(false)

const dicaAtual = computed(() => {
  if (dicaIndex.value < dicas.value.length) {
    return dicas.value[dicaIndex.value]
  }
  return null
})

const verificarResposta = () => {
  if (!resposta.value.trim()) {
    erro.value = 'Por favor, digite uma resposta!'
    return
  }

  const respostaCorreta = enigmaAtual.value?.resposta.toLowerCase()
  const respostaUsuario = resposta.value.toLowerCase().trim()

  if (respostaUsuario === respostaCorreta) {
    enigmaResolvido.value = true
    erro.value = ''
    // Aqui salvaria no store que o enigma foi resolvido
  } else {
    erro.value = 'Resposta incorreta. Tente novamente! 💭'
    resposta.value = ''
  }
}

const proximaDica = () => {
  if (dicaIndex.value < dicas.value.length - 1) {
    dicaIndex.value++
    erro.value = ''
  }
}

onMounted(() => {
  if (!enigmaAtual.value) {
    // Redirecionar se enigma não existir
    // router.push('/love/jogo')
  }
})
</script>

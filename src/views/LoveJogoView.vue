<template>
  <div class="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 p-4 w-full">
    <div class="container mx-auto max-w-6xl">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1
          class="text-5xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-4"
        >
          Nossa Jornada <PERSON>
        </h1>
        <p class="text-xl text-gray-600">
          Resolva os enigmas e colete as chaves para desbloquear a surpresa! 💖
        </p>
      </div>

      <!-- Progress Section -->
      <div class="card bg-white/80 backdrop-blur-sm shadow-xl mb-8">
        <div class="card-body text-center">
          <h2 class="text-2xl font-bold text-purple-700 mb-4">Seu Progresso</h2>

          <!-- Progress Circle -->
          <div class="flex justify-center mb-6">
            <div
              class="radial-progress text-pink-600 text-2xl"
              :style="`--value:${progressoPercentual}; --size:8rem; --thickness:8px`"
              role="progressbar"
            >
              {{ progressoPercentual }}%
            </div>
          </div>

          <p class="text-lg text-gray-700 mb-4">
            {{ chavesColetadas }} de {{ totalEnigmas }} chaves coletadas
          </p>

          <!-- Keys Display -->
          <div class="flex justify-center gap-3 flex-wrap">
            <div v-for="i in totalEnigmas" :key="i" class="relative">
              <div
                v-if="i <= chavesColetadas"
                class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform"
              >
                <span class="text-2xl">🗝️</span>
              </div>
              <div
                v-else
                class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center shadow-md"
              >
                <span class="text-2xl opacity-50">🔒</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enigmas Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div
          v-for="(enigma, index) in enigmas"
          :key="enigma.id"
          class="card bg-white/90 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          <div class="card-body">
            <!-- Enigma Number/Status -->
            <div class="flex justify-between items-start mb-4">
              <div
                class="badge badge-lg"
                :class="enigma.resolvido ? 'badge-success' : 'badge-primary'"
              >
                Enigma {{ index + 1 }}
              </div>
              <div class="text-3xl">
                {{ enigma.resolvido ? '✅' : '🧩' }}
              </div>
            </div>

            <!-- Enigma Title -->
            <h3 class="card-title text-lg text-gray-800 mb-3">
              {{ enigma.titulo }}
            </h3>

            <!-- Enigma Description -->
            <p class="text-gray-600 text-sm mb-4 flex-grow">
              {{ enigma.descricao }}
            </p>

            <!-- Status -->
            <div class="mb-4">
              <div v-if="enigma.resolvido" class="alert alert-success py-2">
                <span class="text-sm">Enigma resolvido! 🎉</span>
              </div>
              <div v-else class="alert alert-info py-2">
                <span class="text-sm">Pronto para ser resolvido ✨</span>
              </div>
            </div>

            <!-- Action Button -->
            <div class="card-actions justify-end">
              <router-link
                v-if="!enigma.resolvido"
                :to="`/love/enigma/${enigma.id}`"
                class="btn btn-primary btn-sm"
              >
                Resolver 🧩
              </router-link>
              <button v-else class="btn btn-success btn-sm" disabled>Completo ✨</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Surprise Button (appears when all enigmas are solved) -->
      <div v-if="todosEnigmasResolvidos" class="text-center">
        <div class="card bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-2xl">
          <div class="card-body text-center">
            <h2 class="text-3xl font-bold mb-4">🎉 Parabéns! 🎉</h2>
            <p class="text-xl mb-6">
              Você coletou todas as chaves! A surpresa especial te espera...
            </p>
            <router-link
              to="/love/surpresa"
              class="btn btn-outline btn-white btn-lg hover:bg-white hover:text-pink-500"
            >
              <span class="text-2xl mr-2">💖</span>
              Ver Minha Surpresa
              <span class="text-2xl ml-2">💖</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Enigma {
  id: number
  titulo: string
  descricao: string
  resolvido: boolean
}

// Mock data - será substituído por store/API
const enigmas = ref<Enigma[]>([
  {
    id: 1,
    titulo: 'Nosso Primeiro Encontro',
    descricao: 'Onde tudo começou... você se lembra daquele dia especial?',
    resolvido: false,
  },
  {
    id: 2,
    titulo: 'Nossa Primeira Conversa',
    descricao: 'As primeiras palavras que trocamos foram mágicas...',
    resolvido: false,
  },
  {
    id: 3,
    titulo: 'Nosso Primeiro Momento Especial',
    descricao: 'Aquele momento que fez meu coração acelerar...',
    resolvido: false,
  },
  {
    id: 4,
    titulo: 'O Que Mais Amo em Você',
    descricao: 'Suas qualidades que me fazem te amar cada dia mais...',
    resolvido: false,
  },
  {
    id: 5,
    titulo: 'Nossa Música',
    descricao: 'A canção que sempre me faz lembrar de você...',
    resolvido: false,
  },
  {
    id: 6,
    titulo: 'Nossos Sonhos Juntos',
    descricao: 'Os planos que fazemos para nosso futuro...',
    resolvido: false,
  },
  {
    id: 7,
    titulo: 'Nosso Lugar Especial',
    descricao: 'O lugar onde nossos corações se conectaram...',
    resolvido: false,
  },
])

const totalEnigmas = computed(() => enigmas.value.length)
const chavesColetadas = computed(() => enigmas.value.filter((e) => e.resolvido).length)
const progressoPercentual = computed(() =>
  Math.round((chavesColetadas.value / totalEnigmas.value) * 100),
)
const todosEnigmasResolvidos = computed(() => chavesColetadas.value === totalEnigmas.value)
</script>

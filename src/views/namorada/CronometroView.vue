<template>
  <div class="min-h-screen flex items-center justify-center p-4">
    <div v-if="namoroAceito" class="max-w-4xl mx-auto text-center space-y-8">
      <div class="space-y-4">
        <h1 class="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-red-600">
          💕 Nosso Tempo Juntos 💕
        </h1>
        <p class="text-xl text-gray-700">
          Desde que você disse "SIM!" ✨
        </p>
      </div>

      <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
      <div class="card bg-gradient-to-br from-pink-50 to-red-50 shadow-2xl border border-pink-200">
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <!-- Dias -->
            <div class="space-y-2">
              <div class="text-5xl font-bold text-pink-600">{{ tempoJuntos.dias }}</div>
              <div class="text-lg font-semibold text-gray-700">
                {{ tempoJuntos.dias === 1 ? 'Dia' : 'Dias' }}
              </div>
              <div class="text-sm text-gray-500">de amor</div>
            </div>

            <!-- Horas -->
            <div class="space-y-2">
              <div class="text-5xl font-bold text-red-600">{{ tempoJuntos.horas }}</div>
              <div class="text-lg font-semibold text-gray-700">
                {{ tempoJuntos.horas === 1 ? 'Hora' : 'Horas' }}
              </div>
              <div class="text-sm text-gray-500">de felicidade</div>
            </div>

            <!-- Minutos -->
            <div class="space-y-2">
              <div class="text-5xl font-bold text-purple-600">{{ tempoJuntos.minutos }}</div>
              <div class="text-lg font-semibold text-gray-700">
                {{ tempoJuntos.minutos === 1 ? 'Minuto' : 'Minutos' }}
              </div>
              <div class="text-sm text-gray-500">de carinho</div>
            </div>

            <!-- Segundos -->
            <div class="space-y-2">
              <div class="text-5xl font-bold text-indigo-600 animate-pulse">{{ tempoJuntos.segundos }}</div>
              <div class="text-lg font-semibold text-gray-700">
                {{ tempoJuntos.segundos === 1 ? 'Segundo' : 'Segundos' }}
              </div>
              <div class="text-sm text-gray-500">de amor</div>
            </div>
          </div>

          <div class="divider my-8"></div>

          <!-- Informações Adicionais -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-center">
            <div class="space-y-2">
              <div class="text-2xl font-bold text-pink-600">{{ totalHoras.toLocaleString() }}</div>
              <div class="text-sm text-gray-600">Total de horas juntos</div>
            </div>
            <div class="space-y-2">
              <div class="text-2xl font-bold text-red-600">{{ totalMinutos.toLocaleString() }}</div>
              <div class="text-sm text-gray-600">Total de minutos de amor</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Marcos do Relacionamento -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title justify-center text-2xl mb-6">🏆 Marcos do Nosso Amor</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="marco in marcos" :key="marco.id" class="card bg-gradient-to-br from-pink-100 to-purple-100 shadow-md">
              <div class="card-body text-center p-4">
                <div class="text-3xl mb-2">{{ marco.icone }}</div>
                <h3 class="font-bold text-sm">{{ marco.titulo }}</h3>
                <p class="text-xs text-gray-600 mt-1">{{ marco.descricao }}</p>
                <div v-if="marco.atingido" class="badge badge-success badge-sm mt-2">Atingido!</div>
                <div v-else class="text-xs text-gray-500 mt-2">Em {{ marco.tempoRestante }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Mensagens Românticas -->
      <div class="card bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-xl">
        <div class="card-body text-center">
          <h2 class="text-2xl font-bold mb-4">💌 Mensagem do Coração</h2>
          <p class="text-lg italic">{{ mensagemRomantica }}</p>
          <div class="mt-4">
            <button @click="proximaMensagem" class="btn btn-outline btn-white">
              Nova Mensagem 💕
            </button>
          </div>
        </div>
      </div>

      <!-- Data de Início -->
      <div class="text-center">
        <p class="text-gray-600">
          <strong>Início do nosso namoro:</strong> {{ dataInicioFormatada }}
        </p>
        <p class="text-sm text-gray-500 mt-2">
          "Cada segundo com você é um presente" ✨
        </p>
      </div>
    </div>

    <!-- Se o namoro não foi aceito ainda -->
    <div v-else class="max-w-2xl mx-auto text-center space-y-8">
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body text-center">
          <h2 class="text-3xl font-bold text-gray-600 mb-4">⏰ Cronômetro Bloqueado</h2>
          <p class="text-lg text-gray-700 mb-6">
            O cronômetro do nosso amor só será ativado quando você aceitar meu pedido de namoro! 💕
          </p>
          <router-link to="/namorada/pedido" class="btn btn-primary">
            Ir para o Pedido 💖
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface TempoJuntos {
  dias: number
  horas: number
  minutos: number
  segundos: number
}

interface Marco {
  id: number
  titulo: string
  descricao: string
  icone: string
  diasNecessarios: number
  atingido: boolean
  tempoRestante: string
}

const namoroAceito = ref(false)
const dataInicio = ref<Date | null>(null)
const tempoJuntos = ref<TempoJuntos>({ dias: 0, horas: 0, minutos: 0, segundos: 0 })
const intervalId = ref<number | null>(null)

const mensagensRomanticas = [
  "Cada momento com você é mágico ✨",
  "Você faz meu coração bater mais forte 💓",
  "Nosso amor cresce a cada segundo ⏰💕",
  "Você é minha pessoa favorita no mundo 🌍💖",
  "Juntos somos infinito ♾️💝",
  "Você é a razão do meu sorriso 😊💕",
  "Nosso amor é a coisa mais linda que existe 🌹",
  "Cada dia com você é uma nova aventura 🚀💖",
  "Você é meu para sempre 💍💕",
  "Nosso amor é eterno como as estrelas ⭐💫"
]

const mensagemRomantica = ref(mensagensRomanticas[0])

const marcos = ref<Marco[]>([
  { id: 1, titulo: "Primeiro Dia", descricao: "24 horas de amor", icone: "🌅", diasNecessarios: 1, atingido: false, tempoRestante: "" },
  { id: 2, titulo: "Uma Semana", descricao: "7 dias juntos", icone: "📅", diasNecessarios: 7, atingido: false, tempoRestante: "" },
  { id: 3, titulo: "Um Mês", descricao: "30 dias de felicidade", icone: "🗓️", diasNecessarios: 30, atingido: false, tempoRestante: "" },
  { id: 4, titulo: "100 Dias", descricao: "Centena de amor", icone: "💯", diasNecessarios: 100, atingido: false, tempoRestante: "" },
  { id: 5, titulo: "6 Meses", descricao: "Meio ano juntos", icone: "💑", diasNecessarios: 180, atingido: false, tempoRestante: "" },
  { id: 6, titulo: "Um Ano", descricao: "365 dias de amor", icone: "🎂", diasNecessarios: 365, atingido: false, tempoRestante: "" }
])

const totalHoras = computed(() => {
  if (!dataInicio.value) return 0
  const agora = new Date()
  const diff = agora.getTime() - dataInicio.value.getTime()
  return Math.floor(diff / (1000 * 60 * 60))
})

const totalMinutos = computed(() => {
  if (!dataInicio.value) return 0
  const agora = new Date()
  const diff = agora.getTime() - dataInicio.value.getTime()
  return Math.floor(diff / (1000 * 60))
})

const dataInicioFormatada = computed(() => {
  if (!dataInicio.value) return ''
  return dataInicio.value.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
})

const calcularTempo = () => {
  if (!dataInicio.value) return

  const agora = new Date()
  const diff = agora.getTime() - dataInicio.value.getTime()

  const dias = Math.floor(diff / (1000 * 60 * 60 * 24))
  const horas = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutos = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const segundos = Math.floor((diff % (1000 * 60)) / 1000)

  tempoJuntos.value = { dias, horas, minutos, segundos }
  
  // Atualizar marcos
  atualizarMarcos(dias)
}

const atualizarMarcos = (diasJuntos: number) => {
  marcos.value.forEach(marco => {
    if (diasJuntos >= marco.diasNecessarios) {
      marco.atingido = true
      marco.tempoRestante = "Atingido!"
    } else {
      marco.atingido = false
      const diasRestantes = marco.diasNecessarios - diasJuntos
      marco.tempoRestante = `${diasRestantes} ${diasRestantes === 1 ? 'dia' : 'dias'}`
    }
  })
}

const proximaMensagem = () => {
  const indiceAtual = mensagensRomanticas.indexOf(mensagemRomantica.value)
  const proximoIndice = (indiceAtual + 1) % mensagensRomanticas.length
  mensagemRomantica.value = mensagensRomanticas[proximoIndice]
}

const verificarNamoro = () => {
  const namoroSalvo = localStorage.getItem('chooseme-namoro-aceito')
  const dataInicioSalva = localStorage.getItem('chooseme-inicio-namoro')
  
  if (namoroSalvo === 'true' && dataInicioSalva) {
    namoroAceito.value = true
    dataInicio.value = new Date(dataInicioSalva)
    
    // Iniciar cronômetro
    calcularTempo()
    intervalId.value = window.setInterval(calcularTempo, 1000)
  }
}

onMounted(() => {
  verificarNamoro()
})

onUnmounted(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
})
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>

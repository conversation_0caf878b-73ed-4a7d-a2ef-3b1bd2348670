<template>
  <div class="space-y-8">
    <!-- <PERSON><PERSON> <PERSON>vinda<PERSON> -->
    <div class="text-center">
      <h1 class="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-4">
        Be<PERSON>-<PERSON><PERSON>, <PERSON>u <PERSON>! 💖
      </h1>
      <p class="text-xl text-gray-700 max-w-2xl mx-auto">
        Preparei uma jornada especial cheia de enigmas sobre nós dois. 
        Cada enigma resolvido te dará uma chave... e quando você tiver todas as chaves, 
        uma surpresa incrível te espera! ✨
      </p>
    </div>

    <!-- Progresso Geral -->
    <div class="card bg-gradient-to-r from-pink-100 to-purple-100 shadow-xl">
      <div class="card-body text-center">
        <h2 class="card-title justify-center text-2xl mb-4">Seu <PERSON></h2>
        <div class="radial-progress text-primary text-2xl" :style="`--value:${progressoPercentual}`" role="progressbar">
          {{ progressoPercentual }}%
        </div>
        <p class="mt-4 text-lg">
          {{ chavesColetadas }} de {{ totalEnigmas }} chaves coletadas 🔑
        </p>
        <div class="flex justify-center gap-2 mt-4">
          <div v-for="i in totalEnigmas" :key="i" class="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold">
            <div v-if="i <= chavesColetadas" class="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center">
              🔑
            </div>
            <div v-else class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              🔒
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Lista de Enigmas -->
    <div class="grid gap-6">
      <div v-for="(enigma, index) in enigmas" :key="enigma.id" class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex items-center gap-4">
            <div class="flex-shrink-0">
              <div v-if="enigma.resolvido" class="w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-2xl">
                ✅
              </div>
              <div v-else-if="enigma.disponivel" class="w-16 h-16 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center text-white text-2xl">
                {{ index + 1 }}
              </div>
              <div v-else class="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-2xl">
                🔒
              </div>
            </div>
            
            <div class="flex-1">
              <h3 class="text-xl font-bold">{{ enigma.titulo }}</h3>
              <p class="text-base-content/70">
                {{ enigma.resolvido ? 'Enigma resolvido! 🎉' : enigma.disponivel ? 'Pronto para ser resolvido' : 'Resolva o enigma anterior primeiro' }}
              </p>
            </div>
            
            <div class="flex-shrink-0">
              <router-link 
                v-if="enigma.disponivel && !enigma.resolvido" 
                :to="`/namorada/enigma/${enigma.id}`" 
                class="btn btn-primary"
              >
                Resolver 🧩
              </router-link>
              <button v-else-if="enigma.resolvido" class="btn btn-success" disabled>
                Completo ✨
              </button>
              <button v-else class="btn btn-disabled" disabled>
                Bloqueado 🔒
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Botão para o Pedido (só aparece quando todos os enigmas estão resolvidos) -->
    <div v-if="todosEnigmasResolvidos" class="text-center">
      <div class="card bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-xl">
        <div class="card-body text-center">
          <h2 class="card-title justify-center text-3xl mb-4">🎉 Parabéns! 🎉</h2>
          <p class="text-xl mb-6">
            Você coletou todas as chaves! Agora é hora da surpresa especial que preparei para você...
          </p>
          <router-link to="/namorada/pedido" class="btn btn-outline btn-white btn-lg hover:bg-white hover:text-pink-500">
            Ver Minha Surpresa 💖
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Enigma {
  id: number
  titulo: string
  resolvido: boolean
  disponivel: boolean
}

const enigmas = ref<Enigma[]>([
  { id: 1, titulo: 'Nosso Primeiro Encontro', resolvido: false, disponivel: true },
  { id: 2, titulo: 'Nossa Primeira Conversa', resolvido: false, disponivel: false },
  { id: 3, titulo: 'Nosso Primeiro Momento Especial', resolvido: false, disponivel: false },
  { id: 4, titulo: 'O Que Mais Amo em Você', resolvido: false, disponivel: false },
  { id: 5, titulo: 'Nossos Sonhos Juntos', resolvido: false, disponivel: false }
])

const totalEnigmas = computed(() => enigmas.value.length)
const chavesColetadas = computed(() => enigmas.value.filter(e => e.resolvido).length)
const progressoPercentual = computed(() => Math.round((chavesColetadas.value / totalEnigmas.value) * 100))
const todosEnigmasResolvidos = computed(() => chavesColetadas.value === totalEnigmas.value)
</script>

<template>
  <div class="min-h-screen flex items-center justify-center p-4">
    <div v-if="!respostaEnviada" class="max-w-2xl mx-auto text-center space-y-8">
      <!-- Animação de corações -->
      <div class="relative">
        <div class="text-8xl animate-pulse">💖</div>
        <div class="absolute -top-4 -left-4 text-4xl animate-bounce delay-100">💕</div>
        <div class="absolute -top-4 -right-4 text-4xl animate-bounce delay-200">💝</div>
        <div class="absolute -bottom-4 -left-4 text-4xl animate-bounce delay-300">💗</div>
        <div class="absolute -bottom-4 -right-4 text-4xl animate-bounce delay-400">💘</div>
      </div>

      <div class="space-y-6">
        <h1 class="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-red-600">
          <PERSON><PERSON><PERSON><PERSON>, <PERSON>u Amor!
        </h1>
        
        <p class="text-xl text-gray-700 leading-relaxed">
          Você conseguiu resolver todos os enigmas e coletou todas as chaves! 🔑✨<br>
          Cada resposta mostrou o quanto você se lembra dos nossos momentos especiais.
        </p>
      </div>

      <!-- Formulário do Pedido -->
      <div class="card bg-gradient-to-br from-pink-50 to-red-50 shadow-2xl border border-pink-200">
        <div class="card-body space-y-6">
          <h2 class="text-3xl font-bold text-center text-pink-700">
            {{ mensagemPedido }}
          </h2>
          
          <p class="text-lg text-center text-gray-700">
            Depois de toda essa jornada juntos, de todos os momentos que compartilhamos,
            e de todo o amor que sinto por você...
          </p>

          <div class="text-center space-y-4">
            <p class="text-2xl font-bold text-pink-600">
              Você aceita namorar comigo? 💕
            </p>
            
            <div class="flex justify-center gap-6 mt-8">
              <button 
                @click="responder(true)" 
                class="btn btn-success btn-lg text-white hover:scale-110 transform transition-all duration-300"
              >
                💖 SIM! 💖
              </button>
              <button 
                @click="responder(false)" 
                class="btn btn-error btn-lg text-white hover:scale-110 transform transition-all duration-300"
              >
                💔 Não...
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="text-center">
        <p class="text-gray-600 italic">
          "Você é a resposta para todas as perguntas que eu nem sabia que tinha." ✨
        </p>
      </div>
    </div>

    <!-- Resposta Positiva -->
    <div v-else-if="respostaSim" class="max-w-2xl mx-auto text-center space-y-8">
      <div class="text-center space-y-6">
        <div class="text-9xl animate-bounce">🎉</div>
        <h1 class="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-emerald-600">
          SIM!!!
        </h1>
        <p class="text-2xl text-gray-700">
          Você disse SIM! Meu coração está explodindo de felicidade! 💖
        </p>
      </div>

      <div class="card bg-gradient-to-br from-green-50 to-emerald-50 shadow-2xl border border-green-200">
        <div class="card-body text-center space-y-6">
          <h2 class="text-3xl font-bold text-green-700">
            Agora somos oficialmente namorados! 💑
          </h2>
          
          <p class="text-lg text-gray-700">
            Este é o início da nossa história de amor oficial!
            Vamos criar muitas memórias incríveis juntos! ✨
          </p>

          <div class="space-y-4">
            <p class="text-xl font-bold text-green-600">
              Data do nosso namoro: {{ dataAtual }}
            </p>
            
            <router-link to="/namorada/cronometro" class="btn btn-primary btn-lg">
              Ver Nosso Cronômetro de Amor ⏰💕
            </router-link>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-4xl">
        <div class="animate-bounce delay-100">💖</div>
        <div class="animate-bounce delay-200">💕</div>
        <div class="animate-bounce delay-300">💝</div>
        <div class="animate-bounce delay-400">💗</div>
      </div>
    </div>

    <!-- Resposta Negativa -->
    <div v-else class="max-w-2xl mx-auto text-center space-y-8">
      <div class="text-center space-y-6">
        <div class="text-9xl">💔</div>
        <h1 class="text-4xl font-bold text-red-600">
          Que pena...
        </h1>
        <p class="text-xl text-gray-700">
          Tudo bem, eu entendo. Talvez não seja o momento certo ainda.
        </p>
      </div>

      <div class="card bg-gradient-to-br from-red-50 to-pink-50 shadow-2xl border border-red-200">
        <div class="card-body text-center space-y-6">
          <p class="text-lg text-gray-700">
            Mesmo assim, quero que saiba que você é muito especial para mim.
            Vou continuar te amando e respeitando sua decisão. 💕
          </p>
          
          <div class="space-y-4">
            <button @click="voltarAoPedido" class="btn btn-primary">
              Talvez eu tenha mudado de ideia... 💭
            </button>
            <router-link to="/namorada" class="btn btn-outline">
              Voltar ao Início
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const respostaEnviada = ref(false)
const respostaSim = ref(false)
const mensagemPedido = ref('Você quer namorar comigo? 💖')

const dataAtual = computed(() => {
  return new Date().toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
})

const responder = (resposta: boolean) => {
  respostaEnviada.value = true
  respostaSim.value = resposta
  
  if (resposta) {
    // Salvar data do início do namoro
    const agora = new Date()
    localStorage.setItem('chooseme-inicio-namoro', agora.toISOString())
    localStorage.setItem('chooseme-namoro-aceito', 'true')
    
    // Adicionar confetes ou animação especial
    celebrar()
  }
}

const voltarAoPedido = () => {
  respostaEnviada.value = false
  respostaSim.value = false
}

const celebrar = () => {
  // Aqui você pode adicionar animações de confete ou outras celebrações
  console.log('🎉 CELEBRANDO! 🎉')
}

const verificarAcesso = () => {
  // Verificar se todos os enigmas foram resolvidos
  const progressoSalvo = JSON.parse(localStorage.getItem('chooseme-progresso') || '{}')
  const totalEnigmas = 5 // Número total de enigmas
  let enigmasResolvidos = 0
  
  for (let i = 1; i <= totalEnigmas; i++) {
    if (progressoSalvo[`enigma_${i}`]) {
      enigmasResolvidos++
    }
  }
  
  if (enigmasResolvidos < totalEnigmas) {
    // Se não resolveu todos os enigmas, redirecionar
    router.push('/namorada')
  }
}

const carregarConfiguracoes = () => {
  // Carregar configurações personalizadas
  const configSalva = localStorage.getItem('chooseme-config')
  if (configSalva) {
    const config = JSON.parse(configSalva)
    if (config.mensagemPedido) {
      mensagemPedido.value = config.mensagemPedido
    }
  }
}

onMounted(() => {
  verificarAcesso()
  carregarConfiguracoes()
})
</script>

<style scoped>
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style>

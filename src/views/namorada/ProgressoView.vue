<template>
  <div class="space-y-6">
    <div class="text-center">
      <h1
        class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 mb-4"
      >
        Meu Progresso 📊
      </h1>
      <p class="text-lg text-gray-700">
        Acompanhe sua jornada através dos enigmas do amor
      </p>
    </div>

    <!-- Estatísticas Gerais -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div
        class="stat bg-gradient-to-r from-pink-400 to-pink-600 text-white rounded-lg"
      >
        <div class="stat-figure">
          <div class="text-3xl">🔑</div>
        </div>
        <div class="stat-title text-white/80">Cha<PERSON></div>
        <div class="stat-value">{{ chavesColetadas }}</div>
        <div class="stat-desc text-white/80">de {{ totalEnigmas }} total</div>
      </div>

      <div
        class="stat bg-gradient-to-r from-purple-400 to-purple-600 text-white rounded-lg"
      >
        <div class="stat-figure">
          <div class="text-3xl">📈</div>
        </div>
        <div class="stat-title text-white/80">Progresso</div>
        <div class="stat-value">{{ progressoPercentual }}%</div>
        <div class="stat-desc text-white/80">Completo</div>
      </div>

      <div
        class="stat bg-gradient-to-r from-indigo-400 to-indigo-600 text-white rounded-lg"
      >
        <div class="stat-figure">
          <div class="text-3xl">⭐</div>
        </div>
        <div class="stat-title text-white/80">Status</div>
        <div class="stat-value text-sm">{{ statusAtual }}</div>
        <div class="stat-desc text-white/80">{{ mensagemStatus }}</div>
      </div>
    </div>

    <!-- Progresso Visual -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title justify-center mb-6">Jornada dos Enigmas</h2>

        <div class="flex justify-center mb-8">
          <div
            class="radial-progress text-primary text-4xl"
            :style="`--value:${progressoPercentual}; --size:12rem; --thickness: 8px`"
            role="progressbar"
          >
            {{ progressoPercentual }}%
          </div>
        </div>

        <!-- Timeline dos Enigmas -->
        <div class="space-y-4">
          <div
            v-for="(enigma, index) in enigmasComStatus"
            :key="enigma.id"
            class="flex items-center gap-4 p-4 rounded-lg"
            :class="enigma.resolvido ? 'bg-success/10' : 'bg-base-200'"
          >
            <div class="flex-shrink-0">
              <div
                v-if="enigma.resolvido"
                class="w-12 h-12 bg-success rounded-full flex items-center justify-center text-white"
              >
                ✅
              </div>
              <div
                v-else-if="enigma.disponivel"
                class="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white"
              >
                {{ index + 1 }}
              </div>
              <div
                v-else
                class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-gray-600"
              >
                🔒
              </div>
            </div>

            <div class="flex-1">
              <h3
                class="font-bold"
                :class="enigma.resolvido ? 'text-success' : ''"
              >
                {{ enigma.titulo }}
              </h3>
              <p class="text-sm text-base-content/70">
                {{
                  enigma.resolvido
                    ? 'Resolvido com sucesso! 🎉'
                    : enigma.disponivel
                    ? 'Disponível para resolver'
                    : 'Aguardando enigma anterior'
                }}
              </p>
              <div
                v-if="enigma.dataResolucao"
                class="text-xs text-base-content/50"
              >
                Resolvido em: {{ enigma.dataResolucao }}
              </div>
            </div>

            <div class="flex-shrink-0">
              <div v-if="enigma.resolvido" class="badge badge-success">
                Completo
              </div>
              <div v-else-if="enigma.disponivel" class="badge badge-primary">
                Disponível
              </div>
              <div v-else class="badge badge-ghost">Bloqueado</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conquistas -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">🏆 Conquistas</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div
            v-for="conquista in conquistas"
            :key="conquista.id"
            class="flex items-center gap-3 p-3 rounded-lg"
            :class="conquista.desbloqueada ? 'bg-warning/10' : 'bg-base-200'"
          >
            <div class="text-2xl">
              {{ conquista.desbloqueada ? conquista.icone : '🔒' }}
            </div>
            <div>
              <h4
                class="font-bold"
                :class="
                  conquista.desbloqueada
                    ? 'text-warning'
                    : 'text-base-content/50'
                "
              >
                {{ conquista.titulo }}
              </h4>
              <p class="text-sm text-base-content/70">
                {{ conquista.descricao }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Próximos Passos -->
    <div
      v-if="proximoEnigma"
      class="card bg-gradient-to-r from-pink-100 to-purple-100 shadow-xl"
    >
      <div class="card-body text-center">
        <h2 class="card-title justify-center">🎯 Próximo Passo</h2>
        <p class="text-lg mb-4">
          Continue sua jornada resolvendo:
          <strong>{{ proximoEnigma.titulo }}</strong>
        </p>
        <router-link
          :to="`/love/enigma/${proximoEnigma.id}`"
          class="btn btn-primary"
        >
          Resolver Próximo Enigma 🧩
        </router-link>
      </div>
    </div>

    <div
      v-else-if="todosEnigmasResolvidos"
      class="card bg-gradient-to-r from-green-100 to-emerald-100 shadow-xl"
    >
      <div class="card-body text-center">
        <h2 class="card-title justify-center text-success">
          🎉 Jornada Completa!
        </h2>
        <p class="text-lg mb-4">
          Parabéns! Você resolveu todos os enigmas e coletou todas as chaves!
        </p>
        <router-link to="/love/pedido" class="btn btn-success">
          Ver Minha Surpresa 💖
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

interface EnigmaStatus {
  id: number;
  titulo: string;
  resolvido: boolean;
  disponivel: boolean;
  dataResolucao?: string;
}

interface Conquista {
  id: number;
  titulo: string;
  descricao: string;
  icone: string;
  desbloqueada: boolean;
}

const enigmasComStatus = ref<EnigmaStatus[]>([
  {
    id: 1,
    titulo: 'Nosso Primeiro Encontro',
    resolvido: false,
    disponivel: true,
  },
  {
    id: 2,
    titulo: 'Nossa Primeira Conversa',
    resolvido: false,
    disponivel: false,
  },
  {
    id: 3,
    titulo: 'Nosso Primeiro Momento Especial',
    resolvido: false,
    disponivel: false,
  },
  {
    id: 4,
    titulo: 'O Que Mais Amo em Você',
    resolvido: false,
    disponivel: false,
  },
  {
    id: 5,
    titulo: 'Nossos Sonhos Juntos',
    resolvido: false,
    disponivel: false,
  },
]);

const conquistas = ref<Conquista[]>([
  {
    id: 1,
    titulo: 'Primeira Chave',
    descricao: 'Resolva seu primeiro enigma',
    icone: '🔑',
    desbloqueada: false,
  },
  {
    id: 2,
    titulo: 'Meio Caminho',
    descricao: 'Resolva 50% dos enigmas',
    icone: '🌟',
    desbloqueada: false,
  },
  {
    id: 3,
    titulo: 'Quase Lá',
    descricao: 'Resolva 80% dos enigmas',
    icone: '🚀',
    desbloqueada: false,
  },
  {
    id: 4,
    titulo: 'Mestre dos Enigmas',
    descricao: 'Resolva todos os enigmas',
    icone: '👑',
    desbloqueada: false,
  },
]);

const totalEnigmas = computed(() => enigmasComStatus.value.length);
const chavesColetadas = computed(
  () => enigmasComStatus.value.filter((e) => e.resolvido).length
);
const progressoPercentual = computed(() =>
  Math.round((chavesColetadas.value / totalEnigmas.value) * 100)
);
const todosEnigmasResolvidos = computed(
  () => chavesColetadas.value === totalEnigmas.value
);

const statusAtual = computed(() => {
  if (chavesColetadas.value === 0) return 'Iniciante';
  if (chavesColetadas.value === totalEnigmas.value) return 'Completo!';
  if (chavesColetadas.value >= totalEnigmas.value * 0.8) return 'Quase lá!';
  if (chavesColetadas.value >= totalEnigmas.value * 0.5) return 'Progredindo';
  return 'Começando';
});

const mensagemStatus = computed(() => {
  if (chavesColetadas.value === 0) return 'Comece sua jornada!';
  if (chavesColetadas.value === totalEnigmas.value) return 'Parabéns! 🎉';
  return 'Continue assim!';
});

const proximoEnigma = computed(() => {
  return enigmasComStatus.value.find((e) => !e.resolvido && e.disponivel);
});

const carregarProgresso = () => {
  const progressoSalvo = JSON.parse(
    localStorage.getItem('chooseme-progresso') || '{}'
  );

  enigmasComStatus.value.forEach((enigma, index) => {
    enigma.resolvido = progressoSalvo[`enigma_${enigma.id}`] || false;

    // Disponibilizar próximo enigma se o anterior foi resolvido
    if (index === 0) {
      enigma.disponivel = true;
    } else {
      enigma.disponivel = enigmasComStatus.value[index - 1].resolvido;
    }

    // Adicionar data de resolução se disponível
    if (enigma.resolvido && progressoSalvo[`enigma_${enigma.id}_data`]) {
      enigma.dataResolucao = progressoSalvo[`enigma_${enigma.id}_data`];
    }
  });

  // Atualizar conquistas
  atualizarConquistas();
};

const atualizarConquistas = () => {
  const chavesColetadasNum = chavesColetadas.value;
  const totalNum = totalEnigmas.value;

  conquistas.value[0].desbloqueada = chavesColetadasNum >= 1;
  conquistas.value[1].desbloqueada =
    chavesColetadasNum >= Math.ceil(totalNum * 0.5);
  conquistas.value[2].desbloqueada =
    chavesColetadasNum >= Math.ceil(totalNum * 0.8);
  conquistas.value[3].desbloqueada = chavesColetadasNum === totalNum;
};

onMounted(() => {
  carregarProgresso();
});
</script>

<template>
  <div class="max-w-2xl mx-auto space-y-6">
    <div class="text-center">
      <router-link to="/namorada" class="btn btn-ghost mb-4">
        ← Voltar aos Enigmas
      </router-link>
      <h1 class="text-3xl font-bold text-primary mb-2">{{ enigmaAtual?.titulo }}</h1>
      <div class="badge badge-primary badge-lg">Enigma {{ enigmaId }}</div>
    </div>

    <div v-if="enigmaAtual" class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <div class="text-center mb-6">
          <div class="text-6xl mb-4">🧩</div>
          <h2 class="text-2xl font-bold mb-4">{{ enigmaAtual.pergunta }}</h2>
        </div>

        <div v-if="!enigmaResolvido" class="space-y-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text text-lg">Sua resposta:</span>
            </label>
            <input 
              v-model="respostaUsuario" 
              type="text" 
              class="input input-bordered input-lg text-center"
              placeholder="Digite sua resposta..."
              @keyup.enter="verificarResposta"
            />
          </div>

          <div class="text-center space-y-4">
            <button @click="verificarResposta" class="btn btn-primary btn-lg">
              Verificar Resposta 💖
            </button>

            <div v-if="mostrarDica && enigmaAtual.dica" class="alert alert-info">
              <div class="text-sm">
                💡 <strong>Dica:</strong> {{ enigmaAtual.dica }}
              </div>
            </div>

            <button v-if="!mostrarDica && enigmaAtual.dica" @click="mostrarDica = true" class="btn btn-outline btn-sm">
              💡 Preciso de uma dica
            </button>
          </div>

          <div v-if="mensagemErro" class="alert alert-error">
            <div>{{ mensagemErro }}</div>
          </div>
        </div>

        <div v-else class="text-center space-y-6">
          <div class="text-6xl">🎉</div>
          <h3 class="text-2xl font-bold text-success">Parabéns! Você acertou!</h3>
          <p class="text-lg">Você ganhou uma chave! 🔑</p>
          
          <div class="space-y-4">
            <router-link to="/namorada" class="btn btn-primary btn-lg">
              Continuar Jornada ✨
            </router-link>
            <router-link to="/namorada/progresso" class="btn btn-outline">
              Ver Meu Progresso 📊
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="card bg-base-100 shadow-xl">
      <div class="card-body text-center">
        <h2 class="text-2xl font-bold text-error">Enigma não encontrado</h2>
        <p>Este enigma não existe ou não está disponível ainda.</p>
        <router-link to="/namorada" class="btn btn-primary mt-4">
          Voltar ao Início
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

interface EnigmaCompleto {
  id: number
  titulo: string
  pergunta: string
  resposta: string
  dica?: string
}

const route = useRoute()
const router = useRouter()

const enigmaId = computed(() => parseInt(route.params.id as string))
const respostaUsuario = ref('')
const mostrarDica = ref(false)
const mensagemErro = ref('')
const enigmaResolvido = ref(false)

// Dados dos enigmas (em uma aplicação real, isso viria de uma API ou store)
const enigmas: EnigmaCompleto[] = [
  {
    id: 1,
    titulo: 'Nosso Primeiro Encontro',
    pergunta: 'Em que lugar nos conhecemos pela primeira vez?',
    resposta: 'faculdade',
    dica: 'Um lugar onde as pessoas vão para aprender e estudar...'
  },
  {
    id: 2,
    titulo: 'Nossa Primeira Conversa',
    pergunta: 'Qual foi o primeiro assunto que conversamos?',
    resposta: 'programação',
    dica: 'Algo relacionado ao que estudamos...'
  },
  {
    id: 3,
    titulo: 'Nosso Primeiro Momento Especial',
    pergunta: 'Onde foi nosso primeiro encontro romântico?',
    resposta: 'cinema',
    dica: 'Um lugar onde assistimos filmes juntos...'
  },
  {
    id: 4,
    titulo: 'O Que Mais Amo em Você',
    pergunta: 'Qual característica sua eu mais admiro?',
    resposta: 'sorriso',
    dica: 'Algo que você faz que ilumina meu dia...'
  },
  {
    id: 5,
    titulo: 'Nossos Sonhos Juntos',
    pergunta: 'Qual é nosso sonho em comum?',
    resposta: 'viajar',
    dica: 'Algo que queremos fazer juntos pelo mundo...'
  }
]

const enigmaAtual = computed(() => {
  return enigmas.find(e => e.id === enigmaId.value)
})

const verificarResposta = () => {
  if (!enigmaAtual.value) return

  const respostaNormalizada = respostaUsuario.value.toLowerCase().trim()
  const respostaCorreta = enigmaAtual.value.resposta.toLowerCase().trim()

  if (respostaNormalizada === respostaCorreta) {
    enigmaResolvido.value = true
    mensagemErro.value = ''
    // Aqui você salvaria o progresso no localStorage ou backend
    salvarProgresso()
  } else {
    mensagemErro.value = 'Resposta incorreta. Tente novamente! 💕'
    // Limpar a mensagem de erro após 3 segundos
    setTimeout(() => {
      mensagemErro.value = ''
    }, 3000)
  }
}

const salvarProgresso = () => {
  // Salvar no localStorage que este enigma foi resolvido
  const progressoSalvo = JSON.parse(localStorage.getItem('chooseme-progresso') || '{}')
  progressoSalvo[`enigma_${enigmaId.value}`] = true
  localStorage.setItem('chooseme-progresso', JSON.stringify(progressoSalvo))
}

const carregarProgresso = () => {
  // Verificar se este enigma já foi resolvido
  const progressoSalvo = JSON.parse(localStorage.getItem('chooseme-progresso') || '{}')
  enigmaResolvido.value = progressoSalvo[`enigma_${enigmaId.value}`] || false
}

onMounted(() => {
  carregarProgresso()
})
</script>

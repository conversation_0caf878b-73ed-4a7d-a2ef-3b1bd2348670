<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 w-full">
    <div class="container mx-auto max-w-7xl">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-blue-800 mb-2">🔧 Painel Administrativo</h1>
        <p class="text-lg text-gray-600">Gerencie os enigmas e configurações do jogo de amor</p>
        <div class="divider"></div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="stat bg-white shadow-lg rounded-xl">
          <div class="stat-figure text-primary">
            <div class="text-3xl">🧩</div>
          </div>
          <div class="stat-title">Total de Enigmas</div>
          <div class="stat-value text-primary">{{ enigmas.length }}</div>
        </div>

        <div class="stat bg-white shadow-lg rounded-xl">
          <div class="stat-figure text-success">
            <div class="text-3xl">✅</div>
          </div>
          <div class="stat-title">Enigmas Resolvidos</div>
          <div class="stat-value text-success">{{ enigmasResolvidos }}</div>
        </div>

        <div class="stat bg-white shadow-lg rounded-xl">
          <div class="stat-figure text-warning">
            <div class="text-3xl">🗝️</div>
          </div>
          <div class="stat-title">Chaves Coletadas</div>
          <div class="stat-value text-warning">{{ enigmasResolvidos }}/{{ enigmas.length }}</div>
        </div>
      </div>

      <!-- Add New Enigma Button -->
      <div class="text-center mb-6">
        <button @click="abrirModalNovoEnigma" class="btn btn-primary btn-lg">
          <span class="text-xl mr-2">➕</span>
          Adicionar Novo Enigma
        </button>
      </div>

      <!-- Enigmas List -->
      <div class="grid gap-6">
        <div v-for="(enigma, index) in enigmas" :key="enigma.id" class="card bg-white shadow-lg">
          <div class="card-body">
            <div class="flex justify-between items-start mb-4">
              <div class="flex items-center gap-3">
                <div class="badge badge-lg badge-primary">{{ index + 1 }}</div>
                <h3 class="text-xl font-bold">{{ enigma.titulo }}</h3>
                <div v-if="enigma.resolvido" class="badge badge-success">Resolvido</div>
              </div>
              <div class="dropdown dropdown-end">
                <div tabindex="0" role="button" class="btn btn-ghost btn-sm">⋮</div>
                <ul
                  tabindex="0"
                  class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52"
                >
                  <li><a @click="editarEnigma(enigma)">✏️ Editar</a></li>
                  <li><a @click="excluirEnigma(enigma.id)" class="text-error">🗑️ Excluir</a></li>
                </ul>
              </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
              <div>
                <h4 class="font-semibold text-gray-700 mb-2">Pergunta:</h4>
                <p class="text-gray-600 bg-gray-50 p-3 rounded">{{ enigma.pergunta }}</p>
              </div>
              <div>
                <h4 class="font-semibold text-gray-700 mb-2">Resposta:</h4>
                <p class="text-gray-600 bg-gray-50 p-3 rounded font-mono">{{ enigma.resposta }}</p>
              </div>
            </div>

            <div class="mt-4">
              <h4 class="font-semibold text-gray-700 mb-2">Dicas ({{ enigma.dicas.length }}):</h4>
              <div class="space-y-2">
                <div
                  v-for="(dica, dicaIndex) in enigma.dicas"
                  :key="dicaIndex"
                  class="bg-yellow-50 p-2 rounded border-l-4 border-yellow-400"
                >
                  <span class="text-sm font-medium text-yellow-800">Dica {{ dicaIndex + 1 }}:</span>
                  <span class="text-yellow-700 ml-2">{{ dica }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal for New/Edit Enigma -->
      <div
        v-if="mostrarModal"
        class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      >
        <div class="card bg-white max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div class="card-body">
            <h3 class="text-2xl font-bold mb-6">
              {{ enigmaEditando ? 'Editar Enigma' : 'Novo Enigma' }}
            </h3>

            <div class="space-y-4">
              <!-- Título -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Título do Enigma</span>
                </label>
                <input
                  v-model="formulario.titulo"
                  type="text"
                  placeholder="Ex: Nosso Primeiro Encontro"
                  class="input input-bordered"
                />
              </div>

              <!-- Pergunta -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Pergunta</span>
                </label>
                <textarea
                  v-model="formulario.pergunta"
                  placeholder="Digite a pergunta do enigma..."
                  class="textarea textarea-bordered h-24"
                ></textarea>
              </div>

              <!-- Resposta -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Resposta (em minúsculas)</span>
                </label>
                <input
                  v-model="formulario.resposta"
                  type="text"
                  placeholder="resposta correta"
                  class="input input-bordered"
                />
              </div>

              <!-- Dicas -->
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-medium">Dicas (uma por linha)</span>
                </label>
                <textarea
                  v-model="dicasTexto"
                  placeholder="Digite cada dica em uma linha separada..."
                  class="textarea textarea-bordered h-32"
                ></textarea>
                <label class="label">
                  <span class="label-text-alt"
                    >Cada linha será uma dica. Ordene da mais sutil para a mais óbvia.</span
                  >
                </label>
              </div>
            </div>

            <div class="card-actions justify-end mt-6">
              <button @click="fecharModal" class="btn btn-ghost">Cancelar</button>
              <button @click="salvarEnigma" class="btn btn-primary">
                {{ enigmaEditando ? 'Salvar Alterações' : 'Criar Enigma' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Enigma {
  id: number
  titulo: string
  pergunta: string
  resposta: string
  dicas: string[]
  resolvido: boolean
}

// Mock data - será substituído por store/API
const enigmas = ref<Enigma[]>([
  {
    id: 1,
    titulo: 'Nosso Primeiro Encontro',
    pergunta: 'Em que lugar nos conhecemos pela primeira vez?',
    resposta: 'faculdade',
    dicas: [
      'Foi em um lugar onde estudamos...',
      'Tem muitos jovens por lá...',
      'É um lugar de aprendizado...',
      'Rima com "cidade"...',
    ],
    resolvido: false,
  },
  {
    id: 2,
    titulo: 'Nossa Primeira Conversa',
    pergunta: 'Qual foi o primeiro assunto que conversamos?',
    resposta: 'musica',
    dicas: [
      'Foi sobre algo que amamos ouvir...',
      'Tem ritmo e melodia...',
      'Pode ser rock, pop, sertanejo...',
      'Rima com "harmônica"...',
    ],
    resolvido: false,
  },
])

const enigmasResolvidos = computed(() => enigmas.value.filter((e) => e.resolvido).length)

// Modal state
const mostrarModal = ref(false)
const enigmaEditando = ref<Enigma | null>(null)
const formulario = ref({
  titulo: '',
  pergunta: '',
  resposta: '',
})
const dicasTexto = ref('')

const abrirModalNovoEnigma = () => {
  enigmaEditando.value = null
  formulario.value = { titulo: '', pergunta: '', resposta: '' }
  dicasTexto.value = ''
  mostrarModal.value = true
}

const editarEnigma = (enigma: Enigma) => {
  enigmaEditando.value = enigma
  formulario.value = {
    titulo: enigma.titulo,
    pergunta: enigma.pergunta,
    resposta: enigma.resposta,
  }
  dicasTexto.value = enigma.dicas.join('\n')
  mostrarModal.value = true
}

const salvarEnigma = () => {
  const dicas = dicasTexto.value.split('\n').filter((d) => d.trim())

  if (enigmaEditando.value) {
    // Editar enigma existente
    const index = enigmas.value.findIndex((e) => e.id === enigmaEditando.value!.id)
    if (index !== -1) {
      enigmas.value[index] = {
        ...enigmas.value[index],
        titulo: formulario.value.titulo,
        pergunta: formulario.value.pergunta,
        resposta: formulario.value.resposta.toLowerCase(),
        dicas,
      }
    }
  } else {
    // Criar novo enigma
    const novoId = Math.max(...enigmas.value.map((e) => e.id)) + 1
    enigmas.value.push({
      id: novoId,
      titulo: formulario.value.titulo,
      pergunta: formulario.value.pergunta,
      resposta: formulario.value.resposta.toLowerCase(),
      dicas,
      resolvido: false,
    })
  }

  fecharModal()
}

const excluirEnigma = (id: number) => {
  if (confirm('Tem certeza que deseja excluir este enigma?')) {
    enigmas.value = enigmas.value.filter((e) => e.id !== id)
  }
}

const fecharModal = () => {
  mostrarModal.value = false
  enigmaEditando.value = null
}
</script>

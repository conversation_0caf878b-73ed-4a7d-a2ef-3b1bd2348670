<template>
  <div class="min-h-screen bg-gray-100 p-4 w-full">
    <div class="container mx-auto max-w-7xl">
      <!-- Header -->
      <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-gray-900 mb-2">🔧 Painel Administrativo</h1>
          <p class="text-lg text-gray-700">Gerencie os enigmas e configurações do jogo de amor</p>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 mb-1">Total de Enigmas</p>
              <p class="text-3xl font-bold text-blue-600">{{ enigmas.length }}</p>
            </div>
            <div class="text-4xl">🧩</div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 mb-1">Enigmas Resolvidos</p>
              <p class="text-3xl font-bold text-green-600">{{ enigmasResolvidos }}</p>
            </div>
            <div class="text-4xl">✅</div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600 mb-1">Chaves Coletadas</p>
              <p class="text-3xl font-bold text-orange-600">
                {{ enigmasResolvidos }}/{{ enigmas.length }}
              </p>
            </div>
            <div class="text-4xl">🗝️</div>
          </div>
        </div>
      </div>

      <!-- Add New Enigma Button -->
      <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-200">
        <div class="text-center">
          <h2 class="text-xl font-bold text-gray-900 mb-4">Gerenciar Enigmas</h2>
          <button
            @click="abrirModalNovoEnigma"
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg"
          >
            <span class="text-xl mr-2">➕</span>
            Adicionar Novo Enigma
          </button>
        </div>
      </div>

      <!-- Enigmas List -->
      <div class="grid gap-6">
        <div
          v-for="(enigma, index) in enigmas"
          :key="enigma.id"
          class="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
        >
          <div class="flex justify-between items-start mb-6">
            <div class="flex items-center gap-4">
              <div class="bg-blue-100 text-blue-800 font-bold px-3 py-1 rounded-lg text-sm">
                Enigma {{ index + 1 }}
              </div>
              <h3 class="text-xl font-bold text-gray-900">{{ enigma.titulo }}</h3>
              <div
                v-if="enigma.resolvido"
                class="bg-green-100 text-green-800 font-medium px-3 py-1 rounded-lg text-sm"
              >
                ✅ Resolvido
              </div>
            </div>
            <div class="dropdown dropdown-end">
              <div
                tabindex="0"
                role="button"
                class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-lg transition-colors"
              >
                ⋮
              </div>
              <ul
                tabindex="0"
                class="dropdown-content z-[1] menu p-2 shadow-lg bg-white rounded-lg w-52 border border-gray-200"
              >
                <li>
                  <a @click="editarEnigma(enigma)" class="text-gray-700 hover:bg-blue-50"
                    >✏️ Editar</a
                  >
                </li>
                <li>
                  <a @click="excluirEnigma(enigma.id)" class="text-red-600 hover:bg-red-50"
                    >🗑️ Excluir</a
                  >
                </li>
              </ul>
            </div>
          </div>

          <div class="grid md:grid-cols-2 gap-6">
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <span class="text-purple-600 mr-2">❓</span>
                Pergunta:
              </h4>
              <p class="text-gray-800 leading-relaxed">{{ enigma.pergunta }}</p>
            </div>
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <span class="text-green-600 mr-2">✅</span>
                Resposta:
              </h4>
              <p class="text-gray-800 font-mono bg-white px-3 py-2 rounded border">
                {{ enigma.resposta }}
              </p>
            </div>
          </div>

          <div class="mt-6">
            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-yellow-600 mr-2">💡</span>
              Dicas ({{ enigma.dicas.length }}):
            </h4>
            <div class="space-y-3">
              <div
                v-for="(dica, dicaIndex) in enigma.dicas"
                :key="dicaIndex"
                class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg"
              >
                <span class="text-sm font-semibold text-yellow-800 bg-yellow-200 px-2 py-1 rounded">
                  Dica {{ dicaIndex + 1 }}
                </span>
                <p class="text-yellow-900 mt-2">{{ dica }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal for New/Edit Enigma -->
    <div
      v-if="mostrarModal"
      class="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4"
    >
      <div
        class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-200"
      >
        <div class="p-8">
          <h3 class="text-3xl font-bold text-gray-900 mb-8 text-center">
            {{ enigmaEditando ? '✏️ Editar Enigma' : '➕ Novo Enigma' }}
          </h3>

          <div class="space-y-4">
            <!-- Título -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Título do Enigma</span>
              </label>
              <input
                v-model="formulario.titulo"
                type="text"
                placeholder="Ex: Nosso Primeiro Encontro"
                class="input input-bordered"
              />
            </div>

            <!-- Pergunta -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Pergunta</span>
              </label>
              <textarea
                v-model="formulario.pergunta"
                placeholder="Digite a pergunta do enigma..."
                class="textarea textarea-bordered h-24"
              ></textarea>
            </div>

            <!-- Resposta -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Resposta (em minúsculas)</span>
              </label>
              <input
                v-model="formulario.resposta"
                type="text"
                placeholder="resposta correta"
                class="input input-bordered"
              />
            </div>

            <!-- Dicas -->
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Dicas (uma por linha)</span>
              </label>
              <textarea
                v-model="dicasTexto"
                placeholder="Digite cada dica em uma linha separada..."
                class="textarea textarea-bordered h-32"
              ></textarea>
              <label class="label">
                <span class="label-text-alt"
                  >Cada linha será uma dica. Ordene da mais sutil para a mais óbvia.</span
                >
              </label>
            </div>
          </div>

          <div class="flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200">
            <button
              @click="fecharModal"
              class="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"
            >
              Cancelar
            </button>
            <button
              @click="salvarEnigma"
              class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-md hover:shadow-lg"
            >
              {{ enigmaEditando ? 'Salvar Alterações' : 'Criar Enigma' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Enigma {
  id: number
  titulo: string
  pergunta: string
  resposta: string
  dicas: string[]
  resolvido: boolean
}

// Mock data - será substituído por store/API
const enigmas = ref<Enigma[]>([
  {
    id: 1,
    titulo: 'Nosso Primeiro Encontro',
    pergunta: 'Em que lugar nos conhecemos pela primeira vez?',
    resposta: 'faculdade',
    dicas: [
      'Foi em um lugar onde estudamos...',
      'Tem muitos jovens por lá...',
      'É um lugar de aprendizado...',
      'Rima com "cidade"...',
    ],
    resolvido: false,
  },
  {
    id: 2,
    titulo: 'Nossa Primeira Conversa',
    pergunta: 'Qual foi o primeiro assunto que conversamos?',
    resposta: 'musica',
    dicas: [
      'Foi sobre algo que amamos ouvir...',
      'Tem ritmo e melodia...',
      'Pode ser rock, pop, sertanejo...',
      'Rima com "harmônica"...',
    ],
    resolvido: false,
  },
])

const enigmasResolvidos = computed(() => enigmas.value.filter((e) => e.resolvido).length)

// Modal state
const mostrarModal = ref(false)
const enigmaEditando = ref<Enigma | null>(null)
const formulario = ref({
  titulo: '',
  pergunta: '',
  resposta: '',
})
const dicasTexto = ref('')

const abrirModalNovoEnigma = () => {
  enigmaEditando.value = null
  formulario.value = { titulo: '', pergunta: '', resposta: '' }
  dicasTexto.value = ''
  mostrarModal.value = true
}

const editarEnigma = (enigma: Enigma) => {
  enigmaEditando.value = enigma
  formulario.value = {
    titulo: enigma.titulo,
    pergunta: enigma.pergunta,
    resposta: enigma.resposta,
  }
  dicasTexto.value = enigma.dicas.join('\n')
  mostrarModal.value = true
}

const salvarEnigma = () => {
  const dicas = dicasTexto.value.split('\n').filter((d) => d.trim())

  if (enigmaEditando.value) {
    // Editar enigma existente
    const index = enigmas.value.findIndex((e) => e.id === enigmaEditando.value!.id)
    if (index !== -1) {
      enigmas.value[index] = {
        ...enigmas.value[index],
        titulo: formulario.value.titulo,
        pergunta: formulario.value.pergunta,
        resposta: formulario.value.resposta.toLowerCase(),
        dicas,
      }
    }
  } else {
    // Criar novo enigma
    const novoId = Math.max(...enigmas.value.map((e) => e.id)) + 1
    enigmas.value.push({
      id: novoId,
      titulo: formulario.value.titulo,
      pergunta: formulario.value.pergunta,
      resposta: formulario.value.resposta.toLowerCase(),
      dicas,
      resolvido: false,
    })
  }

  fecharModal()
}

const excluirEnigma = (id: number) => {
  if (confirm('Tem certeza que deseja excluir este enigma?')) {
    enigmas.value = enigmas.value.filter((e) => e.id !== id)
  }
}

const fecharModal = () => {
  mostrarModal.value = false
  enigmaEditando.value = null
}
</script>

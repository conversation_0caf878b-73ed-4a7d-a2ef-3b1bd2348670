<template>
  <div
    class="min-h-screen bg-gradient-to-br from-pink-200 via-purple-200 to-indigo-200 flex items-center justify-center p-4 relative overflow-hidden w-full"
  >
    <!-- Floating Hearts Animation -->
    <div class="fixed inset-0 pointer-events-none">
      <div
        v-for="i in 50"
        :key="i"
        class="absolute animate-float-hearts"
        :style="{
          left: Math.random() * 100 + '%',
          animationDelay: Math.random() * 5 + 's',
          fontSize: Math.random() * 2 + 1 + 'rem',
        }"
      >
        {{ hearts[Math.floor(Math.random() * hearts.length)] }}
      </div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 max-w-4xl w-full text-center">
      <!-- Title -->
      <div class="mb-12">
        <h1
          class="text-6xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-4"
        >
          Minha Surpresa Para Você
        </h1>
        <div class="text-4xl mb-6">💖✨💖</div>
      </div>

      <!-- Love Letter Card -->
      <div class="card bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-pink-300 mb-8">
        <div class="card-body p-12">
          <div class="prose prose-xl max-w-none text-gray-700 space-y-6">
            <p class="text-2xl font-bold text-pink-700 mb-8">Meu Amor, Você Conseguiu! 🎉</p>

            <p class="text-lg leading-relaxed">
              Cada enigma que você resolveu foi uma jornada através das nossas memórias mais
              preciosas. Cada resposta mostrou o quanto você se importa com os nossos momentos
              juntos. E agora, chegou a hora da minha pergunta mais importante...
            </p>

            <div
              class="bg-gradient-to-r from-pink-100 to-purple-100 p-8 rounded-2xl border-2 border-pink-300 my-8"
            >
              <p class="text-3xl font-bold text-purple-800 mb-6">💍 A Pergunta Que Muda Tudo 💍</p>
              <p class="text-2xl text-gray-800 leading-relaxed">
                Depois de todos esses momentos incríveis juntos, de todas as memórias que criamos e
                de todo o amor que compartilhamos...
              </p>
            </div>

            <div class="text-center my-12">
              <div class="text-8xl mb-6 animate-pulse">💕</div>
              <h2
                class="text-4xl font-bold bg-gradient-to-r from-red-500 to-pink-600 bg-clip-text text-transparent mb-8"
              >
                Você Quer Namorar Comigo?
              </h2>
              <div class="text-6xl mb-8">💍💖💍</div>
            </div>

            <p class="text-lg text-gray-700 leading-relaxed">
              Você é a pessoa mais especial que já conheci. Cada dia ao seu lado é uma nova
              aventura, cada sorriso seu ilumina meu mundo, e cada momento juntos é um presente que
              guardo no coração.
            </p>

            <p class="text-lg text-gray-700 leading-relaxed">
              Quero continuar criando memórias incríveis com você, quero ser seu companheiro em
              todas as aventuras da vida, e quero que saiba que você é a pessoa com quem quero
              compartilhar meus sonhos e construir nosso futuro juntos.
            </p>
          </div>

          <!-- Response Buttons -->
          <div class="flex flex-col sm:flex-row gap-6 justify-center mt-12">
            <button
              @click="responderSim"
              class="btn btn-lg bg-gradient-to-r from-green-500 to-emerald-600 text-white border-none hover:scale-105 transform transition-all duration-300 shadow-lg px-12"
            >
              <span class="text-3xl mr-3">💚</span>
              SIM! EU ACEITO!
              <span class="text-3xl ml-3">💚</span>
            </button>

            <button
              @click="responderNao"
              class="btn btn-lg btn-outline btn-error hover:scale-105 transform transition-all duration-300 px-12"
            >
              <span class="text-2xl mr-2">💔</span>
              Não...
            </button>
          </div>
        </div>
      </div>

      <!-- Response Modal -->
      <div
        v-if="mostrarResposta"
        class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      >
        <div class="card bg-white max-w-2xl w-full">
          <div class="card-body text-center p-8">
            <div v-if="respostaSim" class="space-y-6">
              <div class="text-8xl">🎉💕🎉</div>
              <h3 class="text-4xl font-bold text-green-600">SIIIIM! 💖</h3>
              <p class="text-xl text-gray-700">
                Você me fez a pessoa mais feliz do mundo! Agora somos oficialmente namorados! Mal
                posso esperar para viver todas as aventuras que nos aguardam juntos! 💕✨
              </p>
              <div class="text-6xl">👫💖👫</div>
            </div>

            <div v-else class="space-y-6">
              <div class="text-6xl">😢</div>
              <h3 class="text-2xl font-bold text-red-600">Que pena...</h3>
              <p class="text-lg text-gray-700">
                Tudo bem, respeito sua decisão. Mas saiba que meus sentimentos por você são
                verdadeiros e estarei aqui se mudar de ideia. Você sempre será especial para mim! 💙
              </p>
            </div>

            <div class="flex gap-4 justify-center mt-6">
              <button @click="fecharModal" class="btn btn-primary">Fechar</button>
              <router-link to="/love/jogo" class="btn btn-outline"> Voltar ao Jogo </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const hearts = ['💖', '💕', '💝', '💗', '💘', '💞', '💓', '💟', '❤️', '🧡', '💛', '💚', '💙', '💜']

const mostrarResposta = ref(false)
const respostaSim = ref(false)

const responderSim = () => {
  respostaSim.value = true
  mostrarResposta.value = true
}

const responderNao = () => {
  respostaSim.value = false
  mostrarResposta.value = true
}

const fecharModal = () => {
  mostrarResposta.value = false
}
</script>

<style scoped>
@keyframes float-hearts {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.animate-float-hearts {
  animation: float-hearts 8s infinite linear;
}
</style>

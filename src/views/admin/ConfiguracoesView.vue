<template>
  <div class="space-y-6">
    <h1 class="text-3xl font-bold text-primary">Configurações</h1>

    <!-- Configurações do Pedido -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Configurações do Pedido de Namoro</h2>
        
        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Mensagem do Pedido</span>
          </label>
          <textarea 
            v-model="configuracoes.mensagemPedido" 
            class="textarea textarea-bordered h-32"
            placeholder="Digite a mensagem especial para o pedido..."
          ></textarea>
        </div>

        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Data de Início do Namoro (para cronômetro)</span>
          </label>
          <input 
            v-model="configuracoes.dataInicioNamoro" 
            type="datetime-local" 
            class="input input-bordered w-full"
          />
        </div>

        <div class="form-control mt-4">
          <label class="label cursor-pointer">
            <span class="label-text">Permitir pular enigmas</span>
            <input 
              v-model="configuracoes.permitirPular" 
              type="checkbox" 
              class="checkbox checkbox-primary"
            />
          </label>
        </div>

        <div class="form-control mt-4">
          <label class="label cursor-pointer">
            <span class="label-text">Mostrar dicas automaticamente</span>
            <input 
              v-model="configuracoes.mostrarDicas" 
              type="checkbox" 
              class="checkbox checkbox-primary"
            />
          </label>
        </div>
      </div>
    </div>

    <!-- Configurações Visuais -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Configurações Visuais</h2>
        
        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Tema Principal</span>
          </label>
          <select v-model="configuracoes.tema" class="select select-bordered w-full">
            <option value="valentine">Valentine (Rosa/Vermelho)</option>
            <option value="cupcake">Cupcake (Rosa Claro)</option>
            <option value="pastel">Pastel (Cores Suaves)</option>
            <option value="fantasy">Fantasy (Roxo/Rosa)</option>
            <option value="garden">Garden (Verde/Rosa)</option>
          </select>
        </div>

        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Cor de Destaque</span>
          </label>
          <input 
            v-model="configuracoes.corDestaque" 
            type="color" 
            class="input input-bordered w-full h-12"
          />
        </div>
      </div>
    </div>

    <!-- Configurações de Notificação -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Notificações</h2>
        
        <div class="form-control mt-4">
          <label class="label cursor-pointer">
            <span class="label-text">Enviar email quando enigma for resolvido</span>
            <input 
              v-model="configuracoes.emailEnigmaResolvido" 
              type="checkbox" 
              class="checkbox checkbox-primary"
            />
          </label>
        </div>

        <div class="form-control w-full mt-4" v-if="configuracoes.emailEnigmaResolvido">
          <label class="label">
            <span class="label-text">Email para notificações</span>
          </label>
          <input 
            v-model="configuracoes.emailNotificacao" 
            type="email" 
            class="input input-bordered w-full"
            placeholder="<EMAIL>"
          />
        </div>
      </div>
    </div>

    <!-- Botões de Ação -->
    <div class="flex gap-4">
      <button @click="salvarConfiguracoes" class="btn btn-primary">
        💾 Salvar Configurações
      </button>
      <button @click="resetarConfiguracoes" class="btn btn-warning">
        🔄 Resetar para Padrão
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

const configuracoes = reactive({
  mensagemPedido: 'Você quer namorar comigo? 💖',
  dataInicioNamoro: '',
  permitirPular: false,
  mostrarDicas: true,
  tema: 'valentine',
  corDestaque: '#ff69b4',
  emailEnigmaResolvido: false,
  emailNotificacao: ''
})

const salvarConfiguracoes = () => {
  // Aqui você salvaria as configurações no localStorage ou backend
  localStorage.setItem('chooseme-config', JSON.stringify(configuracoes))
  alert('Configurações salvas com sucesso!')
}

const resetarConfiguracoes = () => {
  if (confirm('Tem certeza que deseja resetar todas as configurações?')) {
    Object.assign(configuracoes, {
      mensagemPedido: 'Você quer namorar comigo? 💖',
      dataInicioNamoro: '',
      permitirPular: false,
      mostrarDicas: true,
      tema: 'valentine',
      corDestaque: '#ff69b4',
      emailEnigmaResolvido: false,
      emailNotificacao: ''
    })
  }
}

onMounted(() => {
  // Carregar configurações salvas
  const configSalva = localStorage.getItem('chooseme-config')
  if (configSalva) {
    Object.assign(configuracoes, JSON.parse(configSalva))
  }
})
</script>

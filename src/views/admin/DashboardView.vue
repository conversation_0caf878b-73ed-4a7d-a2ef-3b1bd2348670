<template>
  <div class="space-y-6">
    <div class="text-center">
      <h1 class="text-4xl font-bold text-primary mb-2">
        Dashboard Administrativo
      </h1>
      <p class="text-lg text-base-content/70">Gerencie sua jornada de amor</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="stat bg-primary text-primary-content rounded-lg">
        <div class="stat-figure">
          <div class="text-3xl">🧩</div>
        </div>
        <div class="stat-title text-primary-content/70">Total de Enigmas</div>
        <div class="stat-value">{{ totalEnigmas }}</div>
        <div class="stat-desc text-primary-content/70">Criados no sistema</div>
      </div>

      <div class="stat bg-secondary text-secondary-content rounded-lg">
        <div class="stat-figure">
          <div class="text-3xl">🔑</div>
        </div>
        <div class="stat-title text-secondary-content/70">Cha<PERSON></div>
        <div class="stat-value">{{ chavesColetadas }}</div>
        <div class="stat-desc text-secondary-content/70">
          de {{ totalEnigmas }}
        </div>
      </div>

      <div class="stat bg-accent text-accent-content rounded-lg">
        <div class="stat-figure">
          <div class="text-3xl">💖</div>
        </div>
        <div class="stat-title text-accent-content/70">Status</div>
        <div class="stat-value text-sm">{{ statusJornada }}</div>
        <div class="stat-desc text-accent-content/70">Progresso atual</div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Ações Rápidas</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <router-link to="/admin/enigmas" class="btn btn-primary">
            🧩 Gerenciar Enigmas
          </router-link>
          <router-link to="/admin/configuracoes" class="btn btn-secondary">
            ⚙️ Configurações
          </router-link>
          <button @click="resetarProgresso" class="btn btn-warning">
            🔄 Resetar Progresso
          </button>
          <router-link to="/love" class="btn btn-accent">
            👀 Ver como Namorada
          </router-link>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Atividade Recente</h2>
        <div class="space-y-2">
          <div
            v-for="atividade in atividadeRecente"
            :key="atividade.id"
            class="alert alert-info"
          >
            <div class="text-sm">
              <strong>{{ atividade.data }}</strong> - {{ atividade.descricao }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// Estados reativos
const totalEnigmas = ref(0);
const chavesColetadas = ref(0);

const statusJornada = computed(() => {
  if (chavesColetadas.value === 0) return 'Não iniciada';
  if (chavesColetadas.value === totalEnigmas.value) return 'Completa!';
  return 'Em progresso';
});

const atividadeRecente = ref([
  {
    id: 1,
    data: '14/06/2025',
    descricao: 'Sistema inicializado',
  },
]);

const resetarProgresso = () => {
  if (confirm('Tem certeza que deseja resetar todo o progresso?')) {
    chavesColetadas.value = 0;
    // Aqui você implementaria a lógica de reset
    alert('Progresso resetado!');
  }
};
</script>

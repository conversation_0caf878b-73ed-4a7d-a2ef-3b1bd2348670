<template>
  <div class="space-y-6">
    <div class="flex justify-between items-center">
      <h1 class="text-3xl font-bold text-primary">Gerenciar Enigmas</h1>
      <button @click="abrirModalNovoEnigma" class="btn btn-primary">
        ➕ Novo Enigma
      </button>
    </div>

    <!-- Lista de Enigmas -->
    <div class="grid gap-4">
      <div v-for="enigma in enigmas" :key="enigma.id" class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <h2 class="card-title">{{ enigma.titulo }}</h2>
              <p class="text-base-content/70 mb-2">{{ enigma.pergunta }}</p>
              <div class="badge badge-outline">Resposta: {{ enigma.resposta }}</div>
            </div>
            <div class="flex gap-2">
              <button @click="editarEnigma(enigma)" class="btn btn-sm btn-secondary">
                ✏️ Editar
              </button>
              <button @click="excluirEnigma(enigma.id)" class="btn btn-sm btn-error">
                🗑️ Excluir
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal para Novo/Editar Enigma -->
    <dialog ref="modalEnigma" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg">{{ editandoEnigma ? 'Editar' : 'Novo' }} Enigma</h3>
        
        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Título do Enigma</span>
          </label>
          <input v-model="formularioEnigma.titulo" type="text" class="input input-bordered w-full" />
        </div>

        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Pergunta/Enigma</span>
          </label>
          <textarea v-model="formularioEnigma.pergunta" class="textarea textarea-bordered h-24"></textarea>
        </div>

        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Resposta Correta</span>
          </label>
          <input v-model="formularioEnigma.resposta" type="text" class="input input-bordered w-full" />
        </div>

        <div class="form-control w-full mt-4">
          <label class="label">
            <span class="label-text">Dica (opcional)</span>
          </label>
          <input v-model="formularioEnigma.dica" type="text" class="input input-bordered w-full" />
        </div>

        <div class="modal-action">
          <button @click="salvarEnigma" class="btn btn-primary">Salvar</button>
          <button @click="fecharModal" class="btn">Cancelar</button>
        </div>
      </div>
    </dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

interface Enigma {
  id: number
  titulo: string
  pergunta: string
  resposta: string
  dica?: string
}

const modalEnigma = ref<HTMLDialogElement>()
const editandoEnigma = ref(false)
const enigmaEditandoId = ref<number | null>(null)

const enigmas = ref<Enigma[]>([
  {
    id: 1,
    titulo: 'Nosso Primeiro Encontro',
    pergunta: 'Em que lugar nos conhecemos pela primeira vez?',
    resposta: 'faculdade',
    dica: 'Um lugar de aprendizado...'
  }
])

const formularioEnigma = reactive({
  titulo: '',
  pergunta: '',
  resposta: '',
  dica: ''
})

const abrirModalNovoEnigma = () => {
  editandoEnigma.value = false
  enigmaEditandoId.value = null
  Object.assign(formularioEnigma, {
    titulo: '',
    pergunta: '',
    resposta: '',
    dica: ''
  })
  modalEnigma.value?.showModal()
}

const editarEnigma = (enigma: Enigma) => {
  editandoEnigma.value = true
  enigmaEditandoId.value = enigma.id
  Object.assign(formularioEnigma, enigma)
  modalEnigma.value?.showModal()
}

const salvarEnigma = () => {
  if (editandoEnigma.value && enigmaEditandoId.value) {
    // Editar enigma existente
    const index = enigmas.value.findIndex(e => e.id === enigmaEditandoId.value)
    if (index !== -1) {
      enigmas.value[index] = { ...formularioEnigma, id: enigmaEditandoId.value }
    }
  } else {
    // Criar novo enigma
    const novoId = Math.max(...enigmas.value.map(e => e.id), 0) + 1
    enigmas.value.push({ ...formularioEnigma, id: novoId })
  }
  fecharModal()
}

const excluirEnigma = (id: number) => {
  if (confirm('Tem certeza que deseja excluir este enigma?')) {
    enigmas.value = enigmas.value.filter(e => e.id !== id)
  }
}

const fecharModal = () => {
  modalEnigma.value?.close()
}
</script>
